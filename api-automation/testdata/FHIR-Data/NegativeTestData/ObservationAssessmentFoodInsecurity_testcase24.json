{"resourceType": "Bundle", "id": "ObservationAssessmentFoodInsecurityTestcase24", "meta": {"lastUpdated": "2024-02-23T00:00:00Z", "profile": ["http://test.shinny.org/us/ny/hrsn/StructureDefinition/SHINNYBundleProfile"]}, "type": "transaction", "timestamp": "2024-02-23T00:00:00Z", "entry": [{"fullUrl": "http://test.shinny.org/us/ny/hrsn/Patient/PatientExample", "resource": {"resourceType": "Patient", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "meta": {"lastUpdated": "2024-02-23T00:00:00.00Z", "profile": ["http://test.shinny.org/us/ny/hrsn/StructureDefinition/shinny-patient"]}, "language": "en", "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\" xml:lang=\"en\" lang=\"en\"><a name=\"Patient_PatientExample\"> </a><p class=\"res-header-id\"><b>Generated Narrative: Patient PatientExample</b></p><a name=\"PatientExample\"> </a><a name=\"hcPatientExample\"> </a><div style=\"display: inline-block; background-color: #d9e0e7; padding: 6px; margin: 4px; border: 1px solid #8da1b4; border-radius: 5px; line-height: 60%\"><p style=\"margin-bottom: 0px\">Last updated: 2024-02-23 00:00:00+0000; Language: en</p><p style=\"margin-bottom: 0px\">Profile: <a href=\"StructureDefinition-shinny-patient.html\">SHINNY Patient Profile</a></p></div><p style=\"border: 1px #661aff solid; background-color: #e6e6ff; padding: 10px;\"><PERSON>, DoB: 1981-07-16 ( Medical record number)</p><hr/><table class=\"grid\"><tr><td style=\"background-color: #f3f5da\" title=\"Other Ids (see the one above)\">Other Ids:</td><td colspan=\"3\"><ul><li>Patient Medicaid number/AA12345C</li><li>Social Security Number/***********</li></ul></td></tr><tr><td style=\"background-color: #f3f5da\" title=\"Ways to contact the Patient\">Contact Detail</td><td colspan=\"3\"><ul><li>ph: ************(Home)</li><li>115 Broadway Apt2 New York, NY 10032</li></ul></td></tr><tr><td style=\"background-color: #f3f5da\" title=\"Language spoken\">Language:</td><td colspan=\"3\"><span title=\"Codes:{urn:ietf:bcp:47 en}\">English</span> (preferred)</td></tr><tr><td style=\"background-color: #f3f5da\" title=\"Nominated Contact: Mother\">Mother:</td><td colspan=\"3\"><ul><li>Joyce Doe </li><li><a href=\"tel:+**********\">+**********</a></li></ul></td></tr><tr><td style=\"background-color: #f3f5da\" title=\"Personal Pronouns\"><a href=\"StructureDefinition-shinny-personal-pronouns.html\">SHINNY Personal Pronouns</a></td><td colspan=\"3\"><span title=\"Codes:{http://loinc.org LA29518-0}\">he/him/his/his/himself</span></td></tr><tr><td style=\"background-color: #f3f5da\" title=\"This extension represents an individual's sense of being a man, woman, boy, girl, nonbinary, or something else, ascertained by asking them what that identity is.\"><a href=\"StructureDefinition-shinny-gender-identity.html\">SHINNYGenderIdentityExtension</a></td><td colspan=\"3\"><span title=\"Codes:{http://snomed.info/sct 446151000124109}\">Identifies as male gender (finding)</span></td></tr><tr><td style=\"background-color: #f3f5da\" title=\"Concepts classifying the person into a named category of humans sharing common history, traits, geographical origin or nationality.  The ethnicity codes used to represent these concepts are based upon the [CDC ethnicity and Ethnicity Code Set Version 1.0](http://www.cdc.gov/phin/resources/vocabulary/index.html) which includes over 900 concepts for representing race and ethnicity of which 43 reference ethnicity.  The ethnicity concepts are grouped by and pre-mapped to the 2 OMB ethnicity categories: - Hispanic or Latino - Not Hispanic or Latino.\">US Core Ethnicity Extension:</td><td colspan=\"3\"><ul><li>ombCategory: <a href=\"http://hl7.org/fhir/us/core/STU3.1.1/CodeSystem-cdcrec.html#cdcrec-2135-2\">Race &amp; Ethnicity - CDC 2135-2</a>: Hispanic or Latino</li><li>text: Hispanic or Latino</li></ul></td></tr><tr><td style=\"background-color: #f3f5da\" title=\"A code classifying the person's sex assigned at birth  as specified by the [Office of the National Coordinator for Health IT (ONC)](https://www.healthit.gov/newsroom/about-onc). This extension aligns with the C-CDA Birth Sex Observation (LOINC 76689-9).\"><a href=\"http://hl7.org/fhir/us/core/STU3.1.1/StructureDefinition-us-core-birthsex.html\">US Core Birth Sex Extension</a></td><td colspan=\"3\">M</td></tr><tr><td style=\"background-color: #f3f5da\" title=\"Concepts classifying the person into a named category of humans sharing common history, traits, geographical origin or nationality.  The race codes used to represent these concepts are based upon the [CDC Race and Ethnicity Code Set Version 1.0](http://www.cdc.gov/phin/resources/vocabulary/index.html) which includes over 900 concepts for representing race and ethnicity of which 921 reference race.  The race concepts are grouped by and pre-mapped to the 5 OMB race categories:\n\n - American Indian or Alaska Native\n - Asian\n - Black or African American\n - Native Hawaiian or Other Pacific Islander\n - White.\">US Core Race Extension:</td><td colspan=\"3\"><ul><li>ombCategory: <a href=\"http://hl7.org/fhir/us/core/STU3.1.1/CodeSystem-cdcrec.html#cdcrec-2028-9\">Race &amp; Ethnicity - CDC 2028-9</a>: Asian</li><li>ombCategory: <a href=\"http://hl7.org/fhir/us/core/STU3.1.1/CodeSystem-cdcrec.html#cdcrec-1002-5\">Race &amp; Ethnicity - CDC 1002-5</a>: American Indian or Alaska Native</li><li>detailed: <a href=\"http://hl7.org/fhir/us/core/STU3.1.1/CodeSystem-cdcrec.html#cdcrec-1010-8\">Race &amp; Ethnicity - CDC 1010-8</a>: Apache</li><li>text: Asian, American Indian, Apache</li></ul></td></tr></table></div>"}, "extension": [{"extension": [{"url": "ombCategory", "valueCoding": {"system": "urn:oid:2.16.840.1.113883.6.238", "code": "2028-9", "display": "Asian"}}, {"url": "ombCategory", "valueCoding": {"system": "urn:oid:2.16.840.1.113883.6.238", "code": "1002-5", "display": "American Indian or Alaska Native"}}, {"url": "detailed", "valueCoding": {"system": "urn:oid:2.16.840.1.113883.6.238", "code": "1010-8", "display": "Apache"}}, {"url": "text", "valueString": "Asian, American Indian, Apache"}], "url": "http://hl7.org/fhir/us/core/StructureDefinition/us-core-race"}, {"extension": [{"url": "ombCategory", "valueCoding": {"system": "urn:oid:2.16.840.1.113883.6.238", "code": "2135-2", "display": "Hispanic or Latino"}}, {"url": "text", "valueString": "Hispanic or Latino"}], "url": "http://hl7.org/fhir/us/core/StructureDefinition/us-core-ethnicity"}, {"url": "http://hl7.org/fhir/us/core/StructureDefinition/us-core-birthsex", "valueCode": "M"}, {"url": "http://test.shinny.org/us/ny/hrsn/StructureDefinition/shinny-personal-pronouns", "valueCodeableConcept": {"coding": [{"system": "http://loinc.org", "code": "LA29518-0", "display": "he/him/his/his/himself"}]}}, {"url": "http://test.shinny.org/us/ny/hrsn/StructureDefinition/shinny-gender-identity", "valueCodeableConcept": {"coding": [{"system": "http://snomed.info/sct", "code": "446151000124109", "display": "Identifies as male gender (finding)"}]}}], "identifier": [{"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MR"}]}, "system": "http://www.scn.gov/facility/CUMC", "value": "11223344", "assigner": {"reference": "Organization/OrganizationExampleOther-SCN1"}}, {"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MA"}]}, "system": "http://www.medicaid.gov/", "value": "AA12345C"}, {"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "SS", "display": "Social Security Number"}], "text": "Social Security Number"}, "system": "http://www.ssa.gov/", "value": "***********"}], "name": [{"extension": [{"url": "http://test.shinny.org/us/ny/hrsn/StructureDefinition/middle-name", "valueString": "<PERSON>"}], "family": "<PERSON><PERSON>", "given": ["<PERSON>"], "prefix": ["Mr.", "Dr.", "PhD", "CCNA"], "suffix": ["Jr.", "III"]}], "telecom": [{"system": "phone", "value": "************", "use": "home"}], "gender": "male", "birthDate": "1981-07-16", "address": [{"text": "115 Broadway Apt2 New York, NY 10032", "line": ["115 Broadway Apt2"], "city": "New York", "district": "MANHATTAN", "state": "NY", "postalCode": "10032"}], "contact": [{"relationship": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0063", "code": "MTH", "display": "Mother"}]}], "name": {"family": "<PERSON><PERSON>", "given": ["<PERSON>"]}, "telecom": [{"system": "phone", "value": "+**********"}]}], "communication": [{"language": {"coding": [{"system": "urn:ietf:bcp:47", "code": "en"}]}, "preferred": true}]}, "request": {"method": "POST", "url": "http://test.shinny.org/us/ny/hrsn/Patient/PatientExample"}}, {"fullUrl": "http://test.shinny.org/us/ny/hrsn/Consent/ConsentExample", "resource": {"resourceType": "Consent", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "meta": {"lastUpdated": "2024-02-23T00:00:00Z", "profile": ["http://test.shinny.org/us/ny/hrsn/StructureDefinition/shinny-Consent"]}, "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><a name=\"Consent_ConsentExample\"> </a><p class=\"res-header-id\"><b>Generated Narrative: <PERSON>sent ConsentExample</b></p><a name=\"ConsentExample\"> </a><a name=\"hcConsentExample\"> </a><div style=\"display: inline-block; background-color: #d9e0e7; padding: 6px; margin: 4px; border: 1px solid #8da1b4; border-radius: 5px; line-height: 60%\"><p style=\"margin-bottom: 0px\">Last updated: 2024-02-23 00:00:00+0000</p><p style=\"margin-bottom: 0px\">Profile: <a href=\"StructureDefinition-shinny-Consent.html\">SHINNY Consent Profile</a></p></div><p><b>status</b>: Active</p><p><b>scope</b>: <span title=\"Codes:{http://terminology.hl7.org/CodeSystem/consentscope treatment}\">treatment</span></p><p><b>category</b>: <span title=\"Codes:{http://loinc.org 59284-0}\">Consent Document</span>, <span title=\"Codes:{http://terminology.hl7.org/CodeSystem/v3-ActCode IDSCL}\">information disclosure</span></p><p><b>patient</b>: <a href=\"Patient-PatientExample.html\">Jon Doe  Male, DoB: 1981-07-16 ( Medical record number)</a></p><p><b>dateTime</b>: 2024-02-23 00:00:00+0000</p><p><b>organization</b>: <a href=\"Organization-OrganizationExampleOther-SCN1.html\">Organization Care Ridge SCN</a></p><p><b>source</b>: </p><h3>Policies</h3><table class=\"grid\"><tr><td style=\"display: none\">-</td><td><b>Authority</b></td></tr><tr><td style=\"display: none\">*</td><td>urn:uuid:d1eaac1a-22b7-4bb6-9c62-cc95d6fdf1a5</td></tr></table><h3>Provisions</h3><table class=\"grid\"><tr><td style=\"display: none\">-</td><td><b>Type</b></td></tr><tr><td style=\"display: none\">*</td><td>Opt In</td></tr></table></div>"}, "status": "active", "scope": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/consentscope", "code": "treatment", "display": "Treatment"}], "text": "treatment"}, "category": [{"coding": [{"system": "http://loinc.org", "code": "59284-0", "display": "Consent Document"}]}, {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v3-ActCode", "code": "IDSCL"}]}], "patient": {"reference": "Patient/PatientExample"}, "dateTime": "2024-02-23T00:00:00Z", "organization": [{"reference": "Organization/OrganizationExampleOther-SCN1"}], "sourceAttachment": {"contentType": "application/pdf", "language": "en"}, "policy": [{"authority": "urn:uuid:d1eaac1a-22b7-4bb6-9c62-cc95d6fdf1a5"}], "provision": {"type": "permit"}}, "request": {"method": "POST", "url": "http://test.shinny.org/us/ny/hrsn/Consent/ConsentExample"}}, {"fullUrl": "http://test.shinny.org/us/ny/hrsn/Observation/SexualOrientationExample", "resource": {"resourceType": "Observation", "id": "SexualOrientationExample", "meta": {"lastUpdated": "2024-02-23T00:00:00Z", "profile": ["http://test.shinny.org/us/ny/hrsn/StructureDefinition/shinny-observation-sexual-orientation"]}, "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><a name=\"Observation_SexualOrientationExample\"> </a><p class=\"res-header-id\"><b>Generated Narrative: Observation SexualOrientationExample</b></p><a name=\"SexualOrientationExample\"> </a><a name=\"hcSexualOrientationExample\"> </a><div style=\"display: inline-block; background-color: #d9e0e7; padding: 6px; margin: 4px; border: 1px solid #8da1b4; border-radius: 5px; line-height: 60%\"><p style=\"margin-bottom: 0px\">Last updated: 2024-02-23 00:00:00+0000</p><p style=\"margin-bottom: 0px\">Profile: <a href=\"StructureDefinition-shinny-observation-sexual-orientation.html\">SHINNYObservationSexualOrientationProfile</a></p></div><p><b>status</b>: Final</p><p><b>code</b>: <span title=\"Codes:{http://loinc.org 76690-7}\">Sexual orientation</span></p><p><b>subject</b>: <a href=\"Patient-PatientExample.html\">Jon Doe  Male, DoB: 1981-07-16 ( Medical record number)</a></p><p><b>value</b>: <span title=\"Codes:{http://terminology.hl7.org/CodeSystem/v3-NullFlavor UNK}\">Unknown</span></p></div>"}, "status": "final", "code": {"coding": [{"system": "http://loinc.org", "code": "76690-7", "display": "Sexual orientation"}]}, "subject": {"reference": "Patient/PatientExample"}, "valueCodeableConcept": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v3-NullFlavor", "code": "UNK", "display": "Unknown"}]}}, "request": {"method": "POST", "url": "http://test.shinny.org/us/ny/hrsn/Observation/SexualOrientationExample"}}, {"fullUrl": "http://test.shinny.org/us/ny/hrsn/Encounter/EncounterExample", "resource": {"resourceType": "Encounter", "id": "EncounterExample", "meta": {"lastUpdated": "2024-02-23T00:00:00Z", "profile": ["http://test.shinny.org/us/ny/hrsn/StructureDefinition/shinny-encounter"]}, "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><a name=\"Encounter_EncounterExample\"> </a><p class=\"res-header-id\"><b>Generated Narrative: Encounter EncounterExample</b></p><a name=\"EncounterExample\"> </a><a name=\"hcEncounterExample\"> </a><div style=\"display: inline-block; background-color: #d9e0e7; padding: 6px; margin: 4px; border: 1px solid #8da1b4; border-radius: 5px; line-height: 60%\"><p style=\"margin-bottom: 0px\">Last updated: 2024-02-23 00:00:00+0000</p><p style=\"margin-bottom: 0px\">Profile: <a href=\"StructureDefinition-shinny-encounter.html\">SHINNY Encounter Profile</a></p></div><p><b>status</b>: Finished</p><p><b>class</b>: <a href=\"http://terminology.hl7.org/6.0.2/CodeSystem-v3-ActCode.html#v3-ActCode-FLD\">ActCode FLD</a>: field</p><p><b>type</b>: <span title=\"Codes:{http://snomed.info/sct 405672008}\">Direct questioning (procedure)</span></p><p><b>subject</b>: <a href=\"Patient-PatientExample.html\">Jon Doe  Male, DoB: 1981-07-16 ( Medical record number)</a></p><p><b>period</b>: 2024-02-23 00:00:00+0000 --&gt; 2024-02-23 01:00:00+0000</p><h3>Locations</h3><table class=\"grid\"><tr><td style=\"display: none\">-</td><td><b>Location</b></td></tr><tr><td style=\"display: none\">*</td><td><a href=\"Location-LocationExample-SCN.html\">Location downtown location</a></td></tr></table></div>"}, "status": "finished", "class": {"system": "http://terminology.hl7.org/CodeSystem/v3-ActCode", "code": "FLD"}, "type": [{"coding": [{"system": "http://snomed.info/sct", "code": "405672008", "display": "Direct questioning (procedure)"}], "text": "Direct questioning (procedure)"}], "subject": {"reference": "Patient/PatientExample"}, "period": {"start": "2024-02-23T00:00:00Z", "end": "2024-02-23T01:00:00Z"}, "location": [{"location": {"reference": "Location/LocationExample-SCN"}}]}, "request": {"method": "POST", "url": "http://test.shinny.org/us/ny/hrsn/Encounter/EncounterExample"}}, {"fullUrl": "http://test.shinny.org/us/ny/hrsn/Observation/AssessmentFoodInsecurityExample", "resource": {"resourceType": "Observation", "id": "AssessmentFoodInsecurityExample", "meta": {"lastUpdated": "2024-02-23T00:00:00Z", "profile": ["http://test.shinny.org/us/ny/hrsn/StructureDefinition/shinny-observation-assessment"]}, "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><a name=\"Observation_AssessmentFoodInsecurityExample\"> </a><p class=\"res-header-id\"><b>Generated Narrative: Observation AssessmentFoodInsecurityExample</b></p><a name=\"AssessmentFoodInsecurityExample\"> </a><a name=\"hcAssessmentFoodInsecurityExample\"> </a><div style=\"display: inline-block; background-color: #d9e0e7; padding: 6px; margin: 4px; border: 1px solid #8da1b4; border-radius: 5px; line-height: 60%\"><p style=\"margin-bottom: 0px\">Last updated: 2024-02-23 00:00:00+0000</p><p style=\"margin-bottom: 0px\">Profile: <a href=\"StructureDefinition-shinny-observation-assessment.html\">SHINNY Observation Assessment Profile</a></p></div><p><b>status</b>: Final</p><p><b>category</b>: <span title=\"Codes:{http://hl7.org/fhir/us/sdoh-clinicalcare/CodeSystem/SDOHCC-CodeSystemTemporaryCodes food-insecurity}\">Food Insecurity</span>, <span title=\"Codes:{http://terminology.hl7.org/CodeSystem/observation-category social-history}\">Social History</span></p><p><b>code</b>: <span title=\"Codes:{http://snomed.info/sct 699653008}\">Inadequate oral food intake for physiological needs</span></p><p><b>subject</b>: <a href=\"Patient-PatientExample.html\">Jon Doe  Male, DoB: 1981-07-16 ( Medical record number)</a></p><p><b>encounter</b>: <a href=\"Encounter-EncounterExample.html\">Encounter: status = finished; class = field (ActCode#FLD); type = Direct questioning (procedure); period = 2024-02-23 00:00:00+0000 --&gt; 2024-02-23 01:00:00+0000</a></p><p><b>effective</b>: 2024-05-10 21:56:54+0000</p><p><b>performer</b>: <a href=\"Organization-OrganizationExampleOther-SCN1.html\">Organization Care Ridge SCN</a></p><p><b>value</b>: true</p><p><b>derivedFrom</b>: <a href=\"Observation-ObservationResponseFoodInsecurity88122-7.html\">Observation Within the past 12 months we worried whether our food would run out before we got money to buy more [U.S. FSS]</a></p></div>"}, "status": "final", "category": [{"coding": [{"system": "http://hl7.org/fhir/us/sdoh-clinicalcare/CodeSystem/SDOHCC-CodeSystemTemporaryCodes", "code": "food-insecurity", "display": "Food Insecurity"}]}, {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "social-history"}]}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "699653008", "display": "Inadequate oral food intake for physiological needs"}]}, "subject": {"reference": "Patient/PatientExample"}, "encounter": {"reference": "Encounter/EncounterExample"}, "effectiveDateTime": "2024-05-10T21:56:54.671Z", "valueBoolean": true, "derivedFrom": [{"reference": "Observation/ObservationResponseFoodInsecurity88122-7"}]}, "request": {"method": "POST", "url": "http://test.shinny.org/us/ny/hrsn/Observation/AssessmentFoodInsecurityExample"}}, {"fullUrl": "http://test.shinny.org/us/ny/hrsn/Observation/ObservationResponseFoodInsecurity88122-7", "resource": {"resourceType": "Observation", "id": "ObservationResponseFoodInsecurity88122-7", "meta": {"lastUpdated": "2024-02-23T00:00:00Z", "profile": ["http://test.shinny.org/us/ny/hrsn/StructureDefinition/shinny-observation-screening-response"]}, "language": "en", "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\" xml:lang=\"en\" lang=\"en\"><a name=\"Observation_ObservationResponseFoodInsecurity88122-7\"> </a><p class=\"res-header-id\"><b>Generated Narrative: Observation ObservationResponseFoodInsecurity88122-7</b></p><a name=\"ObservationResponseFoodInsecurity88122-7\"> </a><a name=\"hcObservationResponseFoodInsecurity88122-7\"> </a><div style=\"display: inline-block; background-color: #d9e0e7; padding: 6px; margin: 4px; border: 1px solid #8da1b4; border-radius: 5px; line-height: 60%\"><p style=\"margin-bottom: 0px\">Last updated: 2024-02-23 00:00:00+0000; Language: en</p><p style=\"margin-bottom: 0px\">Profile: <a href=\"StructureDefinition-shinny-observation-screening-response.html\">SHINNY Observation Screening Response Profile</a></p></div><p><b>status</b>: Final</p><p><b>category</b>: <span title=\"Codes:{http://hl7.org/fhir/us/sdoh-clinicalcare/CodeSystem/SDOHCC-CodeSystemTemporaryCodes food-insecurity}\">Food Insecurity</span>, <span title=\"Codes:{http://terminology.hl7.org/CodeSystem/observation-category social-history}\">Social History</span>, <span title=\"Codes:{http://terminology.hl7.org/CodeSystem/observation-category survey}\">Survey</span></p><p><b>code</b>: <span title=\"Codes:{http://loinc.org 88122-7}\">Within the past 12 months, you worried that your food would run out before you got money to buy more.</span></p><p><b>subject</b>: <a href=\"Patient-PatientExample.html\">Jon Doe  Male, DoB: 1981-07-16 ( Medical record number)</a></p><p><b>encounter</b>: <a href=\"Encounter-EncounterExample.html\">Encounter: status = finished; class = field (ActCode#FLD); type = Direct questioning (procedure); period = 2024-02-23 00:00:00+0000 --&gt; 2024-02-23 01:00:00+0000</a></p><p><b>effective</b>: 2023-07-12 16:08:00+0000</p><p><b>issued</b>: 2023-07-12 16:08:00+0000</p><p><b>value</b>: <span title=\"Codes:{http://loinc.org LA28397-0}\">Often true</span></p><p><b>interpretation</b>: <span title=\"Codes:{http://terminology.hl7.org/CodeSystem/v3-ObservationInterpretation POS}\">Positive</span></p></div>"}, "status": "final", "category": [{"coding": [{"system": "http://hl7.org/fhir/us/sdoh-clinicalcare/CodeSystem/SDOHCC-CodeSystemTemporaryCodes", "code": "food-insecurity", "display": "Food Insecurity"}]}, {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "social-history"}]}, {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "survey"}]}], "code": {"coding": [{"system": "http://loinc.org", "code": "88122-7", "display": "Within the past 12 months we worried whether our food would run out before we got money to buy more [U.S. FSS]"}], "text": "Within the past 12 months, you worried that your food would run out before you got money to buy more."}, "subject": {"reference": "Patient/PatientExample"}, "encounter": {"reference": "Encounter/EncounterExample"}, "effectiveDateTime": "2023-07-12T16:08:00.000Z", "issued": "2023-07-12T16:08:00.000Z", "valueCodeableConcept": {"coding": [{"system": "http://loinc.org", "code": "LA28397-0", "display": "Often true"}], "text": "Often true"}, "interpretation": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v3-ObservationInterpretation", "code": "POS", "display": "Positive"}]}]}, "request": {"method": "POST", "url": "http://test.shinny.org/us/ny/hrsn/Observation/ObservationResponseFoodInsecurity88122-7"}}, {"fullUrl": "http://test.shinny.org/us/ny/hrsn/Condition/ConditionFoodInsecurityExample", "resource": {"resourceType": "Condition", "id": "ConditionFoodInsecurityExample", "meta": {"lastUpdated": "2024-02-23T00:00:00Z", "profile": ["http://test.shinny.org/us/ny/hrsn/StructureDefinition/SHINNY-SDOHCC-Condition"]}, "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><a name=\"Condition_ConditionFoodInsecurityExample\"> </a><p class=\"res-header-id\"><b>Generated Narrative: Condition ConditionFoodInsecurityExample</b></p><a name=\"ConditionFoodInsecurityExample\"> </a><a name=\"hcConditionFoodInsecurityExample\"> </a><div style=\"display: inline-block; background-color: #d9e0e7; padding: 6px; margin: 4px; border: 1px solid #8da1b4; border-radius: 5px; line-height: 60%\"><p style=\"margin-bottom: 0px\">Last updated: 2024-02-23 00:00:00+0000</p><p style=\"margin-bottom: 0px\">Profile: <a href=\"StructureDefinition-SHINNY-SDOHCC-Condition.html\">SHINNY SDOHCC Condition Profile</a></p></div><p><b>clinicalStatus</b>: <span title=\"Codes:{http://terminology.hl7.org/CodeSystem/condition-clinical active}\">Active</span></p><p><b>verificationStatus</b>: <span title=\"Codes:{http://terminology.hl7.org/CodeSystem/condition-ver-status confirmed}\">Confirmed</span></p><p><b>category</b>: <span title=\"Codes:{http://hl7.org/fhir/us/core/CodeSystem/condition-category health-concern}\">Health Concern</span>, <span title=\"Codes:{http://hl7.org/fhir/us/sdoh-clinicalcare/CodeSystem/SDOHCC-CodeSystemTemporaryCodes food-insecurity}\">Food Insecurity</span></p><p><b>code</b>: <span title=\"Codes:{http://hl7.org/fhir/sid/icd-10-cm Z59.4}\">Lack of adequate food</span></p><p><b>subject</b>: <a href=\"Patient-PatientExample.html\">Jon Doe  Male, DoB: 1981-07-16 ( Medical record number)</a></p><p><b>encounter</b>: <a href=\"Encounter-EncounterExample.html\">Encounter: status = finished; class = field (ActCode#FLD); type = Direct questioning (procedure); period = 2024-02-23 00:00:00+0000 --&gt; 2024-02-23 01:00:00+0000</a></p><p><b>onset</b>: 2023-07-12 16:08:00+0000 --&gt; (ongoing)</p><p><b>asserter</b>: <a href=\"Practitioner-PractitionerExampleCBO.html\">Practitioner Bob Kelso MD</a></p><blockquote><p><b>evidence</b></p><p><b>detail</b>: </p><ul><li><a href=\"Observation-AssessmentFoodInsecurityExample.html\">Observation Inadequate oral food intake for physiological needs</a></li><li><a href=\"Observation-ObservationResponseFoodInsecurity88122-7.html\">Observation Within the past 12 months we worried whether our food would run out before we got money to buy more [U.S. FSS]</a></li></ul></blockquote></div>"}, "clinicalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/condition-clinical", "code": "active", "display": "Active"}]}, "verificationStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/condition-ver-status", "code": "confirmed", "display": "Confirmed"}]}, "category": [{"coding": [{"system": "http://hl7.org/fhir/us/core/CodeSystem/condition-category", "code": "health-concern", "display": "Health Concern"}]}, {"coding": [{"system": "http://hl7.org/fhir/us/sdoh-clinicalcare/CodeSystem/SDOHCC-CodeSystemTemporaryCodes", "code": "food-insecurity", "display": "Food Insecurity"}]}], "code": {"coding": [{"system": "http://hl7.org/fhir/sid/icd-10-cm", "code": "Z59.4", "display": "Lack of adequate food"}]}, "subject": {"reference": "Patient/PatientExample"}, "encounter": {"reference": "Encounter/EncounterExample"}, "onsetPeriod": {"start": "2023-07-12T16:08:00.000Z"}, "asserter": {"reference": "Practitioner/PractitionerExampleCBO"}, "evidence": [{"detail": [{"reference": "Observation/AssessmentFoodInsecurityExample"}, {"reference": "Observation/ObservationResponseFoodInsecurity88122-7"}]}]}, "request": {"method": "POST", "url": "http://test.shinny.org/us/ny/hrsn/Condition/ConditionFoodInsecurityExample"}}, {"fullUrl": "http://test.shinny.org/us/ny/hrsn/Practitioner/PractitionerExampleCBO", "resource": {"resourceType": "Practitioner", "id": "PractitionerExampleCBO", "meta": {"lastUpdated": "2024-02-23T00:00:00Z", "profile": ["http://test.shinny.org/us/ny/hrsn/StructureDefinition/shin-ny-practitioner"]}, "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><a name=\"Practitioner_PractitionerExampleCBO\"> </a><p class=\"res-header-id\"><b>Generated Narrative: Practitioner PractitionerExampleCBO</b></p><a name=\"PractitionerExampleCBO\"> </a><a name=\"hcPractitionerExampleCBO\"> </a><div style=\"display: inline-block; background-color: #d9e0e7; padding: 6px; margin: 4px; border: 1px solid #8da1b4; border-radius: 5px; line-height: 60%\"><p style=\"margin-bottom: 0px\">Last updated: 2024-02-23 00:00:00+0000</p><p style=\"margin-bottom: 0px\">Profile: <a href=\"StructureDefinition-shin-ny-practitioner.html\">SHINNY Practitioner Profile</a></p></div><p><b>identifier</b>: National provider identifier/123456789</p><p><b>name</b>: <PERSON></p><p><b>gender</b>: Male</p><p><b>birthDate</b>: 1953-01-01</p></div>"}, "identifier": [{"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "NPI"}]}, "system": "http://hl7.org/fhir/sid/us-npi", "value": "123456789", "assigner": {"reference": "Organization/OrganizationExampleCBO"}}], "name": [{"text": "<PERSON>", "family": "<PERSON><PERSON><PERSON>", "given": ["<PERSON>"], "suffix": ["MD", "CDPAD"]}], "gender": "male", "birthDate": "1953-01-01"}, "request": {"method": "POST", "url": "http://test.shinny.org/us/ny/hrsn/Practitioner/PractitionerExampleCBO"}}, {"fullUrl": "http://test.shinny.org/us/ny/hrsn/Organization/OrganizationExampleOther-SCN1", "resource": {"resourceType": "Organization", "id": "OrganizationExampleOther-SCN1", "meta": {"lastUpdated": "2024-02-23T00:00:00Z", "profile": ["http://test.shinny.org/us/ny/hrsn/StructureDefinition/shin-ny-organization"]}, "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><a name=\"Organization_OrganizationExampleOther-SCN1\"> </a><p class=\"res-header-id\"><b>Generated Narrative: Organization OrganizationExampleOther-SCN1</b></p><a name=\"OrganizationExampleOther-SCN1\"> </a><a name=\"hcOrganizationExampleOther-SCN1\"> </a><div style=\"display: inline-block; background-color: #d9e0e7; padding: 6px; margin: 4px; border: 1px solid #8da1b4; border-radius: 5px; line-height: 60%\"><p style=\"margin-bottom: 0px\">Last updated: 2024-02-23 00:00:00+0000</p><p style=\"margin-bottom: 0px\">Profile: <a href=\"StructureDefinition-shin-ny-organization.html\">SHINNY Organization Profile</a></p></div><p><b>identifier</b>: Care Ridge/SCNExample, NPI/**********</p><p><b>active</b>: true</p><p><b>type</b>: <span title=\"Codes:{https://hl7.org/fhir/R4/codesystem-organization-type.html other}\">Other</span></p><p><b>name</b>: Care Ridge SCN</p><p><b>address</b>: 111 Care Ridge St, Plainview, NY 11803</p></div>"}, "identifier": [{"type": {"coding": [{"display": "Care Ridge"}]}, "system": "http://www.scn.ny.gov/", "value": "SCNExample"}, {"type": {"coding": [{"system": "http://hl7.org/fhir/sid/us-npi", "code": "NPI"}]}, "system": "http://hl7.org/fhir/sid/us-npi", "value": "**********"}], "active": true, "type": [{"coding": [{"system": "https://hl7.org/fhir/R4/codesystem-organization-type.html", "code": "other", "display": "Other"}]}], "name": "Care Ridge SCN", "address": [{"text": "111 Care Ridge St, Plainview, NY 11803", "line": ["111 Care Ridge St"], "city": "Plainview", "district": "Nassau County", "state": "NY", "postalCode": "11803"}]}, "request": {"method": "POST", "url": "http://test.shinny.org/us/ny/hrsn/Organization/OrganizationExampleOther-SCN1"}}, {"fullUrl": "http://test.shinny.org/us/ny/hrsn/Location/LocationExample-SCN", "resource": {"resourceType": "Location", "id": "LocationExample-SCN", "meta": {"lastUpdated": "2024-02-23T00:00:00Z", "profile": ["http://hl7.org/fhir/us/sdoh-clinicalcare/StructureDefinition/SDOHCC-Location"]}, "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><a name=\"Location_LocationExample-SCN\"> </a><p class=\"res-header-id\"><b>Generated Narrative: Location LocationExample-SCN</b></p><a name=\"LocationExample-SCN\"> </a><a name=\"hcLocationExample-SCN\"> </a><div style=\"display: inline-block; background-color: #d9e0e7; padding: 6px; margin: 4px; border: 1px solid #8da1b4; border-radius: 5px; line-height: 60%\"><p style=\"margin-bottom: 0px\">Last updated: 2024-02-23 00:00:00+0000</p><p style=\"margin-bottom: 0px\">Profile: <a href=\"http://hl7.org/fhir/us/sdoh-clinicalcare/STU2.2/StructureDefinition-SDOHCC-Location.html\">SDOHCC Location</a></p></div><p><b>status</b>: Active</p><p><b>name</b>: downtown location</p><p><b>type</b>: <span title=\"Codes:{http://terminology.hl7.org/CodeSystem/v3-RoleCode CSC}\">community service center</span></p><p><b>address</b>: 115 Broadway Suite #1601 New York NY 10006 </p><p><b>physicalType</b>: <span title=\"Codes:{http://terminology.hl7.org/CodeSystem/location-physical-type bu}\">Building</span></p><p><b>managingOrganization</b>: <a href=\"Organization-OrganizationExampleOther-SCN1.html\">Organization Care Ridge SCN</a></p></div>"}, "status": "active", "name": "downtown location", "type": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v3-RoleCode", "code": "CSC"}]}], "address": {"id": "SCNAddressLocation", "type": "physical", "line": ["115 Broadway Suite #1601"], "city": "New York", "district": "MANHATTAN", "state": "NY", "postalCode": "10006"}, "physicalType": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/location-physical-type", "code": "bu"}]}, "managingOrganization": {"reference": "Organization/OrganizationExampleOther-SCN1"}}, "request": {"method": "POST", "url": "http://test.shinny.org/us/ny/hrsn/Location/LocationExample-SCN"}}, {"fullUrl": "http://test.shinny.org/us/ny/hrsn/QuestionnaireResponse/SHINNYDisablityQuestionnaireResponse", "resource": {"resourceType": "QuestionnaireResponse", "id": "SHINNYDisablityQuestionnaireResponse", "meta": {"lastUpdated": "2024-02-23T00:00:00Z", "profile": ["http://hl7.org/fhir/uv/sdc/StructureDefinition/sdc-questionnaireresponse"]}, "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><a name=\"QuestionnaireResponse_SHINNYDisablityQuestionnaireResponse\"> </a><p class=\"res-header-id\"><b>Generated Narrative: QuestionnaireResponse SHINNYDisablityQuestionnaireResponse</b></p><a name=\"SHINNYDisablityQuestionnaireResponse\"> </a><a name=\"hcSHINNYDisablityQuestionnaireResponse\"> </a><div style=\"display: inline-block; background-color: #d9e0e7; padding: 6px; margin: 4px; border: 1px solid #8da1b4; border-radius: 5px; line-height: 60%\"><p style=\"margin-bottom: 0px\">Last updated: 2024-02-23 00:00:00+0000</p><p style=\"margin-bottom: 0px\">Profile: <a href=\"http://hl7.org/fhir/uv/sdc/STU3/StructureDefinition-sdc-questionnaireresponse.html\">SDC Questionnaire Response</a></p></div><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\"border: 1px #F0F0F0 solid; font-size: 11px; font-family: verdana; vertical-align: top;\"><tr style=\"border: 2px #F0F0F0 solid; font-size: 11px; font-family: verdana; vertical-align: top\"><th style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: white; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px; padding-top: 3px; padding-bottom: 3px\" class=\"hierarchy\"><a href=\"https://hl7.org/fhir/R4/formats.html#table\" title=\"The linkID for the item\">LinkID</a></th><th style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: white; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px; padding-top: 3px; padding-bottom: 3px\" class=\"hierarchy\"><a href=\"https://hl7.org/fhir/R4/formats.html#table\" title=\"Text for the item\">Text</a></th><th style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: white; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px; padding-top: 3px; padding-bottom: 3px\" class=\"hierarchy\"><a href=\"https://hl7.org/fhir/R4/formats.html#table\" title=\"Minimum and Maximum # of times the item can appear in the instance\">Definition</a></th><th style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: white; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px; padding-top: 3px; padding-bottom: 3px\" class=\"hierarchy\"><a href=\"https://hl7.org/fhir/R4/formats.html#table\" title=\"The type of the item\">Answer</a><span style=\"float: right\"><a href=\"https://hl7.org/fhir/R4/formats.html#table\" title=\"Legend for this format\"><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3goXBCwdPqAP0wAAAldJREFUOMuNk0tIlFEYhp9z/vE2jHkhxXA0zJCMitrUQlq4lnSltEqCFhFG2MJFhIvIFpkEWaTQqjaWZRkp0g26URZkTpbaaOJkDqk10szoODP//7XIMUe0elcfnPd9zsfLOYplGrpRwZaqTtw3K7PtGem7Q6FoidbGgqHVy/HRb669R+56zx7eRV1L31JGxYbBtjKK93cxeqfyQHbehkZbUkK20goELEuIzEd+dHS+qz/Y8PTSif0FnGkbiwcAjHaU1+QWOptFiyCLp/LnKptpqIuXHx6rbR26kJcBX3yLgBfnd7CxwJmflpP2wUg0HIAoUUpZBmKzELGWcN8nAr6Gpu7tLU/CkwAaoKTWRSQyt89Q8w6J+oVQkKnBoblH7V0PPvUOvDYXfopE/SJmALsxnVm6LbkotrUtNowMeIrVrBcBpaMmdS0j9df7abpSuy7HWehwJdt1lhVwi/J58U5beXGAF6c3UXLycw1wdFklArBn87xdh0ZsZtArghBdAA3+OEDVubG4UEzP6x1FOWneHh2VDAHBAt80IbdXDcesNoCvs3E5AFyNSU5nbrDPZpcUEQQTFZiEVx+51fxMhhyJEAgvlriadIJZZksRuwBYMOPBbO3hePVVqgEJhFeUuFLhIPkRP6BQLIBrmMenujm/3g4zc398awIe90Zb5A1vREALqneMcYgP/xVQWlG+Ncu5vgwwlaUNx+3799rfe96u9K0JSDXcOzOTJg4B6IgmXfsygc7/Bvg9g9E58/cDVmGIBOP/zT8Bz1zqWqpbXIsd0O9hajXfL6u4BaOS6SeWAAAAAElFTkSuQmCC\" alt=\"doco\" style=\"background-color: inherit\"/></a></span></th></tr><tr style=\"border: 1px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: white\"><td style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: white; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck1.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_q_root.gif\" alt=\".\" style=\"background-color: white; background-color: inherit\" title=\"QuestionnaireResponseRoot\" class=\"hierarchy\"/> SHINNYDisablityQuestionnaireResponse</td><td style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: white; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"></td><td style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: white; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"></td><td style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: white; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">Questionnaire:<a href=\"Questionnaire-SHINNYDisablityQuestionnaire.html\">Questionnaire[http://test.shinny.org/us/ny/hrsn/Questionnaire/SHINNYDisablityQuestionnaire|1.7.0]</a></td></tr>\r\n<tr style=\"border: 1px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: #F7F7F7\"><td style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: #F7F7F7; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck01.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin_end.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon-q-group.png\" alt=\".\" style=\"background-color: #F7F7F7; background-color: inherit\" title=\"Group\" class=\"hierarchy\"/> disability-questionnaire</td><td style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: #F7F7F7; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">Captures responses about a patient's disability mobility.</td><td style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: #F7F7F7; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: #F7F7F7; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/></tr>\r\n<tr style=\"border: 1px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: white\"><td style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: white; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck001.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin_end.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon-q-group.png\" alt=\".\" style=\"background-color: white; background-color: inherit\" title=\"Group\" class=\"hierarchy\"/> disability-questions</td><td style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: white; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">Captures a collection of screening responses about a patient's mobility and self-care.</td><td style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: white; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: white; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/></tr>\r\n<tr style=\"border: 1px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: #F7F7F7\"><td style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: #F7F7F7; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck0000.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin_end.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon-q-string.png\" alt=\".\" style=\"background-color: #F7F7F7; background-color: inherit\" title=\"Item\" class=\"hierarchy\"/> 69859-7</td><td style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: #F7F7F7; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">Do you have serious difficulty walking or climbing stairs? (5 years old or older)</td><td style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: #F7F7F7; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : var(--ig-left,left); background-color: #F7F7F7; border: 1px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span><span><a href=\"https://loinc.org/LA32-8\">LOINC LA32-8</a>: No</span></span></td></tr>\r\n<tr><td colspan=\"4\" class=\"hierarchy\"><br/><a href=\"https://hl7.org/fhir/R4/formats.html#table\" title=\"Legend for this format\"><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3goXBCwdPqAP0wAAAldJREFUOMuNk0tIlFEYhp9z/vE2jHkhxXA0zJCMitrUQlq4lnSltEqCFhFG2MJFhIvIFpkEWaTQqjaWZRkp0g26URZkTpbaaOJkDqk10szoODP//7XIMUe0elcfnPd9zsfLOYplGrpRwZaqTtw3K7PtGem7Q6FoidbGgqHVy/HRb669R+56zx7eRV1L31JGxYbBtjKK93cxeqfyQHbehkZbUkK20goELEuIzEd+dHS+qz/Y8PTSif0FnGkbiwcAjHaU1+QWOptFiyCLp/LnKptpqIuXHx6rbR26kJcBX3yLgBfnd7CxwJmflpP2wUg0HIAoUUpZBmKzELGWcN8nAr6Gpu7tLU/CkwAaoKTWRSQyt89Q8w6J+oVQkKnBoblH7V0PPvUOvDYXfopE/SJmALsxnVm6LbkotrUtNowMeIrVrBcBpaMmdS0j9df7abpSuy7HWehwJdt1lhVwi/J58U5beXGAF6c3UXLycw1wdFklArBn87xdh0ZsZtArghBdAA3+OEDVubG4UEzP6x1FOWneHh2VDAHBAt80IbdXDcesNoCvs3E5AFyNSU5nbrDPZpcUEQQTFZiEVx+51fxMhhyJEAgvlriadIJZZksRuwBYMOPBbO3hePVVqgEJhFeUuFLhIPkRP6BQLIBrmMenujm/3g4zc398awIe90Zb5A1vREALqneMcYgP/xVQWlG+Ncu5vgwwlaUNx+3799rfe96u9K0JSDXcOzOTJg4B6IgmXfsygc7/Bvg9g9E58/cDVmGIBOP/zT8Bz1zqWqpbXIsd0O9hajXfL6u4BaOS6SeWAAAAAElFTkSuQmCC\" alt=\"doco\" style=\"background-color: inherit\"/> Documentation for this format</a></td></tr></table></div>"}, "questionnaire": "http://test.shinny.org/us/ny/hrsn/Questionnaire/SHINNYDisablityQuestionnaire", "status": "completed", "subject": {"reference": "Patient/PatientExample"}, "encounter": {"reference": "Encounter/EncounterExample"}, "authored": "2024-01-01T00:00:00Z", "item": [{"linkId": "disability-questionnaire", "text": "Captures responses about a patient's disability mobility.", "item": [{"linkId": "disability-questions", "text": "Captures a collection of screening responses about a patient's mobility and self-care.", "item": [{"linkId": "69859-7", "text": "Do you have serious difficulty walking or climbing stairs? (5 years old or older)", "answer": [{"valueCoding": {"system": "http://loinc.org", "code": "LA32-8", "display": "No"}}]}]}]}]}, "request": {"method": "POST", "url": "http://test.shinny.org/us/ny/hrsn/QuestionnaireResponse/SHINNYDisablityQuestionnaireResponse"}}]}