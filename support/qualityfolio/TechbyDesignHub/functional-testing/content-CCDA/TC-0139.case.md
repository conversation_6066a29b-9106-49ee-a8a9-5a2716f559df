---
FII: TC-0139
groupId: GRP-013
title: Verify that the patient's screening details are displayed in the Patients tab when a xml file is sent to the /ccda/Bundle endpoint.
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Content - CCDA"]
priority: "High"
---

### Description

To verify that the patient's screening details are displayed in the Patients tab
when a xml file is sent to the /ccda/Bundle endpoint.

### Pre-Conditions:

1. The API is up and running.
2. A FHIR bundle must be sent to the /ccda/Bundle endpoint.
3. TechBD Hub url should be loaded.
4. The user must be logged into the application.

### Test Steps:

1. Click on the Content menu header.
2. Navigate to the Patients tab.
3. Open the view for the most recently sent Patient MRN.
4. Open the most recently sent Screening Details view.
5. Verify that the patient's screening details are displayed in the Patients
   tab.

### Expected Result:

The patient's screening details should be displayed in the Patients tab.
