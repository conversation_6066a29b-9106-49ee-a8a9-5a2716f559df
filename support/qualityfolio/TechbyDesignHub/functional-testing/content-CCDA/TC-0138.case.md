---
FII: TC-0138
groupId: GRP-013
title: Verify that the screening details are displayed in the Screenings tab when a xml file is sent to the /ccda/Bundle endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Content - CCDA"]
priority: "High"
---

### Description

To verify that the screening details are displayed in the Screenings tab when a
xml file is sent to the /ccda/Bundle endpoint.

### Pre-Conditions:

1. The API is up and running.
2. A FHIR bundle must be sent to the /ccda/Bundle endpoint.
3. TechBD Hub url should be loaded.
4. The user must be logged into the application.

### Test Steps:

1. Click on the Content menu header.
2. Navigate to the Screenings tab.
3. Open the most recently sent TechBD Tenant ID view.
4. Verify that the screening details are displayed in the Screenings tab

### Expected Result:

The screening details should be displayed in the Screenings tab.
