---
FII: TC-0133
groupId: GRP-012
title: Verify that the number of bundles sent to Tech by Design is displayed in the TechBD Tenant ID to Tech by Design column when a xml file is sent to the /ccda/Bundle endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Data Quality - CCDA"]
priority: "High"
---

### Description

To verify that the number of bundles sent to Tech by Design is displayed in the
TechBD Tenant ID to Tech by Design column when a xml file is sent to the
/ccda/Bundle endpoint.

### Pre-Conditions:

1. The API is up and running.
2. A FHIR bundle must be sent to the /ccda/Bundle endpoint.
3. TechBD Hub url should be loaded.
4. The user must be logged into the application.

### Test Steps:

1. Click on the Data Quality menu header.
2. Navigate to the Needs Attention tab.
3. Identify the latest interaction sent.
4. Locate the TechBD Tenant ID to Tech by Design column.
5. Verify that the number of bundles sent to Tech by Design is displayed in the
   TechBD Tenant ID to Tech by Design column.

### Expected Result:

The number of bundles sent to Tech by Design is successfully displayed in the
TechBD Tenant ID to Tech by Design column.
