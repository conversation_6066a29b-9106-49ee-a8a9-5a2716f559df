---
FII: TC-0135
groupId: GRP-012
title: Verify that the number of bundles that failed to send to the Data Lake is displayed in the drill-down when a xml file is sent to the /ccda/Bundle endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Data Quality - CCDA"]
priority: "High"
---

### Description

To verify that the number of bundles that failed to send to the Data Lake is
displayed in the drill-down when a xml file is sent to the /ccda/Bundle
endpoint.

### Pre-Conditions:

1. The API is up and running.
2. A FHIR bundle must be sent to the /ccda/Bundle endpoint.
3. TechBD Hub url should be loaded.
4. The user must be logged into the application.

### Test Steps:

1. Click on the Data Quality menu header.
2. Navigate to the Needs Attention tab.
3. Identify the latest interaction sent.
4. Locaate the Tech by Design to SHIN-NY Data Lake.
5. Click the drill-down arrow icon.
6. Verify that the number of bundles that failed to send to the Data Lake is
   displayed in the drill-down.

### Expected Result:

The number of bundles that failed to send to the Data Lake should be displayed
in the drill-down.
