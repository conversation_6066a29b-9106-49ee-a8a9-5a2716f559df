---
id: GRP-012
SuiteId: SUT-001
planId: ["PLN-001"]
name: "Data Quality - CCDA"
description: "Data Quality CCDA test cases validate that various UI tabs and columns correctly display data and metrics related to CCDA XML files sent to the CCDA Bundle endpoint."
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
tags: ["functional testing"]
---

### Overview

- **Needs Attention Tab**: Verify data loads correctly, identifying transactions
  requiring review.
- **Metrics and Drill-Downs**: Ensure columns and drill-downs in Tech by Design
  and Data Lake tabs accurately display bundle counts, including sent,
  successful, failed, and discarded bundles.
- **FHIR Data Quality Tab**: Confirm validation results, such as errors,
  warnings, and informational messages, are displayed accurately.
