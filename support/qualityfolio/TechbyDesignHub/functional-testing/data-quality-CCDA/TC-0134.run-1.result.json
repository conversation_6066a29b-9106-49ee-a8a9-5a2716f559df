{"test_case_fii": "TC-0134", "title": "Verify that the number of bundles successfully sent to the Data Lake is displayed in the Tech by Design to SHIN-NY Data Lake column when a xml file is sent to the /ccda/Bundle endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.835803Z", "end_time": "2025-01-23T13:50:57.835803Z", "total_duration": "20.00 seconds", "steps": [{"step": 1, "stepname": "Click on the Data Quality menu header.", "status": "passed", "start_time": "2025-01-23T13:50:37.835803Z", "end_time": "2025-01-23T13:50:57.835803Z"}, {"step": 2, "stepname": "Navigate to the Needs Attention tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.835803Z", "end_time": "2025-01-23T13:50:57.835803Z"}, {"step": 3, "stepname": "Identify the latest interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.835803Z", "end_time": "2025-01-23T13:50:57.835803Z"}, {"step": 4, "stepname": "Locate the Tech by Design to SHIN-NY Data Lake column.", "status": "passed", "start_time": "2025-01-23T13:50:37.835803Z", "end_time": "2025-01-23T13:50:57.835803Z"}, {"step": 5, "stepname": "Verify that the number of bundles successfully sent to the Data Lake is displayed in the Tech by Design to SHIN-NY Data Lake column.", "status": "passed", "start_time": "2025-01-23T13:50:37.835803Z", "end_time": "2025-01-23T13:50:57.835803Z"}]}