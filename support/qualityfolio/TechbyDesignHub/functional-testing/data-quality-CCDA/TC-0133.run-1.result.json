{"test_case_fii": "TC-0133", "title": "Verify that the number of bundles sent to Tech by Design is displayed in the TechBD Tenant ID to Tech by Design column when a xml file is sent to the /ccda/Bundle endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.834803Z", "end_time": "2025-01-23T13:51:11.834803Z", "total_duration": "34.00 seconds", "steps": [{"step": 1, "stepname": "Click on the Data Quality menu header.", "status": "passed", "start_time": "2025-01-23T13:50:37.834803Z", "end_time": "2025-01-23T13:51:11.834803Z"}, {"step": 2, "stepname": "Navigate to the Needs Attention tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.834803Z", "end_time": "2025-01-23T13:51:11.834803Z"}, {"step": 3, "stepname": "Identify the latest interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.834803Z", "end_time": "2025-01-23T13:51:11.834803Z"}, {"step": 4, "stepname": "Locate the TechBD Tenant ID to Tech by Design column.", "status": "passed", "start_time": "2025-01-23T13:50:37.834803Z", "end_time": "2025-01-23T13:51:11.834803Z"}, {"step": 5, "stepname": "Verify that the number of bundles sent to Tech by Design is displayed in the TechBD Tenant ID to Tech by Design column.", "status": "passed", "start_time": "2025-01-23T13:50:37.834803Z", "end_time": "2025-01-23T13:51:11.834803Z"}]}