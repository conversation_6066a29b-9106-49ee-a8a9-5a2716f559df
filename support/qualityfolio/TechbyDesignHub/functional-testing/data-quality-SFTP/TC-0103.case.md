---
FII: TC-0103
groupId: GRP-009
title: Verify that the number of bundles sent to Tech by Design is displayed in the TechBD Tenant ID to Tech by Design column when a CSV zip file is placed in the ingress folder via sftp
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Data Quality - SFTP"]
priority: "High"
---

### Description

To verify that the number of bundles sent to Tech by Design is displayed in the
TechBD Tenant ID to Tech by Design column when a CSV zip file is placed in the
ingress folder via sftp.

### Pre-Conditions:

1. The API is up and running.
2. Login and connect the SFTP site.
3. Place the CSV zip file to the ingress folder.
4. Response should be generated in egress folder when processing is completed
5. TechBD Hub url should be loaded.
6. The user must be logged into the application.

### Test Steps:

1. Click on the Data Quality menu header.
2. Navigate to the Needs Attention tab.
3. Identify the latest interaction sent.
4. Locate the TechBD Tenant ID to Tech by Design column.
5. Verify that the number of bundles sent to Tech by Design is displayed in the
   TechBD Tenant ID to Tech by Design column.

### Expected Result:

The number of bundles sent to Tech by Design is successfully displayed in the
TechBD Tenant ID to Tech by Design column.
