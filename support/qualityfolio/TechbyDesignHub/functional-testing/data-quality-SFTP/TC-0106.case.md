---
FII: TC-0106
groupId: GRP-009
title: Verify that detailed overview of files within the submitted zip archive that could not be processed are displaying in the File Not Processed tab when a CSV zip file with incorrect file format is placed in the ingress folder via sftp
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Data Quality - SFTP"]
priority: "High"
---

### Description

To verify that detailed overview of files within the submitted zip archive that
could not be processed are displaying in the File Not Processed tab when a CSV
zip file with incorrect file format is placed in the ingress folder via sftp.

### Pre-Conditions:

1. The API is up and running.
2. A CSV zip file with incorrect file format must be sent to the
   /flatfile/csv/Bundle endpoint.
3. TechBD Hub url should be loaded.
4. The user must be logged into the application.

### Test Steps:

1. Click on the Data Quality menu header.
2. Navigate to the CSV Data Quality tab - File Not Processed tab.
3. Identify the latest interaction sent.
4. Verify that specific issues, such as missing files or incorrect file formats,
   along with their associated error messages are displaying.

### Expected Result:

It should display specific issues, such as missing files or incorrect file
formats, along with the corresponding error messages.
