---
FII: TC-0108
groupId: GRP-009
title: Verify that errors related to the integrity of data within the submitted CSV file are displaying in the Data Integrity Errors tab when a CSV zip file with incorrect data is placed in the ingress folder via sftp
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Data Quality - SFTP"]
priority: "High"
---

### Description

To verify that errors related to the integrity of data within the submitted CSV
file are displaying in the Data Integrity Errors tab when a CSV zip file with
incorrect data is placed in the ingress folder via sftp.

### Pre-Conditions:

1. The API is up and running.
2. A CSV zip file with incorrect data must be sent to the /flatfile/csv/Bundle
   endpoint.
3. TechBD Hub url should be loaded.
4. The user must be logged into the application.

### Test Steps:

1. Click on the Data Quality menu header.
2. Navigate to the CSV Data Quality tab
3. Click on the Incomplete Groups tab.
4. Identify the latest interaction sent.
5. Verify that issues such as foreign key violations, incorrect values, and
   constraint errors for specific rows and fields in the data.

### Expected Result:

It should display issues such as foreign key violations, incorrect values, and
constraint errors for specific rows and fields in the data.
