---
FII: TC-0107
groupId: GRP-009
title: Verify that errors related to incomplete file groups are displaying in the Incomplete Groups when a CSV zip file with missing files is placed in the ingress folder via sftp
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Data Quality - SFTP"]
priority: "High"
---

### Description

To verify that errors related to incomplete file groups are displaying in the
Incomplete Groups when a CSV zip file with missing files is placed in the
ingress folder via sftp.

### Pre-Conditions:

1. The API is up and running.
2. A CSV zip file with missing files must be sent to the /flatfile/csv/Bundle
   endpoint.
3. TechBD Hub url should be loaded.
4. The user must be logged into the application.

### Test Steps:

1. Click on the Data Quality menu header.
2. Navigate to the CSV Data Quality tab
3. Click on the Incomplete Groups tab.
4. Identify the latest interaction sent.
5. Verify that row represents a missing or incomplete file within a logical
   group, with details such as the group identifier, the missing file name, and
   the associated error message.

### Expected Result:

Each row should represent a missing or incomplete file within a logical group,
including details such as the group identifier, the missing file name, and the
associated error message.
