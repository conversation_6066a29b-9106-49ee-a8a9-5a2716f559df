---
FII: TC-0083
groupId: GRP-006
title: Verify that CSV Data Quality tab is loading
created_by: "Ren<PERSON><PERSON> George"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Data Quality - CSV"]
priority: "High"
---

### Description

To verify that CSV Data Quality tab is loading.

### Pre-Conditions:

1. The API is up and running.
2. A CSV zip file must be sent to the /flatfile/csv/Bundle endpoint.
3. TechBD Hub url should be loaded.
4. The user must be logged into the application.

### Test Steps:

1. Click on the Data Quality menu header.
2. Navigate to the CSV Data Quality tab.
3. Verify that CSV Data Quality tab is loading.

### Expected Result:

The CSV Data Quality tab should load successfully.
