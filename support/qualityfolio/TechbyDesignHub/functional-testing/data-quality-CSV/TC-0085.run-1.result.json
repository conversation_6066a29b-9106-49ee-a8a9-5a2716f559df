{"test_case_fii": "TC-0085", "title": "Verify that errors related to incomplete file groups are displaying in the Incomplete Groups when a CSV zip file with missing files is sent to the /flatfile/csv/Bundle endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.779874Z", "end_time": "2025-01-23T13:51:25.779874Z", "total_duration": "48.00 seconds", "steps": [{"step": 1, "stepname": "Click on the Data Quality menu header.", "status": "passed", "start_time": "2025-01-23T13:50:37.779874Z", "end_time": "2025-01-23T13:51:25.779874Z"}, {"step": 2, "stepname": "Navigate to the CSV Data Quality tab", "status": "passed", "start_time": "2025-01-23T13:50:37.779874Z", "end_time": "2025-01-23T13:51:25.779874Z"}, {"step": 3, "stepname": "Click on the Incomplete Groups tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.779874Z", "end_time": "2025-01-23T13:51:25.779874Z"}, {"step": 4, "stepname": "Identify the latest interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.779874Z", "end_time": "2025-01-23T13:51:25.779874Z"}, {"step": 5, "stepname": "Verify that row represents a missing or incomplete file within a logical group, with details such as the group identifier, the missing file name, and the associated error message.", "status": "passed", "start_time": "2025-01-23T13:50:37.779874Z", "end_time": "2025-01-23T13:51:25.779874Z"}]}