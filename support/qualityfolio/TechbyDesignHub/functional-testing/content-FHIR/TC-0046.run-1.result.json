{"test_case_fii": "TC-0046", "title": "Verify that the Screenings tab is loading with data when a FHIR JSON V is sent to the /Bundle endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.729809Z", "end_time": "2025-01-23T13:50:45.729809Z", "total_duration": "8.00 seconds", "steps": [{"step": 1, "stepname": "Click on the Content menu header.", "status": "passed", "start_time": "2025-01-23T13:50:37.729809Z", "end_time": "2025-01-23T13:50:45.729809Z"}, {"step": 2, "stepname": "Navigate to the Screenings tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.729809Z", "end_time": "2025-01-23T13:50:45.729809Z"}, {"step": 3, "stepname": "Verify that the data is displayed on the Screenings tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.729809Z", "end_time": "2025-01-23T13:50:45.729809Z"}]}