---
FII: TC-0048
groupId: GRP-004
title: Verify that the Patients tab is loading with data when a FHIR JSON file is sent to the /Bundle endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Content - FHIR"]
priority: "High"
---

### Description

To verify that the data on the Patients tab is loading when a FHIR JSON file is
sent to the /Bundle endpoint.

### Pre-Conditions:

1. The API is up and running.
2. TechBD Hub url should be loaded.
3. The user must be logged into the application.

### Test Steps:

1. Click on the Content menu header.
2. Navigate to the Patients tab.
3. Verify that the data is displayed on the Patients tab.

### Expected Result:

The Patients tab should load and display data successfully.
