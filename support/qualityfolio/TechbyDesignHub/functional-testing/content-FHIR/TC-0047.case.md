---
FII: TC-0047
groupId: GRP-004
title: Verify that the screening details are displayed in the Screenings tab when a FHIR JSON V is sent to the /Bundle endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Content - FHIR"]
priority: "High"
---

### Description

To verify that the screening details are displayed in the Screenings tab when a
FHIR JSON file is sent to the /Bundle endpoint.

### Pre-Conditions:

1. The API is up and running.
2. A FHIR bundle must be sent to the /Bundle endpoint.
3. TechBD Hub url should be loaded.
4. The user must be logged into the application.

### Test Steps:

1. Click on the Content menu header.
2. Navigate to the Screenings tab.
3. Open the most recently sent TechBD Tenant ID view.
4. Verify that the screening details are displayed in the Screenings tab

### Expected Result:

The screening details should be displayed in the Screenings tab.
