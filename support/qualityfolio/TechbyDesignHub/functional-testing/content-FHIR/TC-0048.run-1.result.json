{"test_case_fii": "TC-0048", "title": "Verify that the Patients tab is loading with data when a FHIR JSON file is sent to the /Bundle endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.731803Z", "end_time": "2025-01-23T13:50:52.731803Z", "total_duration": "15.00 seconds", "steps": [{"step": 1, "stepname": "Click on the Content menu header.", "status": "passed", "start_time": "2025-01-23T13:50:37.731803Z", "end_time": "2025-01-23T13:50:52.731803Z"}, {"step": 2, "stepname": "Navigate to the Patients tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.731803Z", "end_time": "2025-01-23T13:50:52.731803Z"}, {"step": 3, "stepname": "Verify that the data is displayed on the Patients tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.731803Z", "end_time": "2025-01-23T13:50:52.731803Z"}]}