{"test_case_fii": "TC-0040", "title": "Verify that the number of bundles that failed to send to the Data Lake is displayed in the drill-down when a FHIR JSON file is sent to the /Bundle endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.722803Z", "end_time": "2025-01-23T13:51:00.722803Z", "total_duration": "23.00 seconds", "steps": [{"step": 1, "stepname": "Click on the Data Quality menu header.", "status": "passed", "start_time": "2025-01-23T13:50:37.722803Z", "end_time": "2025-01-23T13:51:00.722803Z"}, {"step": 2, "stepname": "Navigate to the Needs Attention tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.722803Z", "end_time": "2025-01-23T13:51:00.722803Z"}, {"step": 3, "stepname": "Identify the latest interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.722803Z", "end_time": "2025-01-23T13:51:00.722803Z"}, {"step": 4, "stepname": "Locaate the Tech by Design to SHIN-NY Data Lake.", "status": "passed", "start_time": "2025-01-23T13:50:37.722803Z", "end_time": "2025-01-23T13:51:00.722803Z"}, {"step": 5, "stepname": "Click the drill-down arrow icon.", "status": "passed", "start_time": "2025-01-23T13:50:37.722803Z", "end_time": "2025-01-23T13:51:00.722803Z"}, {"step": 6, "stepname": "Verify that the number of bundles that failed to send to the Data Lake is displayed in the drill-down.", "status": "passed", "start_time": "2025-01-23T13:50:37.722803Z", "end_time": "2025-01-23T13:51:00.722803Z"}]}