{"test_case_fii": "TC-0043", "title": "Verify that all Errors, Information, and Warnings in the validation results are displayed in the FHIR Data Quality tab when a FHIR JSON file is sent to the /Bundle endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.725803Z", "end_time": "2025-01-23T13:51:30.725803Z", "total_duration": "53.00 seconds", "steps": [{"step": 1, "stepname": "Click on the Data Quality menu header.", "status": "passed", "start_time": "2025-01-23T13:50:37.725803Z", "end_time": "2025-01-23T13:51:30.725803Z"}, {"step": 2, "stepname": "Navigate to the FHIR Data Quality tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.725803Z", "end_time": "2025-01-23T13:51:30.725803Z"}, {"step": 3, "stepname": "Identify the latest interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.725803Z", "end_time": "2025-01-23T13:51:30.725803Z"}, {"step": 4, "stepname": "Verify that all Errors, Information, and Warnings in the validation results are displayed in the FHIR Data Quality tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.725803Z", "end_time": "2025-01-23T13:51:30.725803Z"}]}