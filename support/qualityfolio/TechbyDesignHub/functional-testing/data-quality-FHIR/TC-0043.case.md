---
FII: TC-0043
groupId: GRP-003
title: Verify that all Errors, Information, and Warnings in the validation results are displayed in the FHIR Data Quality tab when a FHIR JSON file is sent to the /Bundle endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Data Quality - FHIR"]
priority: "High"
---

### Description

To verify that all Errors, Information, and Warnings in the validation results
are displayed in the FHIR Data Quality tab when a FHIR JSON file is sent to the
/Bundle endpoint.

### Pre-Conditions:

1. The API is up and running.
2. A FHIR bundle must be sent to the /Bundle endpoint.
3. TechBD Hub url should be loaded.
4. The user must be logged into the application.

### Test Steps:

1. Click on the Data Quality menu header.
2. Navigate to the FHIR Data Quality tab.
3. Identify the latest interaction sent.
4. Verify that all Errors, Information, and Warnings in the validation results
   are displayed in the FHIR Data Quality tab.

### Expected Result:

All Errors, Information, and Warnings in the validation results should be
displayed in the FHIR Data Quality tab.
