---
FII: TC-0044
groupId: GRP-003
title: Verify that the IG Publication Issues is loading with data when a FHIR JSON file is sent to the /Bundle endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Data Quality - FHIR"]
priority: "High"
---

### Description

To verify that the data on the IG Publication Issues tab is loading when a FHIR
JSON file is sent to the /Bundle endpoint.

### Pre-Conditions:

1. The API is up and running.
2. TechBD Hub url should be loaded.
3. The user must be logged into the application.

### Test Steps:

1. Click on the Data Quality menu header.
2. Navigate to the IG Publication Issues tab.
3. Verify that the data is displayed on the IG Publication Issues tab.

### Expected Result:

The IG Publication Issues tab should load and display data successfully.
