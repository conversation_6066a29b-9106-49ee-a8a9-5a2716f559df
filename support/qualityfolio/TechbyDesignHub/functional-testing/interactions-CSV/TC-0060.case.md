---
FII: TC-0060
groupId: GRP-005
title: Verify that techByDesignDisposition displays as accept when the CSV zip file is sent to /flatfile/csv/Bundle endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - CSV"]
priority: "High"
---

### Description

To verify that the techByDesignDisposition value is displayed as accept when the
CSV zip file is sent to /flatfile/csv/Bundle endpoint.

### Pre-Conditions:

1. The API is up and running.
2. A csv zip file must be sent to the /flatfile/csv/Bundle endpoint.
3. TechBD Hub url should be loaded.
4. The user must be logged into the application.

### Test Steps:

1. Click on the interactions menu header and navigate to FHIR via HTTPs tab.
2. Locate the most recent interaction sent.
3. Click on the interaction ID with the nature techByDesignDisposition.
4. Verify that accept is displayed in the techByDesignDisposition block within
   the payload.

### Expected Result:

accept should be displayed in the techByDesignDisposition block in the payload.
