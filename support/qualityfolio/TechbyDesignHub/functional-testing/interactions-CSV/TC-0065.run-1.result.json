{"test_case_fii": "TC-0065", "title": "Verify that the payload is displayed for the techByDesignDisposition interaction when a CSV zip file is sent to /flatfile/csv/Bundle/ endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.751835Z", "end_time": "2025-01-23T13:51:31.751835Z", "total_duration": "54.00 seconds", "steps": [{"step": 1, "stepname": "Click on the interactions menu header and navigate to FHIR via HTTPs tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.751835Z", "end_time": "2025-01-23T13:51:31.751835Z"}, {"step": 2, "stepname": "Locate the most recent interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.751835Z", "end_time": "2025-01-23T13:51:31.751835Z"}, {"step": 3, "stepname": "Click on the interaction ID with the nature techByDesignDisposition.", "status": "passed", "start_time": "2025-01-23T13:50:37.751835Z", "end_time": "2025-01-23T13:51:31.751835Z"}, {"step": 4, "stepname": "Verify that the payload is displayed correctly.", "status": "passed", "start_time": "2025-01-23T13:50:37.751835Z", "end_time": "2025-01-23T13:51:31.751835Z"}]}