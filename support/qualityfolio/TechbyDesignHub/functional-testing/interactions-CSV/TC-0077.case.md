---
FII: TC-0077
groupId: GRP-005
title: Verify that the FHIR Count column drill-down provides detailed information about each FHIR interaction
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - CSV"]
priority: "High"
---

### Description

To verify that the FHIR Count column drill-down provides detailed information
about each FHIR interaction, including specifics on successful and failed
transactions, when the CSV zip file is sent to /flatfile/csv/Bundle/ endpoint.

### Pre-Conditions:

1. The API is up and running.
2. A CSV zip file must be sent to the /flatfile/csv/Bundle/ endpoint.
3. TechBD Hub url should be loaded.
4. The user must be logged into the application.

### Test Steps:

1. Click on the Interactions menu header and navigate to CSV via HTTPs tab.
2. Locate the most recent interaction sent.
3. Locate the FHIR Count column.
4. Open the FHIR Count view.
5. Verify the following.
   - The detailed information about each FHIR interaction, including specifics
     on successful and failed transactions is being displayed.

### Expected Result:

The detailed information about each FHIR interaction, including specifics on
successful and failed transactions should be displayed.
