{"test_case_fii": "TC-0078", "title": "Verify that the FHIR Count is displaying correctly in CSV via HTTP tab when the CSV zip file is sent to /flatfile/csv/Bundle/$validate endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.769842Z", "end_time": "2025-01-23T13:51:00.769842Z", "total_duration": "23.00 seconds", "steps": [{"step": 1, "stepname": "Click on the Interactions menu header and navigate to CSV via HTTPs tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.769842Z", "end_time": "2025-01-23T13:51:00.769842Z"}, {"step": 2, "stepname": "Locate the most recent interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.769842Z", "end_time": "2025-01-23T13:51:00.769842Z"}, {"step": 3, "stepname": "Locate the FHIR Count column.", "status": "passed", "start_time": "2025-01-23T13:50:37.769842Z", "end_time": "2025-01-23T13:51:00.769842Z"}, {"step": 4, "stepname": "Verify the following.", "status": "passed", "start_time": "2025-01-23T13:50:37.769842Z", "end_time": "2025-01-23T13:51:00.769842Z"}]}