---
FII: TC-0068
groupId: GRP-005
title: Verify that the File Count is displaying in the CSV via HTTP tab when the CSV zip file is sent to /flatfile/csv/Bundle endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - CSV"]
priority: "High"
---

### Description

To verify that the File Count is displaying correctly when the CSV zip file is
sent to /flatfile/csv/Bundle endpoint.

### Pre-Conditions:

1. The API is up and running.
2. A csv zip file must be sent to the /flatfile/csv/Bundle endpoint.
3. TechBD Hub url should be loaded.
4. The user must be logged into the application.

### Test Steps:

1. Click on the Interactions menu header and navigate to CSV via HTTPs tab.
2. Locate the most recent interaction sent.
3. Locate the File Count column.
4. Verify the following.
   - The total count of associated demographic, administrative, observation, and
     profile files is displayed in the File Count column.

### Expected Result:

The total count of associated demographic, administrative, observation, and
profile files should be displayed in the File Count column.
