---
FII: TC-0061
groupId: GRP-005
title: Verify that five rows of interactions are displayed in the FHIR via HTTPs tab, all sharing the same interaction ID, when a CSV zip file is sent to the /flatfile/csv/Bundle/ endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - CSV"]
priority: "High"
---

### Description

To verify that the following rows of interactions are displayed in the FHIR via
HTTPs tab, all sharing the same interaction ID:

- Forwarded HTTP Response.
- Forward HTTP Request.
- org.techbd.service.http.Interactions$RequestResponseEncountered.
- Original FHIR Payload.
- techByDesignDisposition.

### Pre-Conditions:

1. The API is up and running.
2. A csv zip file must be sent to the /flatfile/csv/Bundle/ endpoint.
3. TechBD Hub url should be loaded.
4. The user must be logged into the application.

### Test Steps:

1. Click on the Interactions menu header and navigate to FHIR via HTTPs tab.
2. Locate the Nature column.
3. Identify the most recent interaction sent.
4. Verify that five rows of interactions are displayed, all sharing the same
   interaction ID:

   - Forwarded HTTP Response.
   - Forward HTTP Request.

- org.techbd.service.http.Interactions$RequestResponseEncountered.
  - Original FHIR Payload.
  - techByDesignDisposition.

### Expected Result:

The user should see five rows of interactions, all sharing the same interaction
ID:

- Forwarded HTTP Response.
- Forward HTTP Request.
- org.techbd.service.http.Interactions$RequestResponseEncountered.
- Original FHIR Payload.
- techByDesignDisposition.
