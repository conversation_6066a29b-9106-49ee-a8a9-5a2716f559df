---
FII: TC-0076
groupId: GRP-005
title: Verify the FHIR Failed Count is displaying correctly when the CSV zip file is sent to /flatfile/csv/Bundle/ endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - CSV"]
priority: "High"
---

### Description

To verify the FHIR Failed Count is displaying correctly when the CSV zip file is
sent to /flatfile/csv/Bundle/ endpoint.

### Pre-Conditions:

1. The API is up and running.
2. A CSV zip file with incorrect values in the file set must be sent to the
   /flatfile/csv/Bundle/ endpoint.
3. TechBD Hub url should be loaded.
4. The user must be logged into the application.

### Test Steps:

1. Click on the Interactions menu header and navigate to CSV via HTTPs tab.
2. Locate the most recent interaction sent.
3. Locate the FHIR Failed Count column.
4. Verify the following.
   - The total failed FHIR interactions should be displayed in the FHIR Success
     Count column if any FHIR intercation is not successfully generated.

### Expected Result:

The total failed FHIR interactions should be displayed in the FHIR Success Count
column.
