---
FII: TC-0001
groupId: GRP-001
title: Verify Successful Login with GitHub IDs
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Login"]
priority: "High"
---

### Description

To verify that a user can successfully login to TechBD hub using valid GitHub
credentials.

### Pre-Conditions:

1. TechBD Hub url should be loaded.
2. User should be a valid GitHub account holder.
3. The user has valid login credentials (username and password).

### Test Steps:

1. Open TechBD Hub url in web browser.
2. Click Login with GitHub
3. Enter valid Username or email address.
4. Enter Password
5. Click Sign In

### Expected Result:

The user should be successfully logged into TechBD Hub and redirected to the
home page.
