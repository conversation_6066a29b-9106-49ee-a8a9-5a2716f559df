---
FII: TC-0002
groupId: GRP-001
title: Verify logout functionality
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Login"]
priority: "High"
---

### Description

To verify the logout functionality.

### Pre-Conditions:

1. TechBD Hub url should be loaded.
2. The user must be logged into the application.

### Test Steps:

1. Click user icon.
2. Click logout button.
3. Signout from GitHub
4. <PERSON><PERSON> Sign out

### Expected Result:

User should be logged out of GitHub and TechBD Hub
