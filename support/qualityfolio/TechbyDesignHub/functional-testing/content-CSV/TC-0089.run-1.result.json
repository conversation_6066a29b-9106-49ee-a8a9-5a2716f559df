{"test_case_fii": "TC-0089", "title": "Verify that the patient's screening details under the organization are displayed in the Organizations tab when the CSV zip file is sent to the /flatfile/csv/Bundle endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.784457Z", "end_time": "2025-01-23T13:51:15.784457Z", "total_duration": "38.00 seconds", "steps": [{"step": 1, "stepname": "Click on the Content menu header.", "status": "passed", "start_time": "2025-01-23T13:50:37.784457Z", "end_time": "2025-01-23T13:51:15.784457Z"}, {"step": 2, "stepname": "Navigate to the Organizations tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.784457Z", "end_time": "2025-01-23T13:51:15.784457Z"}, {"step": 3, "stepname": "Open the view for the most recently sent Patient MRN.", "status": "passed", "start_time": "2025-01-23T13:50:37.784457Z", "end_time": "2025-01-23T13:51:15.784457Z"}, {"step": 4, "stepname": "Open the most recently sent Screening Details view.", "status": "passed", "start_time": "2025-01-23T13:50:37.784457Z", "end_time": "2025-01-23T13:51:15.784457Z"}, {"step": 5, "stepname": "Verify that the patient's screening details under the organization are displayed in the Organizations tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.784457Z", "end_time": "2025-01-23T13:51:15.784457Z"}]}