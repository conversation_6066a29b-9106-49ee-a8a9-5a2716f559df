{"test_case_fii": "TC-0013", "title": "Verify that the filter is working for TechBD Tenant ID on the FHIR via HTTPs tab", "status": "passed", "start_time": "2025-01-23T13:50:37.690287Z", "end_time": "2025-01-23T13:51:01.690287Z", "total_duration": "24.00 seconds", "steps": [{"step": 1, "stepname": "Click on the interactions menu header and navigate to FHIR via HTTPs tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.690287Z", "end_time": "2025-01-23T13:51:01.690287Z"}, {"step": 2, "stepname": "Locate the TechBD Tenant ID column.", "status": "passed", "start_time": "2025-01-23T13:50:37.690287Z", "end_time": "2025-01-23T13:51:01.690287Z"}, {"step": 3, "stepname": "Click on the filter icon.", "status": "passed", "start_time": "2025-01-23T13:50:37.690287Z", "end_time": "2025-01-23T13:51:01.690287Z"}, {"step": 4, "stepname": "Locate the Equals filter field.", "status": "passed", "start_time": "2025-01-23T13:50:37.690287Z", "end_time": "2025-01-23T13:51:01.690287Z"}, {"step": 5, "stepname": "Enter the input value.", "status": "passed", "start_time": "2025-01-23T13:50:37.690287Z", "end_time": "2025-01-23T13:51:01.690287Z"}, {"step": 6, "stepname": "Verify that the results are displayed correctly based on the entered value.", "status": "passed", "start_time": "2025-01-23T13:50:37.690287Z", "end_time": "2025-01-23T13:51:01.690287Z"}]}