---
FII: TC-0013
groupId: GRP-002
title: Verify that the filter is working for TechBD Tenant ID on the FHIR via HTTPs tab
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - FHIR"]
priority: "High"
---

### Description

To verify the filter for TechBD Tenant ID is functioning properly on the FHIR
via HTTPs tab.

### Pre-Conditions:

1. The API is up and running.
2. TechBD Hub url should be loaded.
3. The user must be logged into the application.

### Test Steps:

1. Click on the interactions menu header and navigate to FHIR via HTTPs tab.
2. Locate the TechBD Tenant ID column.
3. Click on the filter icon.
4. Locate the Equals filter field.
5. Enter the input value.
6. Verify that the results are displayed correctly based on the entered value.

### Expected Result:

The filter results should display correctly based on the provided input value.
