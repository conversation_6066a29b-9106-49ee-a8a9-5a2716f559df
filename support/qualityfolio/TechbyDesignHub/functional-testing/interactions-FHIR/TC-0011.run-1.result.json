{"test_case_fii": "TC-0011", "title": "Verify that techByDesignDisposition displays as reject when the FHIR JSON file is sent with an incorrect lastUpdated date to the /Bundle endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.688286Z", "end_time": "2025-01-23T13:51:09.688286Z", "total_duration": "32.00 seconds", "steps": [{"step": 1, "stepname": "Click on the interactions menu header and navigate to FHIR via HTTPs tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.688286Z", "end_time": "2025-01-23T13:51:09.688286Z"}, {"step": 2, "stepname": "Locate the most recent interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.688286Z", "end_time": "2025-01-23T13:51:09.688286Z"}, {"step": 3, "stepname": "Click on the interaction ID with the nature techByDesignDisposition.", "status": "passed", "start_time": "2025-01-23T13:50:37.688286Z", "end_time": "2025-01-23T13:51:09.688286Z"}, {"step": 4, "stepname": "Verify that reject is displayed in the techByDesignDisposition block within the payload.", "status": "passed", "start_time": "2025-01-23T13:50:37.688286Z", "end_time": "2025-01-23T13:51:09.688286Z"}]}