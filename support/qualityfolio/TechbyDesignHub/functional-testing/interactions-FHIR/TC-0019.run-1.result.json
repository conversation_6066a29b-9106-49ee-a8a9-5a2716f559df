{"test_case_fii": "TC-0019", "title": "Verify that the payload is displayed for the Original FHIR Payload interaction when a FHIR JSON file is sent to the /Bundle/ endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.697287Z", "end_time": "2025-01-23T13:51:14.697287Z", "total_duration": "37.00 seconds", "steps": [{"step": 1, "stepname": "Click on the interactions menu header and navigate to FHIR via HTTPs tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.697287Z", "end_time": "2025-01-23T13:51:14.697287Z"}, {"step": 2, "stepname": "Locate the most recent interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.697287Z", "end_time": "2025-01-23T13:51:14.697287Z"}, {"step": 3, "stepname": "Click on the interaction ID with the nature Original FHIR Payload.", "status": "passed", "start_time": "2025-01-23T13:50:37.697287Z", "end_time": "2025-01-23T13:51:14.697287Z"}, {"step": 4, "stepname": "Verify that the payload is displayed correctly.", "status": "passed", "start_time": "2025-01-23T13:50:37.697287Z", "end_time": "2025-01-23T13:51:14.697287Z"}]}