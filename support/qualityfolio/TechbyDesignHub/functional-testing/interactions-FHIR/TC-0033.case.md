---
FII: TC-0033
groupId: GRP-002
title: Verify that five rows of interactions are displayed in the HTTP Interactions tab, all sharing the same interaction ID, when a FHIR JSON file is sent to the /Bundle endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - FHIR"]
priority: "High"
---

### Description

To verify that the following rows of interactions are displayed in the HTTP
Interactions tab, all sharing the same interaction ID:

- Forwarded HTTP Response.
- Forward HTTP Request.
- org.techbd.service.http.Interactions$RequestResponseEncountered.
- Original FHIR Payload.
- techByDesignDisposition.

### Pre-Conditions:

1. The API is up and running.
2. TechBD Hub url should be loaded.
3. The user must be logged into the application.

### Test Steps:

1. Click on the Interactions menu header and navigate to HTTP Interactions tab.
2. Locate the Nature column.
3. Identify the most recent interaction sent.
4. Verify that five rows of interactions are displayed, all sharing the same
   interaction ID:

   - Forwarded HTTP Response.
   - Forward HTTP Request.
   - org.techbd.service.http.Interactions$RequestResponseEncountered.
   - Original FHIR Payload.
   - techByDesignDisposition.

### Expected Result:

The user should see five rows of interactions, all sharing the same interaction
ID:

- Forwarded HTTP Response.
- Forward HTTP Request.
- org.techbd.service.http.Interactions$RequestResponseEncountered.
- Original FHIR Payload.
- techByDesignDisposition.
