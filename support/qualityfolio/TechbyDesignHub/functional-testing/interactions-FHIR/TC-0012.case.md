---
FII: TC-0012
groupId: GRP-002
title: Verify that techByDesignDisposition displays as discard when a truncated FHIR JSON file is sent to the /Bundle endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - FHIR"]
priority: "High"
---

### Description

To verify that the techByDesignDisposition value is displayed as discard when a
truncated FHIR JSON file is sent to the /Bundle endpoint.

### Pre-Conditions:

1. The API is up and running.
2. A truncated FHIR bundle must be sent to the /Bundle endpoint.
3. TechBD Hub url should be loaded.
4. The user must be logged into the application.

### Test Steps:

1. Click on the interactions menu header and navigate to FHIR via HTTPs tab.
2. Locate the most recent interaction sent.
3. Click on the interaction ID with the nature techByDesignDisposition.
4. Verify that discard is displayed in the techByDesignDisposition block within
   the payload.

### Expected Result:

discard should be displayed in the techByDesignDisposition block in the payload.
