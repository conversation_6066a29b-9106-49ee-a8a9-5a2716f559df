{"test_case_fii": "TC-0026", "title": "Verify that the payload is displayed for the org.techbd.service.http.Interactions$RequestResponseEncountered interaction when a FHIR JSON file is sent to the /Bundle/$validate endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.705286Z", "end_time": "2025-01-23T13:50:46.705286Z", "total_duration": "9.00 seconds", "steps": [{"step": 1, "stepname": "Click on the interactions menu header and navigate to FHIR via HTTPs tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.705286Z", "end_time": "2025-01-23T13:50:46.705286Z"}, {"step": 2, "stepname": "Locate the most recent interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.705286Z", "end_time": "2025-01-23T13:50:46.705286Z"}, {"step": 3, "stepname": "Click on the interaction ID with the nature. org.techbd.service.http.Interactions$RequestResponseEncountered.", "status": "passed", "start_time": "2025-01-23T13:50:37.705286Z", "end_time": "2025-01-23T13:50:46.705286Z"}, {"step": 4, "stepname": "Verify that the payload is displayed correctly.", "status": "passed", "start_time": "2025-01-23T13:50:37.705286Z", "end_time": "2025-01-23T13:50:46.705286Z"}]}