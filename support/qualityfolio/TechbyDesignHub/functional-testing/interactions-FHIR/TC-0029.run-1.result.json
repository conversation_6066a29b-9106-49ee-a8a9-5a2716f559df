{"test_case_fii": "TC-0029", "title": "Verify that the payload is displayed for the Forwarded HTTP Response Error interaction when a FHIR JSON file is sent to the /Bundle endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.708323Z", "end_time": "2025-01-23T13:51:22.708323Z", "total_duration": "45.00 seconds", "steps": [{"step": 1, "stepname": "Click on the interactions menu header and navigate to FHIR via HTTPs FAILED tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.708323Z", "end_time": "2025-01-23T13:51:22.708323Z"}, {"step": 2, "stepname": "Locate the most recent interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.708323Z", "end_time": "2025-01-23T13:51:22.708323Z"}, {"step": 3, "stepname": "Click on the interaction ID with the nature Forwarded HTTP Response Error.", "status": "passed", "start_time": "2025-01-23T13:50:37.708323Z", "end_time": "2025-01-23T13:51:22.708323Z"}, {"step": 4, "stepname": "Verify that the payload is displayed correctly.", "status": "passed", "start_time": "2025-01-23T13:50:37.708323Z", "end_time": "2025-01-23T13:51:22.708323Z"}]}