---
FII: TC-0032
groupId: GRP-002
title: Verify that the HTTP Interactions tab is loading with data when a FHIR JSON file is sent to the /Bundle endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - FHIR"]
priority: "High"
---

### Description

To verify that the data on the HTTP Interactions tab is loading when a FHIR JSON
file is sent to the /Bundle endpoint.

### Pre-Conditions:

1. The API is up and running.
2. User should be a valid GitHub account holder.
3. The user has valid login credentials (username and password).
4. URL should be loaded.
5. The user must be logged into the application.

### Test Steps:

1. Click on the interactions menu header.
2. Click on the HTTP Interactions tab.
3. Verify that the data is displayed on the HTTP Interactions tab.

### Expected Result:

The HTTP Interactions tab should load and display data successfully.
