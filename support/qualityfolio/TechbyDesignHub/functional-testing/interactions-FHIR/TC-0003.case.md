---
FII: TC-0003
groupId: GRP-002
title: Verify that the FHIR via HTTPs tab is loading with data
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - FHIR"]
priority: "High"
---

### Description

To verify that the data on the FHIR via HTTPs tab is loading.

### Pre-Conditions:

1. The API is up and running.
2. TechBD Hub url should be loaded.
3. The user must be logged into the application.

### Test Steps:

1. Click on the interactions menu header.
2. Verify that the that data is displayed on the FHIR via HTTPs tab.

### Expected Result:

The FHIR via HTTPs tab should load and display data successfully.
