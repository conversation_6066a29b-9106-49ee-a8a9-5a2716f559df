---
FII: TC-0015
groupId: GRP-002
title: Verify that sorting works for TechBD Tenant ID on the FHIR via HTTPs tab
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - FHIR"]
priority: "High"
---

### Description

To verify that sorting works for the TechBD Tenant ID column on the FHIR via
HTTPs tab.

### Pre-Conditions:

1. The API is up and running.
2. TechBD Hub url should be loaded.
3. The user must be logged into the application.

### Test Steps:

1. Click on the interactions menu header and navigate to FHIR via HTTPs tab.
2. Locate the TechBD Tenant ID column.
3. Click on the sort icon.
4. Verify that the sorting result is displayed in ascending order.
5. Click on the sort icon again.
6. Verify that the sorting result is now displayed in descending order.

### Expected Result:

The sorting should first display the results in ascending order, and after
clicking the sort icon a second time, the results should display in descending
order.
