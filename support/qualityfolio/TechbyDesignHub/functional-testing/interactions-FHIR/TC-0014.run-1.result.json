{"test_case_fii": "TC-0014", "title": "Verify that the filter is working for Request Time on the FHIR via HTTPs tab", "status": "passed", "start_time": "2025-01-23T13:50:37.691288Z", "end_time": "2025-01-23T13:50:47.691288Z", "total_duration": "10.00 seconds", "steps": [{"step": 1, "stepname": "Click on the interactions menu header and navigate to FHIR via HTTPs tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.691288Z", "end_time": "2025-01-23T13:50:47.691288Z"}, {"step": 2, "stepname": "Locate the Request Time column.", "status": "passed", "start_time": "2025-01-23T13:50:37.691288Z", "end_time": "2025-01-23T13:50:47.691288Z"}, {"step": 3, "stepname": "Click on the filter icon.", "status": "passed", "start_time": "2025-01-23T13:50:37.691288Z", "end_time": "2025-01-23T13:50:47.691288Z"}, {"step": 4, "stepname": "Locate the Between filter field.", "status": "passed", "start_time": "2025-01-23T13:50:37.691288Z", "end_time": "2025-01-23T13:50:47.691288Z"}, {"step": 5, "stepname": "Enter the input value in the 'From' and 'To' filter fields.", "status": "passed", "start_time": "2025-01-23T13:50:37.691288Z", "end_time": "2025-01-23T13:50:47.691288Z"}, {"step": 6, "stepname": "Verify that the results are displayed correctly based on the entered value.", "status": "passed", "start_time": "2025-01-23T13:50:37.691288Z", "end_time": "2025-01-23T13:50:47.691288Z"}]}