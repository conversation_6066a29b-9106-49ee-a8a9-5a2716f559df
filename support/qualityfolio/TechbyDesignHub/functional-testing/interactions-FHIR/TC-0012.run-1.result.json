{"test_case_fii": "TC-0012", "title": "Verify that techByDesignDisposition displays as discard when a truncated FHIR JSON file is sent to the /Bundle endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.689287Z", "end_time": "2025-01-23T13:51:19.689287Z", "total_duration": "42.00 seconds", "steps": [{"step": 1, "stepname": "Click on the interactions menu header and navigate to FHIR via HTTPs tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.689287Z", "end_time": "2025-01-23T13:51:19.689287Z"}, {"step": 2, "stepname": "Locate the most recent interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.689287Z", "end_time": "2025-01-23T13:51:19.689287Z"}, {"step": 3, "stepname": "Click on the interaction ID with the nature techByDesignDisposition.", "status": "passed", "start_time": "2025-01-23T13:50:37.689287Z", "end_time": "2025-01-23T13:51:19.689287Z"}, {"step": 4, "stepname": "Verify that discard is displayed in the techByDesignDisposition block within the payload.", "status": "passed", "start_time": "2025-01-23T13:50:37.689287Z", "end_time": "2025-01-23T13:51:19.689287Z"}]}