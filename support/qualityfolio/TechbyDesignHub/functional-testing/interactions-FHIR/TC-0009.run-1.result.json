{"test_case_fii": "TC-0009", "title": "Verify that the payload is displayed for the Forwarded HTTP Response interaction when a FHIR JSON file is sent to the /Bundle endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.686287Z", "end_time": "2025-01-23T13:51:24.686287Z", "total_duration": "47.00 seconds", "steps": [{"step": 1, "stepname": "Click on the interactions menu header and navigate to FHIR via HTTPs tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.686287Z", "end_time": "2025-01-23T13:51:24.686287Z"}, {"step": 2, "stepname": "Locate the most recent interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.686287Z", "end_time": "2025-01-23T13:51:24.686287Z"}, {"step": 3, "stepname": "Click on the interaction ID with the nature Forwarded HTTP Response.", "status": "passed", "start_time": "2025-01-23T13:50:37.686287Z", "end_time": "2025-01-23T13:51:24.686287Z"}, {"step": 4, "stepname": "Verify that the payload is displayed correctly.", "status": "passed", "start_time": "2025-01-23T13:50:37.686287Z", "end_time": "2025-01-23T13:51:24.686287Z"}]}