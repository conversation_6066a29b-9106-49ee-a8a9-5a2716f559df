---
FII: TC-0025
groupId: GRP-002
title: Verify that one interaction row is displayed in the FHIR via HTTPS tab when a FHIR JSON file is sent to the /Bundle/$validate endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - FHIR"]
priority: "High"
---

### Description

To verify that the following interaction row is displayed in the FHIR via HTTPS
tab:

- org.techbd.service.http.Interactions$RequestResponseEncountered, when a FHIR
  JSON file is sent to the /Bundle/$validate endpoint

### Pre-Conditions:

1. The API is up and running.
2. A FHIR bundle must be sent to the /Bundle/$validate endpoint.
3. TechBD Hub url should be loaded.
4. The user must be logged into the application.

### Test Steps:

1. Click on the Interactions menu header and navigate to FHIR via HTTPs tab.
2. Locate the Nature column.
3. Identify the most recent interaction sent.
4. Verify that one interaction row is displayed:
   - org.techbd.service.http.Interactions$RequestResponseEncountered.

### Expected Result:

The user should see one interaction row:

- org.techbd.service.http.Interactions$RequestResponseEncountered.
