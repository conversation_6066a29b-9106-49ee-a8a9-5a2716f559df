---
FII: TC-0014
groupId: GRP-002
title: Verify that the filter is working for Request Time on the FHIR via HTTPs tab
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - FHIR"]
priority: "High"
---

### Description

To verify the filter for Request Time is functioning properly on the FHIR via
HTTPs tab.

### Pre-Conditions:

1. The API is up and running.
2. TechBD Hub url should be loaded.
3. The user must be logged into the application.

### Test Steps:

1. Click on the interactions menu header and navigate to FHIR via HTTPs tab.
2. Locate the Request Time column.
3. Click on the filter icon.
4. Locate the Between filter field.
5. Enter the input value in the 'From' and 'To' filter fields.
6. Verify that the results are displayed correctly based on the entered value.

### Expected Result:

The filter results should display correctly based on the provided input value.
