{"test_case_fii": "TC-0092", "title": "Verify that the payload is displayed for the Forward HTTP Request interaction when a CSV zip file is placed in the ingress folder via sftp", "status": "passed", "start_time": "2025-01-23T13:50:37.787509Z", "end_time": "2025-01-23T13:51:25.787509Z", "total_duration": "48.00 seconds", "steps": [{"step": 1, "stepname": "Click on the interactions menu header and navigate to FHIR via HTTPs tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.787509Z", "end_time": "2025-01-23T13:51:25.787509Z"}, {"step": 2, "stepname": "Locate the most recent interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.787509Z", "end_time": "2025-01-23T13:51:25.787509Z"}, {"step": 3, "stepname": "Click on the interaction ID with the nature Forward HTTP Request.", "status": "passed", "start_time": "2025-01-23T13:50:37.787509Z", "end_time": "2025-01-23T13:51:25.787509Z"}, {"step": 4, "stepname": "Verify that the payload is displayed correctly.", "status": "passed", "start_time": "2025-01-23T13:50:37.787509Z", "end_time": "2025-01-23T13:51:25.787509Z"}]}