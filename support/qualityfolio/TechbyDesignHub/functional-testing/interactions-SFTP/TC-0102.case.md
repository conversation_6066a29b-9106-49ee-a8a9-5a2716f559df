---
FII: TC-0102
groupId: GRP-008
title: Verify that five rows of interactions are displayed in the HTTP Interactions tab, all sharing the same interaction ID, when a CSV zip file is placed in the ingress folder via sftp
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - SFTP"]
priority: "High"
---

### Description

To verify that the following rows of interactions are displayed in the HTTP
Interactions tab, all sharing the same interaction ID:

- Forwarded HTTP Response.
- Forward HTTP Request.
- org.techbd.service.http.Interactions$RequestResponseEncountered.
- Original FHIR Payload.
- techByDesignDisposition.

### Pre-Conditions:

1. The API is up and running.
2. Login and connect the SFTP site.
3. Place the CSV zip file to the ingress folder.
4. Response should be generated in egress folder when processing is completed
5. TechBD Hub url should be loaded.
6. The user must be logged into the application.

### Test Steps:

1. Click on the Interactions menu header and navigate to HTTP Interactions tab.
2. Locate the Nature column.
3. Identify the most recent interaction sent.
4. Verify that five rows of interactions are displayed, all sharing the same
   interaction ID:

   - Forwarded HTTP Response.
   - Forward HTTP Request.
   - org.techbd.service.http.Interactions$RequestResponseEncountered.
   - Original FHIR Payload.
   - techByDesignDisposition.

### Expected Result:

The user should see five rows of interactions, all sharing the same interaction
ID:

- Forwarded HTTP Response.
- Forward HTTP Request.
- org.techbd.service.http.Interactions$RequestResponseEncountered.
- Original FHIR Payload.
- techByDesignDisposition.
