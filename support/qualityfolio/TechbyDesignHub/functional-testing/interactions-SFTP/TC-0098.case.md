---
FII: TC-0098
groupId: GRP-008
title: Verify tha the FHIR Count is displaying correctly when a CSV zip file is placed in the ingress folder via sftp
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - SFTP"]
priority: "High"
---

### Description

To verify that the FHIR Count is displaying correctly when a CSV zip file is
placed in the ingress folder via sftp.

### Pre-Conditions:

1. The API is up and running.
2. Login and connect the SFTP site.
3. Place the CSV zip file to the ingress folder.
4. Response should be generated in egress folder when processing is completed
5. TechBD Hub url should be loaded.
6. The user must be logged into the application.

### Test Steps:

1. Click on the Interactions menu header and navigate to CSV via HTTPs tab.
2. Locate the most recent interaction sent.
3. Locate the FHIR Count column.
4. Verify the following.
   - The total FHIR interactions should be displayed in the FHIR Count column.

### Expected Result:

The total FHIR interactions should be displayed in the FHIR Count column.
