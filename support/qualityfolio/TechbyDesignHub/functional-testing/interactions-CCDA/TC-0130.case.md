---
FII: TC-0130
groupId: GRP-011
title: Verify that one interaction row is displayed in the  FHIR via HTTPs FAILED tab when a FHIR JSON is sent to the /ccda/Bundle/ endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - CCDA"]
priority: "High"
---

### Description

To verify that the following interaction row is displayed in the FHIR via HTTPs
FAILED tab:

- Forwarded HTTP Response Error.

### Pre-Conditions:

1. The API is up and running.
2. A bundle must be sent to the /ccda/Bundle/ endpoint with an invalid Data Lake
   URL.
3. User should be a valid GitHub account holder.
4. The user has valid login credentials (username and password).
5. URL should be loaded.
6. The user must be logged into the application.

### Test Steps:

1. Click on the Interactions menu header and navigate to FHIR via HTTPs FAILED
   tab.
2. Locate the Nature column.
3. Identify the most recent interaction sent.
4. Verify that one interaction row is displayed:
   - Forwarded HTTP Response Error.

### Expected Result:

The user should see one interaction row:

- Forwarded HTTP Response Error.
