{"test_case_fii": "TC-0122", "title": "Verify that the payload is displayed for the Original FHIR Payload interaction when a xml file is sent to the /ccda/Bundle/ endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.821798Z", "end_time": "2025-01-23T13:51:12.821798Z", "total_duration": "35.00 seconds", "steps": [{"step": 1, "stepname": "Click on the interactions menu header and navigate to FHIR via HTTPs tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.821798Z", "end_time": "2025-01-23T13:51:12.821798Z"}, {"step": 2, "stepname": "Locate the most recent interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.821798Z", "end_time": "2025-01-23T13:51:12.821798Z"}, {"step": 3, "stepname": "Click on the interaction ID with the nature Original FHIR Payload.", "status": "passed", "start_time": "2025-01-23T13:50:37.821798Z", "end_time": "2025-01-23T13:51:12.821798Z"}, {"step": 4, "stepname": "Verify that the payload is displayed correctly.", "status": "passed", "start_time": "2025-01-23T13:50:37.821798Z", "end_time": "2025-01-23T13:51:12.821798Z"}]}