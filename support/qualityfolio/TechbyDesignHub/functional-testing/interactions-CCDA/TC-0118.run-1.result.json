{"test_case_fii": "TC-0118", "title": "Verify that techByDesignDisposition displays as accept when a xml file is sent to the /ccda/Bundle endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.817832Z", "end_time": "2025-01-23T13:51:16.817832Z", "total_duration": "39.00 seconds", "steps": [{"step": 1, "stepname": "Click on the interactions menu header and navigate to FHIR via HTTPs tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.817832Z", "end_time": "2025-01-23T13:51:16.817832Z"}, {"step": 2, "stepname": "Locate the most recent interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.817832Z", "end_time": "2025-01-23T13:51:16.817832Z"}, {"step": 3, "stepname": "Click on the interaction ID with the nature techByDesignDisposition.", "status": "passed", "start_time": "2025-01-23T13:50:37.817832Z", "end_time": "2025-01-23T13:51:16.817832Z"}, {"step": 4, "stepname": "Verify that accept is displayed in the techByDesignDisposition block within the payload.", "status": "passed", "start_time": "2025-01-23T13:50:37.817832Z", "end_time": "2025-01-23T13:51:16.817832Z"}]}