---
id: GRP-011
SuiteId: SUT-001
planId: ["PLN-001"]
name: "Interactions - CCDA"
description: "Interaction CCDA test cases focus on verifying various aspects of interactions and functionalities related to sending CCDA XML files to CCDA  Validate and Bundle endpoints."
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
tags: ["functional testing"]
---

### Overview

- **Interaction Rows**: Validate the correct number of interaction rows with
  consistent interaction IDs.
- **Payload Display**: Ensure payloads are correctly displayed for all CCDA
  interactions
- **Disposition States**: Validate techByDesignDisposition as "accept" for valid
  files.
