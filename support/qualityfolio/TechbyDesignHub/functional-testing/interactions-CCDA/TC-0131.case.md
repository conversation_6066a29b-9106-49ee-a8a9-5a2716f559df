---
FII: TC-0131
groupId: GRP-011
title: Verify that the payload is displayed for the Forwarded HTTP Response Error interaction when a FHIR JSON is sent to the /ccda/Bundle/ endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - CCDA"]
priority: "High"
---

### Description

To verify that the payload is correctly displayed for the Forwarded HTTP
Response Error interaction when a FHIR JSON is sent to the /ccda/Bundle/
endpoint.

### Pre-Conditions:

1. The API is up and running.
2. A bundle must be sent to the /ccda/Bundle/ endpoint with an invalid Data Lake
   URL.
3. User should be a valid GitHub account holder.
4. The user has valid login credentials (username and password).
5. URL should be loaded.
6. The user must be logged into the application.

### Test Steps:

1. Click on the interactions menu header and navigate to FHIR via HTTPs FAILED
   tab.
2. Locate the most recent interaction sent.
3. Click on the interaction ID with the nature Forwarded HTTP Response Error.
4. Verify that the payload is displayed correctly.

### Expected Result:

The payload should be displayed successfully.
