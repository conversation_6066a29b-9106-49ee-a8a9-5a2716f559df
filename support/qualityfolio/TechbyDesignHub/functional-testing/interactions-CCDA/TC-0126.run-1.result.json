{"test_case_fii": "TC-0126", "title": "Verify that one interaction row is displayed in the FHIR via HTTPS tab when a xml file is sent to the /ccda/Bundle/$validate endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.826803Z", "end_time": "2025-01-23T13:51:37.826803Z", "total_duration": "60.00 seconds", "steps": [{"step": 1, "stepname": "Click on the Interactions menu header and navigate to FHIR via HTTPs tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.826803Z", "end_time": "2025-01-23T13:51:37.826803Z"}, {"step": 2, "stepname": "Locate the Nature column.", "status": "passed", "start_time": "2025-01-23T13:50:37.826803Z", "end_time": "2025-01-23T13:51:37.826803Z"}, {"step": 3, "stepname": "Identify the most recent interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.826803Z", "end_time": "2025-01-23T13:51:37.826803Z"}, {"step": 4, "stepname": "Verify that one interaction row is displayed:", "status": "passed", "start_time": "2025-01-23T13:50:37.826803Z", "end_time": "2025-01-23T13:51:37.826803Z"}]}