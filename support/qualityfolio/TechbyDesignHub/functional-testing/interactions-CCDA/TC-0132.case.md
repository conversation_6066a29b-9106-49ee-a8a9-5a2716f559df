---
FII: TC-0132
groupId: GRP-011
title: Verify that five rows of interactions are displayed in the HTTP Interactions tab, all sharing the same interaction ID, when a xml is sent to the /ccda/Bundle endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - CCDA"]
priority: "High"
---

### Description

To verify that the following rows of interactions are displayed in the HTTP
Interactions tab, all sharing the same interaction ID:

- Forwarded HTTP Response.
- Forward HTTP Request.
- org.techbd.service.http.Interactions$RequestResponseEncountered.
- Original FHIR Payload.
- techByDesignDisposition.

### Pre-Conditions:

1. The API is up and running.
2. A bundle must be sent to the /ccda/Bundle endpoint.
3. User should be a valid GitHub account holder.
4. The user has valid login credentials (username and password).
5. URL should be loaded.
6. The user must be logged into the application.

### Test Steps:

1. Click on the Interactions menu header and navigate to HTTP Interactions tab.
2. Locate the Nature column.
3. Identify the most recent interaction sent.
4. Verify that five rows of interactions are displayed, all sharing the same
   interaction ID:

   - Forwarded HTTP Response.
   - Forward HTTP Request.
   - org.techbd.service.http.Interactions$RequestResponseEncountered.
   - Original FHIR Payload.
   - techByDesignDisposition.

### Expected Result:

The user should see five rows of interactions, all sharing the same interaction
ID:

- Forwarded HTTP Response.
- Forward HTTP Request.
- org.techbd.service.http.Interactions$RequestResponseEncountered.
- Original FHIR Payload.
- techByDesignDisposition.
