{"test_case_fii": "TC-0116", "title": "Verify that the payload is displayed for the techByDesignDisposition interaction when a xml file is sent to the /ccda/Bundle endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.815221Z", "end_time": "2025-01-23T13:51:28.815221Z", "total_duration": "51.00 seconds", "steps": [{"step": 1, "stepname": "Click on the interactions menu header and navigate to FHIR via HTTPs tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.815221Z", "end_time": "2025-01-23T13:51:28.815221Z"}, {"step": 2, "stepname": "Locate the most recent interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.815221Z", "end_time": "2025-01-23T13:51:28.815221Z"}, {"step": 3, "stepname": "Click on the interaction ID with the nature techByDesignDisposition.", "status": "passed", "start_time": "2025-01-23T13:50:37.815221Z", "end_time": "2025-01-23T13:51:28.815221Z"}, {"step": 4, "stepname": "Verify that the payload is displayed correctly.", "status": "passed", "start_time": "2025-01-23T13:50:37.815221Z", "end_time": "2025-01-23T13:51:28.815221Z"}]}