---
FII: TC-0113
groupId: GRP-011
title: Verify that the payload is displayed for the org.techbd.service.http.Interactions$RequestResponseEncountered interaction when a xml file is sent to the /ccda/Bundle endpoint
created_by: "<PERSON><PERSON><PERSON>"
created_at: "2025-01-23"
test_type: "Manual"
tags: ["Interactions - CCDA"]
priority: "High"
---

### Description

To verify that the payload is correctly displayed for the
org.techbd.service.http.Interactions$RequestResponseEncountered interaction when
a xml file is sent to the /ccda/Bundle endpoint.

### Pre-Conditions:

1. The API is up and running.
2. A FHIR bundle must be sent to the /ccda/Bundle endpoint.
3. TechBD Hub url should be loaded.
4. The user must be logged into the application.

### Test Steps:

1. Click on the interactions menu header and navigate to FHIR via HTTPs tab.
2. Locate the most recent interaction sent.
3. Click on the interaction ID with the nature
   org.techbd.service.http.Interactions$RequestResponseEncountered.
4. Verify that the payload is displayed correctly.

### Expected Result:

The payload should be displayed successfully.
