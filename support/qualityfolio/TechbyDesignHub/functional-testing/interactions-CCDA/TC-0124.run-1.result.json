{"test_case_fii": "TC-0124", "title": "Verify that the payload is displayed for the Forwarded HTTP Response interaction when a xml file is sent to the /ccda/Bundle/ endpoint", "status": "passed", "start_time": "2025-01-23T13:50:37.823803Z", "end_time": "2025-01-23T13:51:00.823803Z", "total_duration": "23.00 seconds", "steps": [{"step": 1, "stepname": "Click on the interactions menu header and navigate to FHIR via HTTPs tab.", "status": "passed", "start_time": "2025-01-23T13:50:37.823803Z", "end_time": "2025-01-23T13:51:00.823803Z"}, {"step": 2, "stepname": "Locate the most recent interaction sent.", "status": "passed", "start_time": "2025-01-23T13:50:37.823803Z", "end_time": "2025-01-23T13:51:00.823803Z"}, {"step": 3, "stepname": "Click on the interaction ID with the nature Forwarded HTTP Response.", "status": "passed", "start_time": "2025-01-23T13:50:37.823803Z", "end_time": "2025-01-23T13:51:00.823803Z"}, {"step": 4, "stepname": "Verify that the payload is displayed correctly.", "status": "passed", "start_time": "2025-01-23T13:50:37.823803Z", "end_time": "2025-01-23T13:51:00.823803Z"}]}