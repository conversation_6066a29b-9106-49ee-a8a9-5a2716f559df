<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?xml-stylesheet href="CCDA.xsl" type="text/xsl"?><ClinicalDocument xmlns="urn:hl7-org:v3" xmlns:sdtc="urn:hl7-org:sdtc" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <realmCode code="US"/>
    <typeId extension="POCD_HD000040" root="2.16.840.1.113883.1.3"/>
    <templateId extension="2015-08-01" root="2.16.840.1.113883.10.20.22.1.1"/>
    <templateId root="2.16.840.1.113883.10.20.22.1.1"/>
    <templateId extension="2015-08-01" root="2.16.840.1.113883.10.20.22.1.2"/>
    <templateId root="2.16.840.1.113883.10.20.22.1.2"/>
    <id root="4904cd17-54c7-4577-8374-d49613f2b937"/>
    <code code="34133-9" codeSystem="2.16.840.1.113883.6.1" displayName="Summarization of Episode Note"/>
    <title>Continuity of Care Document</title>
    <effectiveTime value="20250905123746-0400"/>
    <confidentialityCode code="N" codeSystem="2.16.840.1.113883.5.25"/>
    <languageCode code="en-US"/>
    <recordTarget>
        <patientRole>
            <id extension="a-2190101.E-79238.P-79238" root="2.16.840.1.113883.3.564"/>
            <addr use="HP">
                <streetAddressLine>238 ARSENAL ST</streetAddressLine>
                <city>WATERTOWN</city>
                <state>NY</state>
                <postalCode>13601</postalCode>
                <country>USA</country>
                <useablePeriod xsi:type="IVL_TS">
                    <low value="20240111"/>
                </useablePeriod>
            </addr>
            <addr use="PST">
                <streetAddressLine>238 ARSENAL ST</streetAddressLine>
                <city>WATERTOWN</city>
                <state>NY</state>
                <postalCode>13601</postalCode>
                <country>USA</country>
                <useablePeriod xsi:type="IVL_TS">
                    <low value="20240111"/>
                </useablePeriod>
            </addr>
            <telecom use="HP" value="tel:+****************"/>
            <telecom use="MC" value="tel:+1-(*************"/>
            <telecom use="WP" value="tel:+****************"/>
            <patient>
                <name use="L">
                    <given>Adult</given>
                    <family>Test</family>
                </name>
                <administrativeGenderCode code="F" codeSystem="2.16.840.1.113883.5.1" codeSystemName="AdministrativeGender" displayName="Female"/>
                <birthTime value="19600714"/>
                <maritalStatusCode code="S" codeSystem="2.16.840.1.113883.5.2" codeSystemName="MaritalStatusCode" displayName="Never married"/>
                <raceCode code="2106-3" codeSystem="2.16.840.1.113883.6.238" codeSystemName="Race &amp; Ethnicity - CDC" displayName="White"/>
                <ethnicGroupCode code="2186-5" codeSystem="2.16.840.1.113883.6.238" codeSystemName="Race &amp; Ethnicity - CDC" displayName="Not Hispanic or Latino"/>
                <languageCommunication>
                    <languageCode code="en"/>
                </languageCommunication>
            </patient>
            <providerOrganization>
                <id extension="**********" root="2.16.840.1.113883.4.6"/>
                <name>NY - NORTH COUNTRY FAMILY HEALTH CENTER</name>
                <telecom use="WP" value="tel: (*************"/>
                <addr use="WP">
                    <streetAddressLine>5402 DAYAN ST</streetAddressLine>
                    <city>LOWVILLE</city>
                    <state>NY</state>
                    <postalCode>13367-1100</postalCode>
                    <country>USA</country>
                </addr>
            </providerOrganization>
        </patientRole>
    </recordTarget>
    <author>
        <time value="20250905123746-0500"/>
        <assignedAuthor>
            <id root="2.16.840.1.113883.3.564"/>
            <addr use="WP">
                <streetAddressLine>Boston Landing</streetAddressLine>
                <streetAddressLine>80 Guest Street</streetAddressLine>
                <city>Boston</city>
                <state>MA</state>
                <postalCode>02135</postalCode>
                <country>US</country>
            </addr>
            <telecom use="WP" value="tel:+1-***********"/>
            <assignedAuthoringDevice>
                <manufacturerModelName>athenahealth</manufacturerModelName>
                <softwareName>Document Generation Engine</softwareName>
            </assignedAuthoringDevice>
            <representedOrganization>
                <id extension="2190101" root="565b4956-fe78-45e7-a984-43406ba4599f"/>
                <name>NY - NORTH COUNTRY FAMILY HEALTH CENTER, Lowville Family Health Center</name>
                <telecom use="WP" value="tel: (*************"/>
                <addr use="WP">
                    <streetAddressLine>5402 DAYAN ST</streetAddressLine>
                    <city>LOWVILLE</city>
                    <state>NY</state>
                    <postalCode>13367-1100</postalCode>
                    <country>USA</country>
                </addr>
            </representedOrganization>
        </assignedAuthor>
    </author>
    <custodian>
        <assignedCustodian>
            <representedCustodianOrganization>
                <id root="2.16.840.1.113883.3.564"/>
                <name>athenahealth</name>
                <telecom use="WP" value="tel:+1-***********"/>
                <addr use="WP">
                    <streetAddressLine>Boston Landing</streetAddressLine>
                    <streetAddressLine>80 Guest Street</streetAddressLine>
                    <city>Boston</city>
                    <state>MA</state>
                    <postalCode>02135</postalCode>
                    <country>US</country>
                </addr>
            </representedCustodianOrganization>
        </assignedCustodian>
    </custodian>
    <legalAuthenticator>
        <time value="20250905123625-0400"/>
        <signatureCode code="S"/>
        <assignedEntity>
            <id extension="**********" root="2.16.840.1.113883.4.6"/>
            <addr use="WP">
                <streetAddressLine>238 ARSENAL STREET</streetAddressLine>
                <city>WATERTOWN</city>
                <state>NY</state>
                <postalCode>13601-2504</postalCode>
                <country>US</country>
            </addr>
            <telecom use="WP" value="tel: (*************"/>
            <assignedPerson>
                <name>
                    <given>THOMAS</given>
                    <family>BARBER</family>
                    <suffix>DO</suffix>
                </name>
            </assignedPerson>
        </assignedEntity>
    </legalAuthenticator>
    <documentationOf>
        <serviceEvent classCode="PCPR">
            <effectiveTime>
                <low value="20250905"/>
                <high nullFlavor="NI"/>
            </effectiveTime>
        </serviceEvent>
    </documentationOf>
    <componentOf>
        <encompassingEncounter>
            <id extension="E-973132.A-2251737" root="2.16.840.1.113883.19"/>
            <effectiveTime value="20250905"/>
            <encounterParticipant typeCode="ATND">
                <assignedEntity>
                    <id extension="**********" root="2.16.840.1.113883.4.6"/>
                    <code code="207RS0010X" codeSystem="2.16.840.1.113883.6.101" codeSystemName="NUCC" displayName="Family Medicine"/>
                    <addr use="WP">
                        <streetAddressLine>5402 DAYAN ST </streetAddressLine>
                        <city>LOWVILLE</city>
                        <state>NY</state>
                        <postalCode>13367-1100</postalCode>
                        <country>USA</country>
                    </addr>
                    <telecom use="WP" value="tel: (*************"/>
                    <assignedPerson>
                        <name>
                            <given>Thomas</given>
                            <family>Barber</family>
                        </name>
                    </assignedPerson>
                </assignedEntity>
            </encounterParticipant>
        </encompassingEncounter>
    </componentOf>
    <component>
        <structuredBody>
            <component>
                <section>
                    <templateId root="2.16.840.1.113883.**********.8"/>
                    <id root="0a87032f-9345-41a8-b884-87554a9e47e9"/>
                    <code code="51848-0" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="ASSESSMENTS"/>
                    <title>Assessment</title>
                    <text>
                        <paragraph>No assessment recorded.</paragraph>
                    </text>
                </section>
            </component>
            <component>
                <section>
                    <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.10"/>
                    <templateId root="2.16.840.1.113883.**********.10"/>
                    <id root="9b27bd78-a7ac-4bf5-9b61-ab0094e482aa"/>
                    <code code="18776-5" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"/>
                    <title>Plan of Treatment</title>
                    <text>
                        <table>
                            <thead>
                                <tr>
                                    <th>Reminders</th>
                                    <th/>
                                    <th>Order Date</th>
                                    <th>Submit Date</th>
                                    <th>Provider Name</th>
                                    <th>Organization Details</th>
                                    <th>Last Modified By</th>
                                    <th>Last Modified Time</th>
                                    <th>Details</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td styleCode="Botrule">
                                        <content styleCode="Bold">Appointments</content>
                                    </td>
                                    <td styleCode="Botrule">
                                        <content ID="PlanOfCareAct1">EXTENDED FOLLOW-UP</content>
                                    </td>
                                    <td styleCode="Botrule">
    09/05/2025 10:00AM
  </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">THOMAS BARBER, DO</td>
                                    <td styleCode="Botrule">Not available</td>
                                    <td styleCode="Botrule">Not available</td>
                                    <td styleCode="Botrule">Not available</td>
                                    <td styleCode="Botrule"/>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">
                                        <content styleCode="Bold">Lab</content>
                                    </td>
                                    <td styleCode="Botrule">
    None recorded.
  </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">
                                        <content styleCode="Bold">Referral</content>
                                    </td>
                                    <td styleCode="Botrule">
    None recorded.
  </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">
                                        <content styleCode="Bold">Procedures</content>
                                    </td>
                                    <td styleCode="Botrule">
    None recorded.
  </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">
                                        <content styleCode="Bold">Surgeries</content>
                                    </td>
                                    <td styleCode="Botrule">
    None recorded.
  </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">
                                        <content styleCode="Bold">Imaging</content>
                                    </td>
                                    <td styleCode="Botrule">
    None recorded.
  </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">
                                        <content styleCode="Bold">MedicationOrders</content>
                                    </td>
                                    <td styleCode="Botrule">
    None recorded.
  </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">
                                        <content styleCode="Bold">VaccineOrders</content>
                                    </td>
                                    <td styleCode="Botrule">
    None recorded.
  </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                </tr>
                            </tbody>
                        </table>
                        <br/>
                        <content>Patient Targets</content>
                        <content>No targets recorded.</content>
                        <br/>
                        <content>Patient Instructions</content>
                        <content>No instructions recorded.</content>
                    </text>
                    <entry>
                        <encounter classCode="ENC" moodCode="INT">
                            <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.40"/>
                            <templateId root="2.16.840.1.113883.**********.40"/>
                            <id root="f6f2bfd0-6838-4628-b481-adf5a68d7470"/>
                            <code codeSystem="2.16.840.1.113883.3.88.12.80.32" codeSystemName="CPT-4" nullFlavor="NI">
                                <originalText>
                                    <reference value="#PlanOfCareAct1"/>
                                </originalText>
                            </code>
                            <statusCode code="active"/>
                            <effectiveTime value="202509051000"/>
                            <performer typeCode="PRF">
                                <assignedEntity>
                                    <id root="4ef0fe71-f696-4a36-aa2e-1682c96b4b63"/>
                                    <addr use="WP">
                                        <streetAddressLine>5402 DAYAN ST</streetAddressLine>
                                        <city>LOWVILLE</city>
                                        <state>NY</state>
                                        <postalCode>13367-1100</postalCode>
                                        <country>USA</country>
                                    </addr>
                                    <telecom use="WP" value="tel: (*************"/>
                                    <assignedPerson>
                                        <name>
                                            <given>Thomas</given>
                                            <family>Barber</family>
                                        </name>
                                    </assignedPerson>
                                </assignedEntity>
                            </performer>
                        </encounter>
                    </entry>
                </section>
            </component>
            <!-- Reason for Referral Section -->
            <component>
                <section>
                    <templateId extension="2014-06-09" root="1.3.6.1.4.1.19376.1.5.3.1.3.1"/>
                    <templateId root="1.3.6.1.4.1.19376.1.5.3.1.3.1"/>
                    <id root="2b6c9c18-83d7-46f9-ba0f-5fcaad683e42"/>
                    <code code="42349-1" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Reason for Referral"/>
                    <title>Reason for Referral</title>
                    <text>
      None Reported.
      </text>
                </section>
            </component>
            <component>
                <section>
                    <templateId root="2.16.840.1.113883.**********.12"/>
                    <id root="866ae463-92df-41b4-9cb0-05849eb38f29"/>
                    <code code="29299-5" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Reason for Visit"/>
                    <title>Reason for Visit</title>
                    <text>
                        <table>
                            <thead>
                                <tr>
                                    <th>Reason</th>
                                    <th>Note</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td styleCode="Botrule">abdominal pain</td>
                                    <td styleCode="Botrule"/>
                                </tr>
                            </tbody>
                        </table>
                    </text>
                </section>
            </component>
            <component>
                <section nullFlavor="NI">
                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.3.1"/>
                    <templateId root="2.16.840.1.113883.**********.3.1"/>
                    <id root="52339419-b368-43f3-8085-80fa063192b9"/>
                    <code code="30954-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"/>
                    <title>Results</title>
                    <text>
                        <paragraph>
                            <content ID="result1">None recorded.</content>
                        </paragraph>
                    </text>
                    <entry>
                        <organizer classCode="BATTERY" moodCode="EVN">
                            <templateId root="2.16.840.1.113883.**********.1"/>
                            <id root="42985dc7-f8a1-4d44-9888-c67bead6674d"/>
                            <code nullFlavor="NI" xsi:type="CE"/>
                            <statusCode code="completed"/>
                            <effectiveTime nullFlavor="NI"/>
                            <component>
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.2"/>
                                    <id root="ade53e92-62f3-46ae-b0b6-207d0a0ba14c"/>
                                    <code nullFlavor="NI"/>
                                    <text>
                                        <reference value="#result1"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime nullFlavor="NI"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                    <interpretationCode nullFlavor="NI"/>
                                    <referenceRange>
                                        <observationRange>
                                            <text nullFlavor="NI"/>
                                            <value nullFlavor="NI" xsi:type="ST"/>
                                        </observationRange>
                                    </referenceRange>
                                </observation>
                            </component>
                        </organizer>
                    </entry>
                </section>
            </component>
            <component>
                <section>
                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.5.1"/>
                    <templateId root="2.16.840.1.113883.**********.5.1"/>
                    <id root="517d8f04-6f4e-4616-973a-ffff485c633f"/>
                    <code code="11450-4" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Problems"/>
                    <title>Problems</title>
                    <text>
                        <table>
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Problem SNOMED Code</th>
                                    <th>Status</th>
                                    <th>Onset Date</th>
                                    <th>Resolution Date</th>
                                    <th>Notes</th>
                                    <th>Provider Name and Address</th>
                                    <th>Organization Details</th>
                                    <th>Recorded Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td styleCode="Botrule">
                                        <content ID="problem0">Type 1 diabetes mellitus</content>
                                    </td>
                                    <td styleCode="Botrule">46635009</td>
                                    <td styleCode="Botrule">Completed</td>
                                    <td styleCode="Botrule">11/05/2021</td>
                                    <td styleCode="Botrule">11/05/2021</td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">
                                        Jeanna Everard
            
              
                                        <paragraph> null,   </paragraph>
                                        <paragraph/>
                                    </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">11/05/2021 13:36:52</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">
                                        <content ID="problem1">Generalized anxiety disorder</content>
                                    </td>
                                    <td styleCode="Botrule">21897009</td>
                                    <td styleCode="Botrule">Active</td>
                                    <td styleCode="Botrule">09/13/2023</td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">
                                        ANDREA SMITH-HAWTHORNE, LMSW
            
              
                                        <paragraph> 238 Arsenal Street,   Watertown,  </paragraph>
                                        <paragraph> NY,   13601-2504, US </paragraph>
                                    </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">09/13/2023 13:20:47</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">
                                        <content ID="problem2">Finding related to health insurance issues</content>
                                    </td>
                                    <td styleCode="Botrule">419808006</td>
                                    <td styleCode="Botrule">Active</td>
                                    <td styleCode="Botrule">01/22/2025</td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">
                                        Jessica Jones
            
              
                                        <paragraph> null,   </paragraph>
                                        <paragraph/>
                                    </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">01/22/2025 15:24:24</td>
                                </tr>
                            </tbody>
                        </table>
                        <br/>
                        <br/>
                        <content>Problem Notes</content>
                        <paragraph>None recorded.</paragraph>
                    </text>
                    <entry>
                        <act classCode="ACT" moodCode="EVN">
                            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.3"/>
                            <templateId root="2.16.840.1.113883.**********.3"/>
                            <id root="b2efe439-157b-41ea-ab80-b658c6fb7a30"/>
                            <code code="CONC" codeSystem="2.16.840.1.113883.5.6" displayName="Concern"/>
                            <text>
                                <reference value="#problem0"/>
                            </text>
                            <statusCode code="completed"/>
                            <effectiveTime>
                                <low value="20211105000000-0500"/>
                            </effectiveTime>
                            <entryRelationship typeCode="SUBJ">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.4"/>
                                    <templateId root="2.16.840.1.113883.**********.4"/>
                                    <id root="7aeeef87-6c25-4192-b17b-b5d3e8c04700"/>
                                    <code code="55607006" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Problem">
                                        <translation code="75326-9" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Problem HL7.CCDAR2"/>
                                    </code>
                                    <statusCode code="completed"/>
                                    <effectiveTime>
                                        <low value="20211105000000-0500"/>
                                    </effectiveTime>
                                    <value code="46635009" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED" displayName="Type 1 diabetes mellitus" xsi:type="CD"/>
                                    <author>
                                        <templateId root="2.16.840.1.113883.**********.119"/>
                                        <time value="20211105133652-0500"/>
                                        <assignedAuthor>
                                            <id root="2.16.840.1.113883.4.6"/>
                                            <addr use="WP">
                                                <city nullFlavor="NI"/>
                                                <state nullFlavor="NI"/>
                                                <postalCode nullFlavor="NI"/>
                                                <country>US</country>
                                            </addr>
                                            <assignedPerson>
                                                <name>
                                                    <given>Jeanna Everard</given>
                                                    <family>Everard</family>
                                                </name>
                                            </assignedPerson>
                                            <representedOrganization>
                                                <id nullFlavor="UNK" root="c7d87ece-f20d-46b2-b452-b727dec0640f"/>
                                                <name>NY - NORTH COUNTRY FAMILY HEALTH CENTER</name>
                                                <telecom nullFlavor="NI"/>
                                            </representedOrganization>
                                        </assignedAuthor>
                                    </author>
                                </observation>
                            </entryRelationship>
                        </act>
                    </entry>
                    <entry>
                        <act classCode="ACT" moodCode="EVN">
                            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.3"/>
                            <templateId root="2.16.840.1.113883.**********.3"/>
                            <id root="1c7805e9-f6ed-4a13-afff-ecb632b8199c"/>
                            <code code="CONC" codeSystem="2.16.840.1.113883.5.6" displayName="Concern"/>
                            <text>
                                <reference value="#problem1"/>
                            </text>
                            <statusCode code="active"/>
                            <effectiveTime>
                                <low value="20230913000000-0500"/>
                            </effectiveTime>
                            <entryRelationship typeCode="SUBJ">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.4"/>
                                    <templateId root="2.16.840.1.113883.**********.4"/>
                                    <id root="3071d23a-2d90-4bff-8c7d-d048e7663c48"/>
                                    <code code="55607006" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Problem">
                                        <translation code="75326-9" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Problem HL7.CCDAR2"/>
                                    </code>
                                    <statusCode code="completed"/>
                                    <effectiveTime>
                                        <low value="20230913000000-0500"/>
                                    </effectiveTime>
                                    <value code="21897009" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED" displayName="Generalized anxiety disorder" xsi:type="CD">
                                        <translation code="F41.1" codeSystem="2.16.840.1.113883.6.90" codeSystemName="ICD10" displayName="Generalized anxiety disorder"/>
                                    </value>
                                    <author>
                                        <templateId root="2.16.840.1.113883.**********.119"/>
                                        <time value="20230913132047-0500"/>
                                        <assignedAuthor>
                                            <id extension="1790496941" root="2.16.840.1.113883.4.6"/>
                                            <addr use="WP">
                                                <streetAddressLine>238 Arsenal Street</streetAddressLine>
                                                <city>Watertown</city>
                                                <state>NY</state>
                                                <postalCode>13601-2504</postalCode>
                                                <country>US</country>
                                            </addr>
                                            <assignedPerson>
                                                <name>
                                                    <given>ANDREA SMITH-HAWTHORNE, LMSW</given>
                                                    <family>SMITH HAWTHORNE</family>
                                                </name>
                                            </assignedPerson>
                                            <representedOrganization>
                                                <id nullFlavor="UNK" root="96452b6b-dc83-4bd6-879f-53f330c01224"/>
                                                <name>NY - NORTH COUNTRY FAMILY HEALTH CENTER</name>
                                                <telecom nullFlavor="NI"/>
                                            </representedOrganization>
                                        </assignedAuthor>
                                    </author>
                                </observation>
                            </entryRelationship>
                        </act>
                    </entry>
                    <entry>
                        <act classCode="ACT" moodCode="EVN">
                            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.3"/>
                            <templateId root="2.16.840.1.113883.**********.3"/>
                            <id root="0a2bb004-ff16-42d6-bab4-89e50fd3b57d"/>
                            <code code="CONC" codeSystem="2.16.840.1.113883.5.6" displayName="Concern"/>
                            <text>
                                <reference value="#problem2"/>
                            </text>
                            <statusCode code="active"/>
                            <effectiveTime>
                                <low value="20250122000000-0500"/>
                            </effectiveTime>
                            <entryRelationship typeCode="SUBJ">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.4"/>
                                    <templateId root="2.16.840.1.113883.**********.4"/>
                                    <id root="02e3ec9f-18a7-4236-aeeb-9e6ddb01fd1b"/>
                                    <code code="55607006" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Problem">
                                        <translation code="75326-9" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Problem HL7.CCDAR2"/>
                                    </code>
                                    <statusCode code="completed"/>
                                    <effectiveTime>
                                        <low value="20250122000000-0500"/>
                                    </effectiveTime>
                                    <value code="419808006" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED" displayName="Finding related to health insurance issues" xsi:type="CD"/>
                                    <author>
                                        <templateId root="2.16.840.1.113883.**********.119"/>
                                        <time value="20250122152424-0500"/>
                                        <assignedAuthor>
                                            <id root="2.16.840.1.113883.4.6"/>
                                            <addr use="WP">
                                                <city nullFlavor="NI"/>
                                                <state nullFlavor="NI"/>
                                                <postalCode nullFlavor="NI"/>
                                                <country>US</country>
                                            </addr>
                                            <assignedPerson>
                                                <name>
                                                    <given>Jessica Jones</given>
                                                    <family>Jones</family>
                                                </name>
                                            </assignedPerson>
                                            <representedOrganization>
                                                <id nullFlavor="UNK" root="c8bf23b6-cc81-479b-8b0d-f2e50a3709d5"/>
                                                <name>NY - NORTH COUNTRY FAMILY HEALTH CENTER</name>
                                                <telecom nullFlavor="NI"/>
                                            </representedOrganization>
                                        </assignedAuthor>
                                    </author>
                                </observation>
                            </entryRelationship>
                        </act>
                    </entry>
                </section>
            </component>
            <component>
                <section>
                    <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.7.1"/>
                    <templateId root="2.16.840.1.113883.**********.7.1"/>
                    <id root="01538526-cfb8-4465-bae8-7feb6b3234e7"/>
                    <code code="47519-4" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"/>
                    <title>Procedures</title>
                    <text>
                        <content>Surgical History</content>
                        <table>
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Name</th>
                                    <th>Laterality</th>
                                    <th>Status</th>
                                    <th>Provider Name and Address</th>
                                    <th>Organization Details</th>
                                    <th>Recorded Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td styleCode="Botrule">01/01/2015</td>
                                    <td styleCode="Botrule">
                                        <content ID="Proc1">Total Hysterectomy</content>
                                    </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">completed</td>
                                    <td styleCode="Botrule">Jessica Jones
                    
                  </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">06/24/2022 16:00:48</td>
                                </tr>
                            </tbody>
                        </table>
                        <br/>
                        <content>Imaging Results</content>
                        <paragraph>None recorded.</paragraph>
                        <br/>
                        <content>Procedure Notes</content>
                        <paragraph>None recorded.</paragraph>
                    </text>
                    <entry>
                        <procedure classCode="PROC" moodCode="EVN">
                            <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.14"/>
                            <templateId root="2.16.840.1.113883.**********.14"/>
                            <id root="e9b98cbc-7f14-447b-9157-b7745c32f6c5"/>
                            <code code="116140006" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Total Hysterectomy"/>
                            <text>
                                <reference value="#Proc1"/>
                            </text>
                            <statusCode code="completed"/>
                            <!--        As per SPEC period timestamp is not expected here-->
                            <effectiveTime value="20150101"/>
                            <author>
                                <templateId root="2.16.840.1.113883.**********.119"/>
                                <time value="20220624160048-0500"/>
                                <assignedAuthor>
                                    <id root="2.16.840.1.113883.4.6"/>
                                    <assignedPerson>
                                        <name>
                                            <given>Jessica Jones</given>
                                            <family>Jones</family>
                                        </name>
                                    </assignedPerson>
                                    <representedOrganization>
                                        <id nullFlavor="UNK" root="9193f2d3-3a92-4291-8ed9-e47b19d031d5"/>
                                        <name>NY - NORTH COUNTRY FAMILY HEALTH CENTER</name>
                                        <telecom nullFlavor="NI"/>
                                    </representedOrganization>
                                </assignedAuthor>
                            </author>
                        </procedure>
                    </entry>
                    <entry>
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.13"/>
                            <templateId root="2.16.840.1.113883.**********.13"/>
                            <id root="f126edcb-01ec-400a-9d9f-0ad27983ab04"/>
                            <code nullFlavor="NI"/>
                            <statusCode code="active"/>
                            <effectiveTime nullFlavor="NI"/>
                            <value nullFlavor="NI" xsi:type="CD"/>
                        </observation>
                    </entry>
                </section>
            </component>
            <component>
                <section>
                    <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.23"/>
                    <templateId root="2.16.840.1.113883.**********.23"/>
                    <id root="fdf1de21-5ae7-4f61-89b2-828e88797516"/>
                    <code code="46264-8" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Medical Equipment"/>
                    <title>Medical Equipment</title>
                    <text>
      None Reported.
      </text>
                    <entry>
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.13"/>
                            <templateId root="2.16.840.1.113883.**********.13"/>
                            <id root="f597bcbc-b5e0-4848-bd64-96c02766d660"/>
                            <code nullFlavor="NI"/>
                            <statusCode code="active"/>
                            <effectiveTime nullFlavor="NI"/>
                            <value nullFlavor="NI" xsi:type="CD"/>
                        </observation>
                    </entry>
                </section>
            </component>
            <component>
                <section>
                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.6.1"/>
                    <templateId root="2.16.840.1.113883.**********.6.1"/>
                    <id root="062c1e98-679a-4132-927a-7f1ca049bf84"/>
                    <code code="48765-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Allergies, adverse reactions, alerts"/>
                    <title>Allergies</title>
                    <text>
                        <table>
                            <thead>
                                <tr>
                                    <th>Allergen ID</th>
                                    <th>Allergen Name</th>
                                    <th>Allergen Category</th>
                                    <th>Reaction</th>
                                    <th>Reaction Severity</th>
                                    <th>Criticality</th>
                                    <th>Documentation Date</th>
                                    <th>Start Date</th>
                                    <th>Code</th>
                                    <th>Code System</th>
                                    <th>Note</th>
                                    <th>Provider Name and Address</th>
                                    <th>Organization Details</th>
                                    <th>Recorded Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ID="allergy23653">
                                    <td styleCode="Botrule">
                                        <content>23653</content>
                                    </td>
                                    <td styleCode="Botrule">
                                        <content ID="allergy23653allergen">Product containing penicillin (product)</content>
                                    </td>
                                    <td styleCode="Botrule">medication</td>
                                    <td styleCode="Botrule">
                                        <content ID="allergy23653reaction0">dizziness</content>
                                        <br/>
                                    </td>
                                    <td styleCode="Botrule">
                                        <content ID="allergy23653severity0">Not available</content>
                                        <br/>
                                    </td>
                                    <td styleCode="Botrule">
                                        <content>Not available</content>
                                    </td>
                                    <td styleCode="Botrule">
                                        <content>11/05/2021</content>
                                    </td>
                                    <td styleCode="Botrule">
                                        <content/>
                                    </td>
                                    <td styleCode="Botrule">
                                        <content>890458001</content>
                                    </td>
                                    <td styleCode="Botrule">
                                        <content>SNOMED</content>
                                    </td>
                                    <td styleCode="Botrule">
                                        <content/>
                                    </td>
                                    <td styleCode="Botrule">
                                        Jeanna Everard
                
                  
                                        <paragraph> null,   </paragraph>
                                        <paragraph/>
                                    </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">11/05/2021 13:36:42</td>
                                </tr>
                            </tbody>
                        </table>
                    </text>
                    <entry>
                        <act classCode="ACT" moodCode="EVN">
                            <!-- Allergy Concern Act -->
                            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.30"/>
                            <templateId root="2.16.840.1.113883.**********.30"/>
                            <id root="6524bcf1-0556-49c3-8730-0827819b8cfc"/>
                            <code code="CONC" codeSystem="2.16.840.1.113883.5.6" codeSystemName="HL7ActClass" displayName="Concern"/>
                            <text>
                                <reference value="#allergy23653"/>
                            </text>
                            <statusCode code="active"/>
                            <effectiveTime>
                                <low nullFlavor="NI"/>
                                <high nullFlavor="UNK"/>
                            </effectiveTime>
                            <entryRelationship typeCode="SUBJ">
                                <observation classCode="OBS" moodCode="EVN">
                                    <!-- Allergy Intolerance -->
                                    <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.7"/>
                                    <templateId root="2.16.840.1.113883.**********.7"/>
                                    <id root="d7b5032e-3d27-4656-821b-67d36fef9a4b"/>
                                    <code code="ASSERTION" codeSystem="2.16.840.1.113883.5.4" codeSystemName="HL7ActCode"/>
                                    <statusCode code="completed"/>
                                    <effectiveTime>
                                        <low nullFlavor="NI"/>
                                    </effectiveTime>
                                    <value code="419199007" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Allergy to substance" xsi:type="CD"/>
                                    <author>
                                        <templateId root="2.16.840.1.113883.**********.119"/>
                                        <time value="20211105133642-0500"/>
                                        <assignedAuthor>
                                            <id root="2.16.840.1.113883.4.6"/>
                                            <addr use="WP">
                                                <city nullFlavor="NI"/>
                                                <state nullFlavor="NI"/>
                                                <postalCode nullFlavor="NI"/>
                                                <country>US</country>
                                            </addr>
                                            <assignedPerson>
                                                <name>
                                                    <given>Jeanna Everard</given>
                                                    <family>Everard</family>
                                                </name>
                                            </assignedPerson>
                                            <representedOrganization>
                                                <id nullFlavor="UNK" root="583be12b-6458-4ee6-80a1-19522defd5ab"/>
                                                <name>NY - NORTH COUNTRY FAMILY HEALTH CENTER</name>
                                                <telecom nullFlavor="NI"/>
                                            </representedOrganization>
                                        </assignedAuthor>
                                    </author>
                                    <participant typeCode="CSM">
                                        <participantRole classCode="MANU">
                                            <playingEntity classCode="MMAT">
                                                <code code="890458001" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED" displayName="Product containing penicillin (product)">
                                                    <originalText>
                                                        <reference value="#allergy23653allergen"/>
                                                    </originalText>
                                                </code>
                                            </playingEntity>
                                        </participantRole>
                                    </participant>
                                    <entryRelationship inversionInd="true" typeCode="SUBJ">
                                        <observation classCode="OBS" moodCode="EVN">
                                            <!-- Allergy Status observation -->
                                            <templateId extension="2019-06-20" root="2.16.840.1.113883.**********.28"/>
                                            <templateId root="2.16.840.1.113883.**********.28"/>
                                            <code code="33999-4" codeSystem="2.16.840.1.113883.6.1"/>
                                            <statusCode code="completed"/>
                                            <value code="55561003" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Active" xsi:type="CE"/>
                                        </observation>
                                    </entryRelationship>
                                    <entryRelationship inversionInd="true" typeCode="MFST">
                                        <observation classCode="OBS" moodCode="EVN">
                                            <!-- Reaction observation -->
                                            <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.9"/>
                                            <templateId root="2.16.840.1.113883.**********.9"/>
                                            <id root="89a93593-5b18-4946-855a-6a25d170960a"/>
                                            <code code="ASSERTION" codeSystem="2.16.840.1.113883.5.4" codeSystemName="ActCode" displayName="Assertion"/>
                                            <text>
                                                <reference value="#allergy23653reaction0"/>
                                            </text>
                                            <statusCode code="completed"/>
                                            <value code="404640003" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="dizziness" xsi:type="CD"/>
                                            <entryRelationship inversionInd="true" typeCode="SUBJ">
                                                <observation classCode="OBS" moodCode="EVN">
                                                    <!-- Severity observation -->
                                                    <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.8"/>
                                                    <templateId root="2.16.840.1.113883.**********.8"/>
                                                    <code code="SEV" codeSystem="2.16.840.1.113883.5.4" codeSystemName="HL7ActCode" displayName="Severity Observation"/>
                                                    <text>
                                                        <reference value="#allergy23653severity0"/>
                                                    </text>
                                                    <statusCode code="completed"/>
                                                    <value nullFlavor="NI" xsi:type="CD"/>
                                                </observation>
                                            </entryRelationship>
                                        </observation>
                                    </entryRelationship>
                                </observation>
                            </entryRelationship>
                        </act>
                    </entry>
                </section>
            </component>
            <component>
                <section nullFlavor="NI">
                    <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.1.1"/>
                    <templateId root="2.16.840.1.113883.**********.1.1"/>
                    <id root="4916203d-4fc3-41a1-a020-2dad7eba2e45"/>
                    <code code="10160-0" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="History of Medication Use"/>
                    <title>Medications</title>
                    <text>
                        <paragraph>
                            <content ID="med1">No information.</content>
                        </paragraph>
                    </text>
                    <entry>
                        <substanceAdministration classCode="SBADM" moodCode="EVN" negationInd="true">
                            <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.16"/>
                            <templateId root="2.16.840.1.113883.**********.16"/>
                            <id root="072f00fc-4f9d-4516-8d6f-ed00ed523fe0"/>
                            <statusCode code="active"/>
                            <effectiveTime xsi:type="IVL_TS">
                                <low nullFlavor="NI"/>
                            </effectiveTime>
                            <doseQuantity nullFlavor="NI"/>
                            <consumable>
                                <manufacturedProduct classCode="MANU">
                                    <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.23"/>
                                    <templateId root="2.16.840.1.113883.**********.23"/>
                                    <manufacturedMaterial>
                                        <code nullFlavor="NI">
                                            <originalText>
                                                <reference value="#med1"/>
                                            </originalText>
                                        </code>
                                    </manufacturedMaterial>
                                </manufacturedProduct>
                            </consumable>
                            <author>
                                <templateId root="2.16.840.1.113883.**********.119"/>
                                <time nullFlavor="NI"/>
                                <assignedAuthor>
                                    <id nullFlavor="UNK"/>
                                </assignedAuthor>
                            </author>
                        </substanceAdministration>
                    </entry>
                </section>
            </component>
            <component>
                <section>
                    <templateId root="1.3.6.1.4.1.19376.1.5.3.1.3.4"/>
                    <id root="d418184d-7907-4c92-a910-98354f15ce3a"/>
                    <code code="10164-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="History of Present Illness"/>
                    <title>History of Present Illness</title>
                    <text>None recorded.</text>
                </section>
            </component>
            <component>
                <section>
                    <templateId root="2.16.840.1.113883.10.20.2.10"/>
                    <id root="f4cdd90d-f625-4137-865b-5444c17acfef"/>
                    <code code="29545-1" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"/>
                    <title>Physical Exam</title>
                    <text>
                        <table>
                            <tbody>
                                <tr>
                                    <td/>
                                    <td>
                                        <content styleCode="Bold"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <content styleCode="Bold">Notes:</content>
                                    </td>
                                    <td>
                                        <content>
                  None recorded.
                </content>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </text>
                </section>
            </component>
            <component>
                <section>
                    <templateId root="1.3.6.1.4.1.19376.1.5.3.1.3.18"/>
                    <id root="97fc1ed0-6e15-434e-8b18-7241c4b6da85"/>
                    <code code="10187-3" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="REVIEW OF SYSTEMS"/>
                    <title>Review of Systems</title>
                    <text>None recorded.</text>
                </section>
            </component>
            <component>
                <section>
                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.4.1"/>
                    <id root="2d3d3d26-c180-4b99-b65b-c8da85f08ac0"/>
                    <code code="8716-3" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="VITAL SIGNS"/>
                    <title>Vitals</title>
                    <text>
                        <paragraph>
                            <content ID="vit1">None Recorded</content>
                        </paragraph>
                    </text>
                    <entry typeCode="DRIV">
                        <organizer classCode="CLUSTER" moodCode="EVN">
                            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.26"/>
                            <templateId root="2.16.840.1.113883.**********.26"/>
                            <id root="f00015b9-bf22-410d-a8f9-4d620365c26c"/>
                            <code code="46680005" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Vital signs">
                                <translation code="74728-7" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Vital signs, weight, height, head circumference, oximetry, BMI, and BSA panel"/>
                            </code>
                            <statusCode code="completed"/>
                            <effectiveTime nullFlavor="NI"/>
                            <component>
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.27"/>
                                    <templateId root="2.16.840.1.113883.**********.27"/>
                                    <id root="72b2db72-4ebd-4f5e-b87a-6cbe49edd7c5"/>
                                    <code nullFlavor="NI"/>
                                    <text>
                                        <reference value="#vit1"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime nullFlavor="NI"/>
                                    <value xsi:type="PQ"/>
                                </observation>
                            </component>
                        </organizer>
                    </entry>
                </section>
            </component>
            <component>
                <section>
                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.17"/>
                    <templateId root="2.16.840.1.113883.**********.17"/>
                    <id root="2d423ef6-9b13-477d-b133-475e936c0d77"/>
                    <code code="29762-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"/>
                    <title>Social History</title>
                    <text>
                        <br/>
                        <table>
                            <thead>
                                <tr>
                                    <th>Social History Observation</th>
                                    <th>Description</th>
                                    <th>Date Observed</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td styleCode="Botrule">
                                        <content ID="BirthSexInfo">Birth Sex</content>
                                    </td>
                                    <td styleCode="Botrule">Unknown</td>
                                    <td styleCode="Botrule">09/05/2025</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">Gender Identity</td>
                                    <td styleCode="Botrule">Identifies as male</td>
                                    <td styleCode="Botrule">09/05/2025</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">Sexual orientation</td>
                                    <td styleCode="Botrule">Straight or heterosexual</td>
                                    <td styleCode="Botrule">09/13/2023</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">Legal Sex</td>
                                    <td styleCode="Botrule">Female</td>
                                    <td styleCode="Botrule">09/05/2025</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">Pregnancy Status</td>
                                    <td styleCode="Botrule">Not pregnant (finding)</td>
                                    <td styleCode="Botrule">09/05/2025</td>
                                </tr>
                            </tbody>
                        </table>
                        <br/>
                        <table>
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Assessment</th>
                                    <th>Value</th>
                                    <th>LastModified by</th>
                                    <th>Organization Details</th>
                                    <th>LastModified Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialscreener140287">NYS Accountable Health Communities (AHC) Health-Related Social Needs (HRSN) Screening Tool</content>
                                    </td>
                                    <td/>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839520">Please note: This screening questionnaire is intended to be used for the New York State’s Health Equity Reform (NYHER) 1115 Waiver Amendment.</content>
                                    </td>
                                    <td/>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839521">We use this survey to understand needs our [Members / patients / clients] have which could interfere with good health. We may share your answers with your other healthcare providers, and with your health plan and social services organizations, so they can determine if you qualify for any free non-medical services that could be helpful. Please check this box if you agree to continue. You can choose not to answer this survey, but we can only check for services if you do answer. You can choose to be screened later and may be eligible for extra services at that time. None of this will affect your ongoing Medicaid eligibility.</content>
                                    </td>
                                    <td/>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839522">Housing and Utilities</content>
                                    </td>
                                    <td/>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839523">1. What is your living situation today?</content>
                                    </td>
                                    <td>I have a steady place to live</td>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839524">2. Think about the place you live. Do you have problems with any of the following? &lt;content&gt; CHOOSE ALL THAT APPLY &lt;/content&gt;</content>
                                    </td>
                                    <td/>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839525">Pests such as bugs, ants, or mice</content>
                                    </td>
                                    <td>Yes*</td>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839526">Mold</content>
                                    </td>
                                    <td>Yes*</td>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839527">Lead paint or pipes</content>
                                    </td>
                                    <td>Yes*</td>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839528">Lack of heat</content>
                                    </td>
                                    <td>Yes*</td>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839529">Oven or stove not working</content>
                                    </td>
                                    <td>Yes*</td>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839530">Smoke detectors missing or not working</content>
                                    </td>
                                    <td>Yes*</td>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839531">Water leaks</content>
                                    </td>
                                    <td>Yes*</td>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839532">None of the above</content>
                                    </td>
                                    <td/>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839533">3. In the past 12 months has the electric, gas, oil, or water company threatened to shut off services in your home?</content>
                                    </td>
                                    <td>Yes*</td>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839534">Food &lt;content&gt; Some people have made the following statements about their food situation. Please answer whether the statements were OFTEN, SOMETIMES, or NEVER true for you and your household in the last 12 months. &lt;/content&gt;</content>
                                    </td>
                                    <td/>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839535">4. Within the past 12 months, you worried that your food would run out before you got money to buy more.</content>
                                    </td>
                                    <td>Often true*</td>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839536">5. Within the past 12 months, the food you bought just didn't last and you didn't have money to get more.</content>
                                    </td>
                                    <td>Often true*</td>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839537">Transportation</content>
                                    </td>
                                    <td/>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839538">6. In the past 12 months, has lack of reliable transportation kept you from medical appointments, meetings, work or from getting things needed for daily living?</content>
                                    </td>
                                    <td>Yes*</td>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839539">Employment</content>
                                    </td>
                                    <td/>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839540">7. Do you want help finding or keeping work or a job?</content>
                                    </td>
                                    <td>Yes, help finding work*</td>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839541">Education</content>
                                    </td>
                                    <td/>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839542">8. Do you want help with school or training? For example, starting or completing job training or getting a high school diploma, GED or equivalent</content>
                                    </td>
                                    <td>Yes*</td>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839543">Safety</content>
                                    </td>
                                    <td/>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839544">9. How often does anyone, including family and friends, physically hurt you?</content>
                                    </td>
                                    <td>Never</td>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839545">10. How often does anyone, including family and friends, insult or talk down to you?</content>
                                    </td>
                                    <td>Never</td>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839546">11. How often does anyone, including family and friends, threaten you with harm?</content>
                                    </td>
                                    <td>Never</td>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839547">12. How often does anyone, including family and friends, scream or curse at you?</content>
                                    </td>
                                    <td>Never</td>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839548">Practice Staff Only Questions:</content>
                                    </td>
                                    <td/>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839549">Was screening conducted during direct interaction with the Member?</content>
                                    </td>
                                    <td/>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td>09/05/2025</td>
                                    <td>
                                        <content ID="socialassessment1839550">How many minutes of direct patient interaction did you have with the Member while administering the screening?</content>
                                    </td>
                                    <td/>
                                    <td>Not Available</td>
                                    <td>Not Available</td>
                                    <td>09/05/2025 12:36:02</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">09/05/2025</td>
                                    <td styleCode="Botrule">
                                        <content ID="socialassessment1839551">Is this screening covered under other funding? 
  &lt;content&gt; If "yes" document in the notes field at the bottom of the screener. &lt;/content&gt;</content>
                                    </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">Not Available</td>
                                    <td styleCode="Botrule">Not Available</td>
                                    <td styleCode="Botrule">09/05/2025 12:36:02</td>
                                </tr>
                            </tbody>
                        </table>
                        <paragraph>No social history SDOH screeners recorded</paragraph>
                    </text>
                    <entry>
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.78"/>
                            <templateId root="2.16.840.1.113883.**********.78"/>
                            <id root="0703225e-2590-424e-8c57-ab1fce9b3fda"/>
                            <code code="72166-2" codeSystem="2.16.840.1.113883.6.1"/>
                            <statusCode code="completed"/>
                            <effectiveTime nullFlavor="NI"/>
                            <value code="266927001" codeSystem="2.16.840.1.113883.6.96" displayName="Unknown If Ever Smoked" xsi:type="CD"/>
                        </observation>
                    </entry>
                    <entry>
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId extension="2016-06-01" root="2.16.840.1.113883.**********.200"/>
                            <code code="76689-9" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Sex Assigned At Birth"/>
                            <statusCode code="completed"/>
                            <effectiveTime value="20250905"/>
                            <value codeSystem="2.16.840.1.113883.5.1" displayName="unknown" nullFlavor="UNK" xsi:type="CD"/>
                        </observation>
                    </entry>
                    <entry typeCode="DRIV">
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.38"/>
                            <templateId extension="2022-06-01" root="2.16.840.1.113883.10.20.34.3.45"/>
                            <id root="94a916d5-30eb-4bcb-8788-956b7bd49ce7"/>
                            <code code="76691-5" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Gender Identity"/>
                            <statusCode code="completed"/>
                            <effectiveTime>
                                <low value="20250905"/>
                            </effectiveTime>
                            <value code="446151000124109" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Identifies as Male" xsi:type="CD"/>
                        </observation>
                    </entry>
                    <entry typeCode="DRIV">
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId root="2.16.840.1.113883.**********.38"/>
                            <templateId extension="2023-05-01" root="2.16.840.1.113883.**********.501"/>
                            <id root="3167d3aa-787d-4bd0-9a41-4b02bfc05e08"/>
                            <code code="76690-7" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Sexual Orientation"/>
                            <statusCode code="completed"/>
                            <effectiveTime>
                                <low value="20230913082400-0500"/>
                            </effectiveTime>
                            <value displayName="Straight or heterosexual" nullFlavor="UNK" xsi:type="CD"/>
                        </observation>
                    </entry>
                    <entry>
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId extension="2023-06-28" root="2.16.840.1.113883.**********.507"/>
                            <id root="324484c4-bc03-495d-b19a-31874d177936"/>
                            <code code="46098-0" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Sex"/>
                            <statusCode code="completed"/>
                            <effectiveTime value="20250905"/>
                            <value code="248152002" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Female" xsi:type="CD"/>
                        </observation>
                    </entry>
                    <entry>
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId root="2.16.840.1.113883.10.20.15.3.8"/>
                            <id extension="123456789" root="2.16.840.1.113883.19"/>
                            <code code="ASSERTION" codeSystem="2.16.840.1.113883.5.4"/>
                            <statusCode code="completed"/>
                            <effectiveTime>
                                <low value="20250905000000-0500"/>
                                <high nullFlavor="NI"/>
                            </effectiveTime>
                            <value code="60001007" codeSystem="2.16.840.1.113883.6.96" displayName="Not Pregnant (finding)" xsi:type="CD"/>
                        </observation>
                    </entry>
                    <entry typeCode="DRIV">
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.69"/>
                            <id root="055f8d9e-20e0-42eb-b297-6ddd13f4c3d9"/>
                            <code code="273601006" codeSystem="2.16.840.1.113883.6.1" codeSystemName="SNOMED2"/>
                            <statusCode code="completed"/>
                            <effectiveTime value="20250905123602-0400"/>
                            <value xsi:type="CD"/>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="59bf2c66-b8ab-4ea6-900c-7004483ed890"/>
                                    <code nullFlavor="NI"/>
                                    <text>
                                        <reference value="#socialscreener140287"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="b7ba973a-fdb8-4f1d-8120-dc53a162d8dd"/>
                                    <code displayName="Please note: This screening questionnaire is intended to be used for the New York State’s Health Equity Reform (NYHER) 1115 Waiver Amendment." nullFlavor="NI"/>
                                    <text>
                                        <reference value="#socialassessment1839520"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="e2a1e196-a7af-4783-8197-2a31000bd68f"/>
                                    <code displayName="We use this survey to understand needs our [Members / patients / clients] have which could interfere with good health. We may share your answers with your other healthcare providers, and with your health plan and social services organizations, so they can determine if you qualify for any free non-medical services that could be helpful. Please check this box if you agree to continue. You can choose not to answer this survey, but we can only check for services if you do answer. You can choose to be screened later and may be eligible for extra services at that time. None of this will affect your ongoing Medicaid eligibility." nullFlavor="NI"/>
                                    <text>
                                        <reference value="#socialassessment1839521"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="8db99d4a-ecd4-41b8-b1dc-7e351aab0c7f"/>
                                    <code displayName="Housing and Utilities" nullFlavor="NI"/>
                                    <text>
                                        <reference value="#socialassessment1839522"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="c6a74afc-5782-47e0-84b8-08e143d27c91"/>
                                    <code code="71802-3" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Housing status"/>
                                    <text>
                                        <reference value="#socialassessment1839523"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value code="LA31993-1" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="I have a steady place to live" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="b992eb63-3097-4ef8-8948-f60e3c170266"/>
                                    <code displayName="2. Think about the place you live. Do you have problems with any of the following?    &lt;content&gt; CHOOSE ALL THAT APPLY &lt;/content&gt;" nullFlavor="NI"/>
                                    <text>
                                        <reference value="#socialassessment1839524"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="ab9e7696-7401-4582-8b61-b0c63336e66a"/>
                                    <code code="96778-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Problems with place where you live"/>
                                    <text>
                                        <reference value="#socialassessment1839525"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value code="LA31996-4" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Yes*" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="5a1b363f-6c04-45bf-ab6a-fd79d577e328"/>
                                    <code code="96778-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Problems with place where you live"/>
                                    <text>
                                        <reference value="#socialassessment1839526"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value code="LA28580-1" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Yes*" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="c6011523-2654-43c4-bcd3-7a4411dd04d7"/>
                                    <code code="96778-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Problems with place where you live"/>
                                    <text>
                                        <reference value="#socialassessment1839527"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value code="LA31997-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Yes*" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="ed8e2115-1023-4c10-9ede-85523fd4b568"/>
                                    <code code="96778-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Problems with place where you live"/>
                                    <text>
                                        <reference value="#socialassessment1839528"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value code="LA31998-0" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Yes*" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="5c4e341c-178b-48e1-b964-a0958456e6a5"/>
                                    <code code="96778-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Problems with place where you live"/>
                                    <text>
                                        <reference value="#socialassessment1839529"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value code="LA31999-8" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Yes*" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="6d9f308e-996f-4613-b24f-3e53b9e70b5f"/>
                                    <code code="96778-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Problems with place where you live"/>
                                    <text>
                                        <reference value="#socialassessment1839530"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value code="LA32000-4" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Yes*" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="bdef8724-ccdb-49aa-9bb5-6ba1f1166e09"/>
                                    <code code="96778-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Problems with place where you live"/>
                                    <text>
                                        <reference value="#socialassessment1839531"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value code="LA32001-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Yes*" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="268dd391-684b-4be0-a410-054beb2135d4"/>
                                    <code code="96778-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Problems with place where you live"/>
                                    <text>
                                        <reference value="#socialassessment1839532"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="bd1ec93a-e96b-4652-940a-2ec4d525992d"/>
                                    <code code="96779-4" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Has the electric, gas, oil, or water company threatened to shut off services in your home in past 12 months"/>
                                    <text>
                                        <reference value="#socialassessment1839533"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value code="LA33-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Yes*" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="4617a830-484b-45c6-9d3b-c1b7518e6371"/>
                                    <code displayName="Food    &lt;content&gt; Some people have made the following statements about their food situation. Please answer whether the statements were OFTEN, SOMETIMES, or NEVER true for you and your household in the last 12 months. &lt;/content&gt;" nullFlavor="NI"/>
                                    <text>
                                        <reference value="#socialassessment1839534"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="0901153f-4414-46fc-8e45-5bd0b0a54999"/>
                                    <code code="88122-7" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Within the past 12 months we worried whether our food would run out before we got money to buy more"/>
                                    <text>
                                        <reference value="#socialassessment1839535"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value code="LA28397-0" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Often true*" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="3f021d9a-d8a2-448f-ade6-bd72487eb9a8"/>
                                    <code code="88123-5" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Within the past 12 months the food we bought just didn't last and we didn't have money to get more"/>
                                    <text>
                                        <reference value="#socialassessment1839536"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value code="LA28397-0" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Often true*" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="6d1c1b1f-4cb4-494e-9992-de0e6e8bd5d4"/>
                                    <code displayName="Transportation" nullFlavor="NI"/>
                                    <text>
                                        <reference value="#socialassessment1839537"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="8ef163ed-1678-4a00-bd1a-8cb3216647de"/>
                                    <code code="93030-5" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Has lack of transportation kept you from medical appointments, meetings, work, or from getting things needed for daily living"/>
                                    <text>
                                        <reference value="#socialassessment1839538"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value code="LA33-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Yes*" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="7bb6f0f1-124d-41f9-a891-36ca545899a5"/>
                                    <code displayName="Employment" nullFlavor="NI"/>
                                    <text>
                                        <reference value="#socialassessment1839539"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="b634bf3e-fc25-4c3b-8c79-7fd1525a3b02"/>
                                    <code code="96780-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Wants help finding or keeping work or a job"/>
                                    <text>
                                        <reference value="#socialassessment1839540"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value code="LA31981-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Yes, help finding work*" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="3eae0dcf-0f05-4ac0-81a2-c4221de1f159"/>
                                    <code displayName="Education" nullFlavor="NI"/>
                                    <text>
                                        <reference value="#socialassessment1839541"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="e7c68774-8df9-415a-9444-7d334791dc84"/>
                                    <code code="96782-8" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Wants help with school or training"/>
                                    <text>
                                        <reference value="#socialassessment1839542"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value code="LA33-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Yes*" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="9adfe2a9-c97d-4682-9c09-f6303c23d82e"/>
                                    <code displayName="Safety" nullFlavor="NI"/>
                                    <text>
                                        <reference value="#socialassessment1839543"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="3b21fee0-e284-4136-9382-7d2c0d878b42"/>
                                    <code code="95618-5" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Physically hurt you"/>
                                    <text>
                                        <reference value="#socialassessment1839544"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value code="LA6270-8" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Never" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="692bc186-5d0e-46c9-81f9-df6a690796d7"/>
                                    <code code="95617-7" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Insult you or talk down to you"/>
                                    <text>
                                        <reference value="#socialassessment1839545"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value code="LA6270-8" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Never" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="374e2270-76f1-4b3e-9f6a-396329f5f778"/>
                                    <code code="95616-9" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Threaten you with physical harm"/>
                                    <text>
                                        <reference value="#socialassessment1839546"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value code="LA6270-8" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Never" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="be983306-fdbf-4a3b-8ac4-aa42909f0d41"/>
                                    <code code="95615-1" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Scream or curse at you"/>
                                    <text>
                                        <reference value="#socialassessment1839547"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value code="LA6270-8" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Never" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="14eb3203-340f-4157-b785-4b6f4914cd54"/>
                                    <code displayName="Practice Staff Only Questions:" nullFlavor="NI"/>
                                    <text>
                                        <reference value="#socialassessment1839548"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="547e44f3-9aa5-4254-a3f1-c9dbc5efee72"/>
                                    <code displayName="Was screening conducted during direct interaction with the Member?" nullFlavor="NI"/>
                                    <text>
                                        <reference value="#socialassessment1839549"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="a806981b-9649-427c-b05e-c1bf1feff0bf"/>
                                    <code displayName="How many minutes of direct patient interaction did you have with the Member while administering the screening?" nullFlavor="NI"/>
                                    <text>
                                        <reference value="#socialassessment1839550"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="4fb3ae62-13c7-471d-b19f-864705d5d56c"/>
                                    <code displayName="Is this screening covered under other funding?    &lt;content&gt; If &quot;yes&quot; document in the notes field at the bottom of the screener. &lt;/content&gt;" nullFlavor="NI"/>
                                    <text>
                                        <reference value="#socialassessment1839551"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20250905123602-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                        </observation>
                    </entry>
                </section>
            </component>
            <component>
                <section>
                    <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.14"/>
                    <templateId root="2.16.840.1.113883.**********.14"/>
                    <id root="1e24cf35-d880-4ac7-b803-94e53764eef9"/>
                    <code code="47420-5" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"/>
                    <title>Functional Status</title>
                    <text>
                                
          None recorded.
        
        
                        <table>
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Assessment</th>
                                    <th>Value</th>
                                    <th>LastModified by</th>
                                    <th>Organization Details</th>
                                    <th>LastModified Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="screener91692">
                      CAGE-AID</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184274">Have you ever felt you ought to cut down on your drinking or drug use?</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184276">Have people annoyed you by criticizing your drinking or drug use?</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184275">Have you felt bad or guilty about your drinking or drug use?</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">03/21/2024</td>
                                    <td styleCode="Botrule">
                                        <content ID="assessment1184273">Have you ever had a drink or used drugs first thing in the morning to steady your nerves or to get rid of a hangover (eye-opener)?</content>
                                    </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">Maureen Burns-Gorman RN
                        
                      </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="screener91693">
                      CDC STEADI Fall Risk</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184278">Have you fallen in the past year?</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184284">Do you use or have you been advised to use a cane or walker to get around safely?</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184280">Do you sometimes feel unsteady while walking?</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184286">Do you steady yourself by holding onto furniture when walking at home?</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184277">Do you worry about falling?</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184283">Do you need to push with your hands to stand up from a chair?</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184285">Do you have trouble stepping up onto a curb?</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184282">Do you often have to rush to the toilet?</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184288">Have you lost some feeling in your feet?</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184279">Do you take medicine that sometimes makes you light-headed or more tired than usual?</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184287">Do you take medicine to help you sleep or improve your mood?</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">03/21/2024</td>
                                    <td styleCode="Botrule">
                                        <content ID="assessment1184281">Do you often feel sad or depressed?</content>
                                    </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">Maureen Burns-Gorman RN
                        
                      </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="disabilityscreener91694">
                      Katz index of independence in activities of daily living</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184293">Bathing</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184291">Dressing</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184289">Toileting</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184292">Transferring</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184290">Continence</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                        
                      </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">03/21/2024</td>
                                    <td styleCode="Botrule">
                                        <content ID="assessment1184294">Feeding</content>
                                    </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">Maureen Burns-Gorman RN
                        
                      </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">03/21/2024 14:37:11</td>
                                </tr>
                            </tbody>
                        </table>
                        <br/>
                        <paragraph>No Functional SDOH screeners recorded</paragraph>
                    </text>
                    <entry typeCode="DRIV">
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId root="2.16.840.1.113883.**********.69"/>
                            <id root="86de1af1-2d42-4a4a-bfaa-4821d246aa4d"/>
                            <code code="370854007" codeSystem="2.16.840.1.113883.6.1" codeSystemName="SNOMED2"/>
                            <statusCode code="completed"/>
                            <effectiveTime value="20240321143711-0400"/>
                            <value xsi:type="CD"/>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="7a0dbc27-e524-42a7-a1ef-a4917472feaf"/>
                                    <code nullFlavor="NI"/>
                                    <text>
                                        <reference value="#screener91692"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20240321143711-0400"/>
                                    <value xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                        </observation>
                    </entry>
                    <entry typeCode="DRIV">
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId root="2.16.840.1.113883.**********.69"/>
                            <id root="92a54ab8-1289-4ee5-bfab-874315a70c11"/>
                            <code code="414191008" codeSystem="2.16.840.1.113883.6.1" codeSystemName="SNOMED2"/>
                            <statusCode code="completed"/>
                            <effectiveTime value="20240321143711-0400"/>
                            <value xsi:type="CD"/>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="a5887880-bb01-44e8-b423-deccfc6319c3"/>
                                    <code nullFlavor="NI"/>
                                    <text>
                                        <reference value="#screener91693"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20240321143711-0400"/>
                                    <value xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                        </observation>
                    </entry>
                    <entry typeCode="DRIV">
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId extension="2023-05-01" root="2.16.840.1.113883.**********.505"/>
                            <id root="3553879d-9d18-4f09-9ae4-ebafe09b992a"/>
                            <code code="89571-4" codeSystem="2.16.840.1.113883.6.1" displayName="Disability Status [CUBS]"/>
                            <statusCode code="completed"/>
                            <effectiveTime value="20240321143711-0400"/>
                            <value nullFlavor="NA" xsi:type="CD"/>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="216fdbe5-85e5-4893-a8af-2bdbc5959163"/>
                                    <code nullFlavor="NI"/>
                                    <text>
                                        <reference value="#disabilityscreener91694"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20240321143711-0400"/>
                                    <value xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="1236b594-f119-4ddc-8100-760a495e53a0"/>
                                    <code displayName="Bathing" nullFlavor="NI"/>
                                    <text>
                                        <reference value="#disabilityassessment1184293"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20240321143711-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="17685c0c-1d8f-4e9c-9e19-3eebe9dd995f"/>
                                    <code displayName="Dressing" nullFlavor="NI"/>
                                    <text>
                                        <reference value="#disabilityassessment1184291"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20240321143711-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="7efaa4dc-0cb5-4969-a1d3-4623dee40319"/>
                                    <code displayName="Toileting" nullFlavor="NI"/>
                                    <text>
                                        <reference value="#disabilityassessment1184289"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20240321143711-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="43eef72b-bb15-495b-90f9-dcba1ca0d0a4"/>
                                    <code displayName="Transferring" nullFlavor="NI"/>
                                    <text>
                                        <reference value="#disabilityassessment1184292"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20240321143711-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="42c5f938-2511-404f-8dc9-b9ff38094faa"/>
                                    <code displayName="Continence" nullFlavor="NI"/>
                                    <text>
                                        <reference value="#disabilityassessment1184290"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20240321143711-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="72acdcd6-c964-4ad2-9cb0-348510ffb69f"/>
                                    <code displayName="Feeding" nullFlavor="NI"/>
                                    <text>
                                        <reference value="#disabilityassessment1184294"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20240321143711-0400"/>
                                    <value nullFlavor="NI" xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                        </observation>
                    </entry>
                </section>
            </component>
            <component>
                <section>
                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.56"/>
                    <templateId root="2.16.840.1.113883.**********.56"/>
                    <id root="6c1bb327-b612-4849-8c71-c59b75be3bde"/>
                    <code code="10190-7" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"/>
                    <title>Mental Status</title>
                    <text>
                              
        None recorded.
      
      
                        <table>
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Assessment</th>
                                    <th>Value</th>
                                    <th>LastModified by</th>
                                    <th>Organization Details</th>
                                    <th>LastModified Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>12/09/2022</td>
                                    <td>
                                        <content ID="screener50672">
                    Generalized Anxiety Disorder</content>
                                    </td>
                                    <td>16</td>
                                    <td>MICHELE SMITHERS, LMSW
                    
                  </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>12/09/2022 14:24:16</td>
                                </tr>
                                <tr>
                                    <td>12/09/2022</td>
                                    <td>
                                        <content ID="assessment642640">Feeling nervous, anxious or on edge</content>
                                    </td>
                                    <td/>
                                    <td>MICHELE SMITHERS, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>12/09/2022 14:24:16</td>
                                </tr>
                                <tr>
                                    <td>12/09/2022</td>
                                    <td>
                                        <content ID="assessment642641">Not being able to stop or control worrying</content>
                                    </td>
                                    <td/>
                                    <td>MICHELE SMITHERS, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>12/09/2022 14:24:16</td>
                                </tr>
                                <tr>
                                    <td>12/09/2022</td>
                                    <td>
                                        <content ID="assessment642642">Worrying too much about different things</content>
                                    </td>
                                    <td/>
                                    <td>MICHELE SMITHERS, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>12/09/2022 14:24:16</td>
                                </tr>
                                <tr>
                                    <td>12/09/2022</td>
                                    <td>
                                        <content ID="assessment642643">Trouble relaxing</content>
                                    </td>
                                    <td/>
                                    <td>MICHELE SMITHERS, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>12/09/2022 14:24:16</td>
                                </tr>
                                <tr>
                                    <td>12/09/2022</td>
                                    <td>
                                        <content ID="assessment642644">Being so restless that it is hard to sit still</content>
                                    </td>
                                    <td/>
                                    <td>MICHELE SMITHERS, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>12/09/2022 14:24:16</td>
                                </tr>
                                <tr>
                                    <td>12/09/2022</td>
                                    <td>
                                        <content ID="assessment642645">Becoming easily annoyed or irritable</content>
                                    </td>
                                    <td/>
                                    <td>MICHELE SMITHERS, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>12/09/2022 14:24:16</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">12/09/2022</td>
                                    <td styleCode="Botrule">
                                        <content ID="assessment642646">Feeling afraid as if something awful might happen</content>
                                    </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">MICHELE SMITHERS, LMSW
                      
                    </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">12/09/2022 14:24:16</td>
                                </tr>
                                <tr>
                                    <td>12/09/2022</td>
                                    <td>
                                        <content ID="screener50673">
                    Patient Health Questionnaire</content>
                                    </td>
                                    <td>18</td>
                                    <td>MICHELE SMITHERS, LMSW
                    
                  </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>12/09/2022 14:25:41</td>
                                </tr>
                                <tr>
                                    <td>12/09/2022</td>
                                    <td>
                                        <content ID="assessment642647">Little interest or pleasure in doing things</content>
                                    </td>
                                    <td/>
                                    <td>MICHELE SMITHERS, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>12/09/2022 14:25:41</td>
                                </tr>
                                <tr>
                                    <td>12/09/2022</td>
                                    <td>
                                        <content ID="assessment642648">Feeling down, depressed, or hopeless</content>
                                    </td>
                                    <td/>
                                    <td>MICHELE SMITHERS, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>12/09/2022 14:25:41</td>
                                </tr>
                                <tr>
                                    <td>12/09/2022</td>
                                    <td>
                                        <content ID="assessment642649">Trouble falling or staying asleep, or sleeping too much</content>
                                    </td>
                                    <td/>
                                    <td>MICHELE SMITHERS, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>12/09/2022 14:25:41</td>
                                </tr>
                                <tr>
                                    <td>12/09/2022</td>
                                    <td>
                                        <content ID="assessment642650">Feeling tired or having little energy</content>
                                    </td>
                                    <td/>
                                    <td>MICHELE SMITHERS, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>12/09/2022 14:25:41</td>
                                </tr>
                                <tr>
                                    <td>12/09/2022</td>
                                    <td>
                                        <content ID="assessment642651">Poor appetite or overeating</content>
                                    </td>
                                    <td/>
                                    <td>MICHELE SMITHERS, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>12/09/2022 14:25:41</td>
                                </tr>
                                <tr>
                                    <td>12/09/2022</td>
                                    <td>
                                        <content ID="assessment642652">Feeling bad about yourself - or that you are a failure or have let yourself or your family down</content>
                                    </td>
                                    <td/>
                                    <td>MICHELE SMITHERS, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>12/09/2022 14:25:41</td>
                                </tr>
                                <tr>
                                    <td>12/09/2022</td>
                                    <td>
                                        <content ID="assessment642653">Trouble concentrating on things, such as reading the newspaper or watching television</content>
                                    </td>
                                    <td/>
                                    <td>MICHELE SMITHERS, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>12/09/2022 14:25:41</td>
                                </tr>
                                <tr>
                                    <td>12/09/2022</td>
                                    <td>
                                        <content ID="assessment642654">Moving or speaking so slowly that other people could have noticed? Or the opposite - being so fidgety or restless that you have been moving around a lot more than usual</content>
                                    </td>
                                    <td/>
                                    <td>MICHELE SMITHERS, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>12/09/2022 14:25:41</td>
                                </tr>
                                <tr>
                                    <td>12/09/2022</td>
                                    <td>
                                        <content ID="assessment642655">Thoughts that you would be better off dead or of hurting yourself in some way</content>
                                    </td>
                                    <td/>
                                    <td>MICHELE SMITHERS, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>12/09/2022 14:25:41</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">12/09/2022</td>
                                    <td styleCode="Botrule">
                                        <content ID="assessment642656">If you checked off any problems, how difficult have these problems made it for you to do your work, take care of things at home, or get along with other people?</content>
                                    </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">MICHELE SMITHERS, LMSW
                      
                    </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">12/09/2022 14:25:41</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="screener75196">
                    Adverse Childhood Experience Questionnaire for Adults</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                    
                  </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 07:54:33</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment957213">Instructions: Below is a list of 10 categories of Adverse Childhood Experiences (ACEs). From the list below, please select "yes" for each ACE category that you experienced prior to your 18th birthday.</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 07:54:33</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment957214">1. Did you feel that you didn?t have enough to eat, had to wear dirty clothes, or had no one to protect or take care of you?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 07:54:33</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment957215">2. Did you lose a parent through divorce, abandonment, death, or other reason?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 07:54:33</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment957216">3. Did you live with anyone who was depressed, mentally ill, or attempted suicide?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 07:54:33</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment957217">4. Did you live with anyone who had a problem with drinking or using drugs, including prescription drugs?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 07:54:33</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment957218">5. Did your parents or adults in your home ever hit, punch, beat, or threaten to harm each other?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 07:54:33</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment957219">6. Did you live with anyone who went to jail or prison?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 07:54:33</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment957220">7. Did a parent or adult in your home ever swear at you, insult you, or put you down?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 07:54:33</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment957221">8. Did a parent or adult in your home ever hit, beat, kick, or physically hurt you in any way?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 07:54:33</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment957222">9. Did you feel that no one in your family loved you or thought you were special?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 07:54:33</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment957223">10. Did you experience unwanted sexual contact (such as fondling or oral/anal/vaginal intercourse/penetration)?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 07:54:33</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">09/13/2023</td>
                                    <td styleCode="Botrule">
                                        <content ID="assessment957224">11. Do you believe that these experiences have affected your health?</content>
                                    </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">Jessica Jones
                      
                    </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">09/13/2023 07:54:33</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="screener75290">
                    Adverse Childhood Experience Questionnaire for Adults</content>
                                    </td>
                                    <td>8</td>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                    
                  </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:04:47</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958517">Instructions: Below is a list of 10 categories of Adverse Childhood Experiences (ACEs). From the list below, please select "yes" for each ACE category that you experienced prior to your 18th birthday.</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:04:47</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958518">1. Did you feel that you didn?t have enough to eat, had to wear dirty clothes, or had no one to protect or take care of you?</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:04:47</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958519">2. Did you lose a parent through divorce, abandonment, death, or other reason?</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:04:47</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958520">3. Did you live with anyone who was depressed, mentally ill, or attempted suicide?</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:04:47</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958521">4. Did you live with anyone who had a problem with drinking or using drugs, including prescription drugs?</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:04:47</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958522">5. Did your parents or adults in your home ever hit, punch, beat, or threaten to harm each other?</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:04:47</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958523">6. Did you live with anyone who went to jail or prison?</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:04:47</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958524">7. Did a parent or adult in your home ever swear at you, insult you, or put you down?</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:04:47</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958525">8. Did a parent or adult in your home ever hit, beat, kick, or physically hurt you in any way?</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:04:47</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958526">9. Did you feel that no one in your family loved you or thought you were special?</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:04:47</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958527">10. Did you experience unwanted sexual contact (such as fondling or oral/anal/vaginal intercourse/penetration)?</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:04:47</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">09/13/2023</td>
                                    <td styleCode="Botrule">
                                        <content ID="assessment958528">11. Do you believe that these experiences have affected your health?</content>
                                    </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">09/13/2023 14:04:47</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="screener75291">
                    Generalized Anxiety Disorder</content>
                                    </td>
                                    <td>20</td>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                    
                  </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:03:43</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958529">Feeling nervous, anxious or on edge</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:03:43</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958530">Not being able to stop or control worrying</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:03:43</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958531">Worrying too much about different things</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:03:43</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958532">Trouble relaxing</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:03:43</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958533">Being so restless that it is hard to sit still</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:03:43</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958534">Becoming easily annoyed or irritable</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:03:43</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">09/13/2023</td>
                                    <td styleCode="Botrule">
                                        <content ID="assessment958535">Feeling afraid as if something awful might happen</content>
                                    </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">09/13/2023 14:03:43</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="screener75292">
                    Patient Health Questionnaire</content>
                                    </td>
                                    <td>11</td>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                    
                  </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:03:15</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958536">Little interest or pleasure in doing things</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:03:15</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958537">Feeling down, depressed, or hopeless</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:03:15</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958538">Trouble falling or staying asleep, or sleeping too much</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:03:15</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958539">Feeling tired or having little energy</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:03:15</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958540">Poor appetite or overeating</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:03:15</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958541">Feeling bad about yourself - or that you are a failure or have let yourself or your family down</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:03:15</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958542">Trouble concentrating on things, such as reading the newspaper or watching television</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:03:15</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958543">Moving or speaking so slowly that other people could have noticed? Or the opposite - being so fidgety or restless that you have been moving around a lot more than usual</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:03:15</td>
                                </tr>
                                <tr>
                                    <td>09/13/2023</td>
                                    <td>
                                        <content ID="assessment958544">Thoughts that you would be better off dead or of hurting yourself in some way</content>
                                    </td>
                                    <td/>
                                    <td>ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>09/13/2023 14:03:15</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">09/13/2023</td>
                                    <td styleCode="Botrule">
                                        <content ID="assessment958545">If you checked off any problems, how difficult have these problems made it for you to do your work, take care of things at home, or get along with other people?</content>
                                    </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">ANDREA SMITH-HAWTHORNE, LMSW
                      
                    </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">09/13/2023 14:03:15</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="screener86782">
                    Modified Checklist for Autism in Toddlers</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                    
                  </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116897">Does your child enjoy being swung, bounced on your knee, etc.?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116898">Does your child take an interest in other children?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116899">Does your child like climbing on things, such as up stairs?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116900">Does your child enjoy playing peek-a-boo/hide-and-seek?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116901">Does your child ever pretend, for example, to talk on the phone or take care of a doll or pretend other things?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116902">Does your child ever use his/her index finger to point, to ask for something?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116903">Does your child ever use his/her index finger to point, to indicate interest in something?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116904">Can your child play properly with small toys (e.g. cars or blocks) without just mouthing, fiddling, or dropping them?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116905">Does your child ever bring objects over to you (parent) to show you something?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116906">Does your child look you in the eye for more than a second or two?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116907">Does your child ever seem oversensitive to noise? (e.g., plugging ears)</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116908">Does your child smile in response to your face or your smile?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116909">Does your child imitate you? (e.g., you make a face-will your child imitate it?)</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116910">Does your child respond to his/her name when you call?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116911">If you point at a toy across the room, does your child look at it?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116912">Does your child walk?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116913">Does your child look at things you are looking at?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116914">Does your child make unusual finger movements near his/her face?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116915">Does your child try to attract your attention to his/her own activity?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116916">Have you ever wondered if your child is deaf?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116917">Does your child understand what people say?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>02/02/2024</td>
                                    <td>
                                        <content ID="assessment1116918">Does your child sometimes stare at nothing or wander with no purpose?</content>
                                    </td>
                                    <td/>
                                    <td>Jessica Jones
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">02/02/2024</td>
                                    <td styleCode="Botrule">
                                        <content ID="assessment1116919">Does your child look at your face to check your reaction when faced with something unfamiliar?</content>
                                    </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">Jessica Jones
                      
                    </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">02/02/2024 12:02:16</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="screener91691">
                    Patient Health Questionnaire</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                    
                  </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184269">Little interest or pleasure in doing things</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184267">Feeling down, depressed, or hopeless</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184264">Trouble falling or staying asleep, or sleeping too much</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184271">Feeling tired or having little energy</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184265">Poor appetite or overeating</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184266">Feeling bad about yourself - or that you are a failure or have let yourself or your family down</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184270">Trouble concentrating on things, such as reading the newspaper or watching television</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184268">Moving or speaking so slowly that other people could have noticed? Or the opposite - being so fidgety or restless that you have been moving around a lot more than usual</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184263">Thoughts that you would be better off dead or of hurting yourself in some way</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">03/21/2024</td>
                                    <td styleCode="Botrule">
                                        <content ID="assessment1184272">If you checked off any problems, how difficult have these problems made it for you to do your work, take care of things at home, or get along with other people?</content>
                                    </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">Maureen Burns-Gorman RN
                      
                    </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="screener91695">
                    Six Item Cognitive Impairment Test</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                    
                  </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184297">1. What year is it?</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184301">2. What month is it?</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184298">3. Give the patient an address phrase to remember with 5 components- eg John, Smith, 42, High St, Bedford.</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184296">4. About what time is it (within 1 hour)</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184299">5. Count backwards from 20-1</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184295">6. Say the months of the year in reverse</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">03/21/2024</td>
                                    <td styleCode="Botrule">
                                        <content ID="assessment1184300">7. Repeat address phrase John, Smith, 42, High St, Bedford</content>
                                    </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">Maureen Burns-Gorman RN
                      
                    </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">03/21/2024 14:37:11</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="screener91699">
                    Generalized Anxiety Disorder</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                    
                  </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:49:08</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184353">Feeling nervous, anxious or on edge</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:49:08</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184354">Not being able to stop or control worrying</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:49:08</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184355">Worrying too much about different things</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:49:08</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184356">Trouble relaxing</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:49:08</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184357">Being so restless that it is hard to sit still</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:49:08</td>
                                </tr>
                                <tr>
                                    <td>03/21/2024</td>
                                    <td>
                                        <content ID="assessment1184358">Becoming easily annoyed or irritable</content>
                                    </td>
                                    <td/>
                                    <td>Maureen Burns-Gorman RN
                      
                    </td>
                                    <td>NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td>03/21/2024 14:49:08</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">03/21/2024</td>
                                    <td styleCode="Botrule">
                                        <content ID="assessment1184359">Feeling afraid as if something awful might happen</content>
                                    </td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">Maureen Burns-Gorman RN
                      
                    </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">03/21/2024 14:49:08</td>
                                </tr>
                            </tbody>
                        </table>
                        <br/>
                        <paragraph>No Mental SDOH screeners recorded</paragraph>
                    </text>
                    <entry typeCode="DRIV">
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId root="2.16.840.1.113883.**********.69"/>
                            <id root="1fba5574-4bf6-4d81-8b29-32462f00376f"/>
                            <code code="69737-5" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"/>
                            <statusCode code="completed"/>
                            <effectiveTime value="20221209142416-0500"/>
                            <value value="16" xsi:type="INT"/>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="e7a9072a-4f93-4352-b0d4-bb68d2bb8e59"/>
                                    <code code="70274-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Generalized anxiety disorder 7 item (GAD-7) total score [Reported.PHQ]"/>
                                    <text>
                                        <reference value="#screener50672"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20221209142416-0500"/>
                                    <value value="16" xsi:type="INT"/>
                                </observation>
                            </entryRelationship>
                        </observation>
                    </entry>
                    <entry typeCode="DRIV">
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId root="2.16.840.1.113883.**********.69"/>
                            <id root="090db075-4840-44a7-ba47-18b57eab8a3b"/>
                            <code code="44257-4" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"/>
                            <statusCode code="completed"/>
                            <effectiveTime value="20221209142541-0500"/>
                            <value value="18" xsi:type="INT"/>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="52c4b938-c5b5-4836-b83b-eb3b9087ba04"/>
                                    <code code="44261-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Patient Health Questionnaire 9 item (PHQ-9) total score [Reported]"/>
                                    <text>
                                        <reference value="#screener50673"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20221209142541-0500"/>
                                    <value value="18" xsi:type="INT"/>
                                </observation>
                            </entryRelationship>
                        </observation>
                    </entry>
                    <entry typeCode="DRIV">
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId root="2.16.840.1.113883.**********.69"/>
                            <id root="ca0e74a4-9358-4225-9596-1d1bc10f05ac"/>
                            <code code="82813-7" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"/>
                            <statusCode code="completed"/>
                            <effectiveTime value="20230913075433-0400"/>
                            <value xsi:type="CD"/>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="b72c188a-a48a-4702-96ea-bee5f6942e38"/>
                                    <code code="82813-7" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Adverse Childhood Experiences [ACE]"/>
                                    <text>
                                        <reference value="#screener75196"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20230913075433-0400"/>
                                    <value xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                        </observation>
                    </entry>
                    <entry typeCode="DRIV">
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId root="2.16.840.1.113883.**********.69"/>
                            <id root="edd3900d-6e4e-4913-871a-6016806c1ba7"/>
                            <code code="82813-7" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"/>
                            <statusCode code="completed"/>
                            <effectiveTime value="20230913140447-0400"/>
                            <value value="8" xsi:type="INT"/>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="33828eec-b5a1-48a4-91f1-d18f6094ed1f"/>
                                    <code code="82813-7" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Adverse Childhood Experiences [ACE]"/>
                                    <text>
                                        <reference value="#screener75290"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20230913140447-0400"/>
                                    <value value="8" xsi:type="INT"/>
                                </observation>
                            </entryRelationship>
                        </observation>
                    </entry>
                    <entry typeCode="DRIV">
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId root="2.16.840.1.113883.**********.69"/>
                            <id root="81a99be8-6aa0-480b-b214-a8da48b3dfa6"/>
                            <code code="69737-5" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"/>
                            <statusCode code="completed"/>
                            <effectiveTime value="20230913140343-0400"/>
                            <value value="20" xsi:type="INT"/>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="211576dd-840c-43c6-af18-7a327a0edc09"/>
                                    <code code="70274-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Generalized anxiety disorder 7 item (GAD-7) total score [Reported.PHQ]"/>
                                    <text>
                                        <reference value="#screener75291"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20230913140343-0400"/>
                                    <value value="20" xsi:type="INT"/>
                                </observation>
                            </entryRelationship>
                        </observation>
                    </entry>
                    <entry typeCode="DRIV">
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId root="2.16.840.1.113883.**********.69"/>
                            <id root="3269fa46-8f98-4477-93eb-e95674604d92"/>
                            <code code="44257-4" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"/>
                            <statusCode code="completed"/>
                            <effectiveTime value="20230913140315-0400"/>
                            <value value="11" xsi:type="INT"/>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="09b163ce-6c15-4886-b393-07df2c558c6e"/>
                                    <code code="44261-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Patient Health Questionnaire 9 item (PHQ-9) total score [Reported]"/>
                                    <text>
                                        <reference value="#screener75292"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20230913140315-0400"/>
                                    <value value="11" xsi:type="INT"/>
                                </observation>
                            </entryRelationship>
                        </observation>
                    </entry>
                    <entry typeCode="DRIV">
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId root="2.16.840.1.113883.**********.69"/>
                            <id root="81c6150b-773f-4193-9d30-dafcce969876"/>
                            <code code="273601006" codeSystem="2.16.840.1.113883.6.1" codeSystemName="SNOMED2"/>
                            <statusCode code="completed"/>
                            <effectiveTime value="20240202120216-0500"/>
                            <value xsi:type="CD"/>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="ef7102e1-d20a-49bb-9a0c-31957647a42e"/>
                                    <code nullFlavor="NI"/>
                                    <text>
                                        <reference value="#screener86782"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20240202120216-0500"/>
                                    <value xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                        </observation>
                    </entry>
                    <entry typeCode="DRIV">
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId root="2.16.840.1.113883.**********.69"/>
                            <id root="ee9e4ddd-460d-4154-9a0f-c4cfaffc62e6"/>
                            <code code="44257-4" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"/>
                            <statusCode code="completed"/>
                            <effectiveTime value="20240321143711-0400"/>
                            <value xsi:type="CD"/>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="99dbf794-0113-4479-8097-e754a94e7733"/>
                                    <code code="44261-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Patient Health Questionnaire 9 item (PHQ-9) total score [Reported]"/>
                                    <text>
                                        <reference value="#screener91691"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20240321143711-0400"/>
                                    <value xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                        </observation>
                    </entry>
                    <entry typeCode="DRIV">
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId root="2.16.840.1.113883.**********.69"/>
                            <id root="22efd8b4-c2f9-43c8-a830-57a3c3617794"/>
                            <code code="94015-5" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"/>
                            <statusCode code="completed"/>
                            <effectiveTime value="20240321143711-0400"/>
                            <value xsi:type="CD"/>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="bb48b4c1-b840-44a3-9479-2f09b417c07f"/>
                                    <code code="94015-5" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Mental health screening report"/>
                                    <text>
                                        <reference value="#screener91695"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20240321143711-0400"/>
                                    <value xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                        </observation>
                    </entry>
                    <entry typeCode="DRIV">
                        <observation classCode="OBS" moodCode="EVN">
                            <templateId root="2.16.840.1.113883.**********.69"/>
                            <id root="324417c3-d8f4-477c-8ae2-66f095ec5572"/>
                            <code code="69737-5" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"/>
                            <statusCode code="completed"/>
                            <effectiveTime value="20240321144908-0400"/>
                            <value xsi:type="CD"/>
                            <entryRelationship typeCode="COMP">
                                <observation classCode="OBS" moodCode="EVN">
                                    <templateId root="2.16.840.1.113883.**********.86"/>
                                    <id root="b3edc32e-d66f-428b-9d1b-025e38564a81"/>
                                    <code code="70274-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Generalized anxiety disorder 7 item (GAD-7) total score [Reported.PHQ]"/>
                                    <text>
                                        <reference value="#screener91699"/>
                                    </text>
                                    <statusCode code="completed"/>
                                    <effectiveTime value="20240321144908-0400"/>
                                    <value xsi:type="CD"/>
                                </observation>
                            </entryRelationship>
                        </observation>
                    </entry>
                </section>
            </component>
            <component>
                <section>
                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.15"/>
                    <templateId root="2.16.840.1.113883.**********.15"/>
                    <id root="2431c12e-8465-49e8-9c23-f750d0b91c79"/>
                    <code code="10157-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Family History"/>
                    <title>Family History</title>
                    <text>
    Nothing Reported.
    </text>
                </section>
            </component>
            <component>
                <section>
                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.39"/>
                    <templateId root="2.16.840.1.113883.**********.39"/>
                    <code code="11329-0" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"/>
                    <title>Medical History</title>
                    <text>
                              
        No medical history recorded.
      
      
        
                        <paragraph/>
                        <content>Gynecological History</content>
                                
          No gynecological history recorded.
        
        
                        <paragraph/>
                        <content>Obstetrics History</content>
                        <paragraph>
                            <content styleCode="Bold">GPAL: </content>
                            G 0 P 0 0 0 0
                        </paragraph>
                    </text>
                </section>
            </component>
            <component>
                <section>
                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.2.1"/>
                    <templateId root="2.16.840.1.113883.**********.2.1"/>
                    <id root="b4bd4988-fd6a-461b-b57e-3476dc1c9244"/>
                    <code code="11369-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="History of immunizations"/>
                    <title>Immunizations</title>
                    <text>
                        <table>
                            <thead>
                                <tr>
                                    <th>Vaccine Type</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Note</th>
                                    <th>Provider Name and Address</th>
                                    <th>Organization Details</th>
                                    <th>Recorded Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td styleCode="Botrule">
                                        <content ID="immun1">zoster recombinant</content>
                                    </td>
                                    <td styleCode="Botrule">04/06/2021</td>
                                    <td styleCode="Botrule">completed</td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">
                                        Jeanna Everard
                  
                    
                                        <paragraph> null,   </paragraph>
                                        <paragraph/>
                                    </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">11/05/2021 13:37:06</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">
                                        <content ID="immun2">influenza, seasonal, intradermal, preservative free</content>
                                    </td>
                                    <td styleCode="Botrule">11/06/2023</td>
                                    <td styleCode="Botrule">completed</td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">
                                        Amy Kirker
                  
                    
                                        <paragraph> null,   </paragraph>
                                        <paragraph/>
                                    </td>
                                    <td styleCode="Botrule">NY - NORTH COUNTRY FAMILY HEALTH CENTER </td>
                                    <td styleCode="Botrule">11/06/2023 13:45:04</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">
                                        <content ID="immun3">COVID-19, mRNA, LNP-S, bivalent, PF, 30 mcg/0.3 mL dose</content>
                                    </td>
                                    <td styleCode="Botrule">08/22/2025</td>
                                    <td styleCode="Botrule">completed</td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">Not Available</td>
                                    <td styleCode="Botrule">AthenaHealth </td>
                                    <td styleCode="Botrule">09/05/2025 12:34:20</td>
                                </tr>
                                <tr>
                                    <td styleCode="Botrule">
                                        <content ID="immun4">Influenza, split virus, quadrivalent, PF</content>
                                    </td>
                                    <td styleCode="Botrule">10/02/2023</td>
                                    <td styleCode="Botrule">completed</td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">Not Available</td>
                                    <td styleCode="Botrule">AthenaHealth </td>
                                    <td styleCode="Botrule">09/05/2025 12:34:20</td>
                                </tr>
                            </tbody>
                        </table>
                    </text>
                    <entry>
                        <substanceAdministration classCode="SBADM" moodCode="EVN" negationInd="false">
                            <templateId root="2.16.840.1.113883.**********.52"/>
                            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.52"/>
                            <id root="e8dc3a7d-b66b-4d7e-93fd-352ca01f61b6"/>
                            <text>
                                <reference value="#immun1"/>
                            </text>
                            <statusCode code="completed"/>
                            <effectiveTime value="20210406"/>
                            <consumable>
                                <manufacturedProduct classCode="MANU">
                                    <templateId root="2.16.840.1.113883.**********.54"/>
                                    <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.54"/>
                                    <manufacturedMaterial>
                                        <code code="187" codeSystem="2.16.840.1.113883.12.292" codeSystemName="CVX" displayName="zoster recombinant">
                                            <originalText>
                    zoster recombinant
                  </originalText>
                                        </code>
                                        <lotNumberText nullFlavor="NI"/>
                                    </manufacturedMaterial>
                                </manufacturedProduct>
                            </consumable>
                            <author>
                                <templateId root="2.16.840.1.113883.**********.119"/>
                                <time value="20211105133706-0500"/>
                                <assignedAuthor>
                                    <id root="2.16.840.1.113883.4.6"/>
                                    <addr use="WP">
                                        <city nullFlavor="NI"/>
                                        <state nullFlavor="NI"/>
                                        <postalCode nullFlavor="NI"/>
                                        <country>US</country>
                                    </addr>
                                    <assignedPerson>
                                        <name>
                                            <given>Jeanna Everard</given>
                                            <family>Everard</family>
                                        </name>
                                    </assignedPerson>
                                    <representedOrganization>
                                        <id nullFlavor="UNK" root="e06c7b8c-1ebe-4d40-a72b-7dec4eb35f4d"/>
                                        <name>NY - NORTH COUNTRY FAMILY HEALTH CENTER</name>
                                        <telecom nullFlavor="NI"/>
                                    </representedOrganization>
                                </assignedAuthor>
                            </author>
                        </substanceAdministration>
                    </entry>
                    <entry>
                        <substanceAdministration classCode="SBADM" moodCode="EVN" negationInd="false">
                            <templateId root="2.16.840.1.113883.**********.52"/>
                            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.52"/>
                            <id root="ce640aa5-95b4-41b4-8b4f-0c369a481674"/>
                            <text>
                                <reference value="#immun2"/>
                            </text>
                            <statusCode code="completed"/>
                            <effectiveTime value="20231106"/>
                            <consumable>
                                <manufacturedProduct classCode="MANU">
                                    <templateId root="2.16.840.1.113883.**********.54"/>
                                    <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.54"/>
                                    <manufacturedMaterial>
                                        <code code="144" codeSystem="2.16.840.1.113883.12.292" codeSystemName="CVX" displayName="influenza, seasonal, intradermal, preservative free">
                                            <originalText>
                    influenza, seasonal, intradermal, preservative free
                  </originalText>
                                        </code>
                                        <lotNumberText nullFlavor="NI"/>
                                    </manufacturedMaterial>
                                </manufacturedProduct>
                            </consumable>
                            <author>
                                <templateId root="2.16.840.1.113883.**********.119"/>
                                <time value="20231106134504-0500"/>
                                <assignedAuthor>
                                    <id root="2.16.840.1.113883.4.6"/>
                                    <addr use="WP">
                                        <city nullFlavor="NI"/>
                                        <state nullFlavor="NI"/>
                                        <postalCode nullFlavor="NI"/>
                                        <country>US</country>
                                    </addr>
                                    <assignedPerson>
                                        <name>
                                            <given>Amy Kirker</given>
                                            <family>Kirker</family>
                                        </name>
                                    </assignedPerson>
                                    <representedOrganization>
                                        <id nullFlavor="UNK" root="4ce5d135-3208-4b03-8e54-643ed2ad2fff"/>
                                        <name>NY - NORTH COUNTRY FAMILY HEALTH CENTER</name>
                                        <telecom nullFlavor="NI"/>
                                    </representedOrganization>
                                </assignedAuthor>
                            </author>
                        </substanceAdministration>
                    </entry>
                    <entry>
                        <substanceAdministration classCode="SBADM" moodCode="EVN" negationInd="false">
                            <templateId root="2.16.840.1.113883.**********.52"/>
                            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.52"/>
                            <id root="10d070e7-4b54-4d32-bfeb-2b4c26110444"/>
                            <text>
                                <reference value="#immun3"/>
                            </text>
                            <statusCode code="completed"/>
                            <effectiveTime value="20250822"/>
                            <routeCode nullFlavor="OTH">
                                <translation code="78421000" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Intramuscular use"/>
                            </routeCode>
                            <approachSiteCode code="16217701000119102" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Structure of left deltoid muscle"/>
                            <doseQuantity unit="mL" value="0.3"/>
                            <consumable>
                                <manufacturedProduct classCode="MANU">
                                    <templateId root="2.16.840.1.113883.**********.54"/>
                                    <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.54"/>
                                    <manufacturedMaterial>
                                        <code code="300" codeSystem="2.16.840.1.113883.12.292" codeSystemName="CVX" displayName="COVID-19, mRNA, LNP-S, bivalent, PF, 30 mcg/0.3 mL dose">
                                            <originalText>
                    COVID-19, mRNA, LNP-S, bivalent, PF, 30 mcg/0.3 mL dose
                  </originalText>
                                        </code>
                                        <lotNumberText>
                    FF2588
                  </lotNumberText>
                                    </manufacturedMaterial>
                                    <manufacturerOrganization classCode="ORG">
                                        <name>Pfizer, Inc</name>
                                    </manufacturerOrganization>
                                </manufacturedProduct>
                            </consumable>
                            <author>
                                <templateId root="2.16.840.1.113883.**********.119"/>
                                <time value="20250905123420-0500"/>
                                <assignedAuthor>
                                    <id root="2.16.840.1.113883.4.6"/>
                                    <representedOrganization>
                                        <id nullFlavor="UNK" root="62fa3a85-abac-40ff-9f46-ef82497e9348"/>
                                        <name>AthenaHealth</name>
                                        <telecom nullFlavor="NI"/>
                                    </representedOrganization>
                                </assignedAuthor>
                            </author>
                        </substanceAdministration>
                    </entry>
                    <entry>
                        <substanceAdministration classCode="SBADM" moodCode="EVN" negationInd="false">
                            <templateId root="2.16.840.1.113883.**********.52"/>
                            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.52"/>
                            <id root="bd17b3d9-4760-44fb-b58c-e56cb5b17648"/>
                            <text>
                                <reference value="#immun4"/>
                            </text>
                            <statusCode code="completed"/>
                            <effectiveTime value="20231002"/>
                            <routeCode nullFlavor="OTH">
                                <translation code="78421000" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Intramuscular use"/>
                            </routeCode>
                            <approachSiteCode code="16217701000119102" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Structure of left deltoid muscle"/>
                            <doseQuantity unit="mL" value="0.5"/>
                            <consumable>
                                <manufacturedProduct classCode="MANU">
                                    <templateId root="2.16.840.1.113883.**********.54"/>
                                    <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.54"/>
                                    <manufacturedMaterial>
                                        <code code="150" codeSystem="2.16.840.1.113883.12.292" codeSystemName="CVX" displayName="Influenza, split virus, quadrivalent, PF">
                                            <originalText>
                    Influenza, split virus, quadrivalent, PF
                  </originalText>
                                        </code>
                                        <lotNumberText>
                    946605
                  </lotNumberText>
                                    </manufacturedMaterial>
                                    <manufacturerOrganization classCode="ORG">
                                        <name>Seqirus</name>
                                    </manufacturerOrganization>
                                </manufacturedProduct>
                            </consumable>
                            <author>
                                <templateId root="2.16.840.1.113883.**********.119"/>
                                <time value="20250905123420-0500"/>
                                <assignedAuthor>
                                    <id root="2.16.840.1.113883.4.6"/>
                                    <representedOrganization>
                                        <id nullFlavor="UNK" root="d108c86c-1a18-4957-84b6-f625b22b1507"/>
                                        <name>AthenaHealth</name>
                                        <telecom nullFlavor="NI"/>
                                    </representedOrganization>
                                </assignedAuthor>
                            </author>
                        </substanceAdministration>
                    </entry>
                </section>
            </component>
            <component>
                <section>
                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.22"/>
                    <templateId root="2.16.840.1.113883.**********.22"/>
                    <id root="6c850e36-a21c-40be-870a-d8fde9836756"/>
                    <code code="46240-8" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"/>
                    <title>Past Encounters</title>
                    <text>
                        <table>
                            <thead>
                                <tr>
                                    <th>Encounter ID</th>
                                    <th>Performer</th>
                                    <th>Location</th>
                                    <th>Encounter Start Date</th>
                                    <th>Encounter Closed Date</th>
                                    <th>Diagnosis/Indication</th>
                                    <th>Diagnosis SNOMED-CT Code</th>
                                    <th>Diagnosis ICD10 Code</th>
                                    <th>Diagnosis IMO Codes</th>
                                    <th>Diagnosis Note</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td styleCode="Botrule">
                                        <content ID="encounter973132">973132</content>
                                    </td>
                                    <td styleCode="Botrule">THOMAS BARBER, DO </td>
                                    <td styleCode="Botrule">
                                        Lowville Family Health Center
                              
                                        <paragraph>5402 DAYAN ST</paragraph>
                                        <paragraph>LOWVILLE, NY 13367-1100</paragraph>
                                    </td>
                                    <td styleCode="Botrule">09/05/2025 12:34:07</td>
                                    <td styleCode="Botrule">09/05/2025 12:36:28</td>
                                    <td styleCode="Botrule">Acute abdominal pain</td>
                                    <td styleCode="Botrule">116290004</td>
                                    <td styleCode="Botrule">
                                        R10.9
                                        <br/>
                                    </td>
                                    <td styleCode="Botrule">
                                         27490
                                        <br/>
                                    </td>
                                    <td styleCode="Botrule"/>
                                </tr>
                            </tbody>
                        </table>
                    </text>
                    <entry>
                        <encounter classCode="ENC" moodCode="EVN">
                            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.49"/>
                            <templateId root="2.16.840.1.113883.**********.49"/>
                            <id root="65c3a2de-0c82-4191-9e56-a259f1dd8c3d"/>
                            <code codeSystem="2.16.840.1.113883.6.12" codeSystemName="CPT-4" nullFlavor="NI"/>
                            <text>
                                <reference value="#encounter973132"/>
                            </text>
                            <effectiveTime>
                                <low value="20250905123407-0500"/>
                                <high value="20250905123628-0500"/>
                            </effectiveTime>
                            <participant typeCode="LOC">
                                <participantRole classCode="SDLOC">
                                    <templateId root="2.16.840.1.113883.**********.32"/>
                                    <code codeSystem="2.16.840.1.113883.6.259" codeSystemName="Healthcare Service Location (HL7)" nullFlavor="NI"/>
                                    <addr use="WP">
                                        <streetAddressLine>5402 DAYAN ST</streetAddressLine>
                                        <city>LOWVILLE</city>
                                        <state>NY</state>
                                        <postalCode>13367-1100</postalCode>
                                        <country>US</country>
                                    </addr>
                                    <telecom use="WP" value="tel: (*************"/>
                                    <playingEntity classCode="PLC">
                                        <name> - Lowville Family Health Center</name>
                                    </playingEntity>
                                </participantRole>
                            </participant>
                            <entryRelationship typeCode="RSON">
                                <act classCode="ACT" moodCode="EVN">
                                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.80"/>
                                    <templateId root="2.16.840.1.113883.**********.80"/>
                                    <code code="29308-4" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Encounter Diagnosis"/>
                                    <entryRelationship typeCode="SUBJ">
                                        <observation classCode="OBS" moodCode="EVN">
                                            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.4"/>
                                            <templateId root="2.16.840.1.113883.**********.4"/>
                                            <id root="7a1a2570-a4ed-46c2-a2d8-90f763b69821"/>
                                            <code code="282291009" codeSystem="2.16.840.1.113883.3.88.12.3221.7.2" codeSystemName="SNOMED CT" displayName="Diagnosis">
                                                <translation code="29308-4" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Diagnosis"/>
                                            </code>
                                            <statusCode code="completed"/>
                                            <effectiveTime>
                                                <low value="20250905123418-0500"/>
                                                <high value="20250905123628-0500"/>
                                            </effectiveTime>
                                            <value code="116290004" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED" displayName="Acute abdominal pain" xsi:type="CD">
                                                <translation code="R10.9" codeSystem="2.16.840.1.113883.6.90" codeSystemName="ICD10" displayName="Unspecified abdominal pain"/>
                                                <translation code="27490" codeSystem="2.16.840.1.113883.3.247.1.1" codeSystemName="IMO" displayName="Acute abdominal pain"/>
                                            </value>
                                            <author>
                                                <templateId root="2.16.840.1.113883.**********.119"/>
                                                <time value="20250905123625-0500"/>
                                                <assignedAuthor>
                                                    <id extension="**********" root="2.16.840.1.113883.4.6"/>
                                                    <assignedPerson>
                                                        <name/>
                                                    </assignedPerson>
                                                    <representedOrganization>
                                                        <id nullFlavor="UNK" root="d9cd488f-88fa-463b-b233-2598d853e0a4"/>
                                                        <name>AthenaHealth</name>
                                                        <telecom nullFlavor="NI"/>
                                                    </representedOrganization>
                                                </assignedAuthor>
                                            </author>
                                        </observation>
                                    </entryRelationship>
                                </act>
                            </entryRelationship>
                        </encounter>
                    </entry>
                </section>
            </component>
            <component>
                <section nullFlavor="NI">
                    <templateId root="2.16.840.1.113883.**********.60"/>
                    <id root="7466f0bc-b59d-4b31-b608-a80ae6306a82"/>
                    <code code="61146-7" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Goals"/>
                    <title>Goals Section</title>
                    <text>
    
      None Recorded
    
  </text>
                </section>
            </component>
            <component>
                <section>
                    <!-- Health Concern section -->
                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.58"/>
                    <code code="75310-3" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Health Concerns Document"/>
                    <title>Health Concerns Section</title>
                    <text>
                        <table>
                            <thead>
                                <tr>
                                    <th>Related Observation</th>
                                    <th>LastModified by</th>
                                    <th>Organization Details</th>
                                    <th>LastModified Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>None Recorded</td>
                                </tr>
                            </tbody>
                        </table>
                        <table>
                            <thead>
                                <tr>
                                    <th>Concern</th>
                                    <th>Status</th>
                                    <th>LastModified by</th>
                                    <th>Organization Details</th>
                                    <th>LastModified Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>None Recorded</td>
                                    <td/>
                                    <td/>
                                    <td/>
                                    <td/>
                                </tr>
                            </tbody>
                        </table>
                        <table>
                            <thead>
                                <tr>
                                    <th>SDOH Concern</th>
                                    <th>Status</th>
                                    <th>LastModified by</th>
                                    <th>Organization Details</th>
                                    <th>LastModified Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>None Recorded</td>
                                    <td/>
                                    <td/>
                                    <td/>
                                    <td/>
                                </tr>
                            </tbody>
                        </table>
                    </text>
                    <entry>
                        <!-- Health Concern Act  -->
                        <act classCode="ACT" moodCode="EVN">
                            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.132"/>
                            <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.132"/>
                            <id root="d9cc92a8-0e70-4cdf-8b8d-6db608240b01"/>
                            <code code="75310-3" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Health Concern"/>
                            <statusCode code="completed"/>
                        </act>
                    </entry>
                </section>
            </component>
            <component>
                <section>
                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.18"/>
                    <templateId root="2.16.840.1.113883.**********.18"/>
                    <code code="48768-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Payers"/>
                    <title>Payers</title>
                    <text>
                        <table>
                            <thead>
                                <tr>
                                    <th>Encounter Date</th>
                                    <th>Sequence</th>
                                    <th>Insurance Name</th>
                                    <th>Policy Number</th>
                                    <th>Policy Holder</th>
                                    <th>Covered Member ID</th>
                                    <th>Holder Member ID</th>
                                    <th>Guarantor Name</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td styleCode="Botrule">09/05/2025</td>
                                    <td styleCode="Botrule">1</td>
                                    <td styleCode="Botrule">*SELF PAY*</td>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule"/>
                                    <td styleCode="Botrule">Patient  Test</td>
                                </tr>
                            </tbody>
                        </table>
                    </text>
                    <entry typeCode="DRIV">
                        <act classCode="ACT" moodCode="EVN">
                            <templateId extension="2023-05-01" root="2.16.840.1.113883.**********.60"/>
                            <templateId root="2.16.840.1.113883.**********.60"/>
                            <id root="33a2dcf9-70b7-4bd4-8f13-9156f01433c2"/>
                            <code code="48768-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Payment sources"/>
                            <statusCode code="completed"/>
                            <effectiveTime value="20250515"/>
                            <entryRelationship typeCode="COMP">
                                <sequenceNumber value="1"/>
                                <act classCode="ACT" moodCode="EVN">
                                    <templateId extension="2023-05-01" root="2.16.840.1.113883.**********.61"/>
                                    <templateId root="2.16.840.1.113883.**********.61"/>
                                    <id root="78a3ef42-d99e-4ad9-af10-43d40888b7ef"/>
                                    <code code="OT" codeSystem="2.16.840.1.113883.6.255.1336" codeSystemName="Insurance Type Code (x12N-1336)" displayName="Other">
                                        <translation codeSystem="2.16.840.1.113883.3.221.5" codeSystemName="Source of Payment Typology (PHDSC)" nullFlavor="NI"/>
                                    </code>
                                    <statusCode code="completed"/>
                                    <performer typeCode="PRF">
                                        <templateId root="2.16.840.1.113883.**********.87"/>
                                        <assignedEntity>
                                            <id extension="0" root="9c6c1430-e140-4164-8345-f53ae84a848a"/>
                                            <code code="PAYOR" codeSystem="2.16.840.1.113883.5.110" codeSystemName="HL7RoleCode" displayName="Invoice Payor"/>
                                            <addr use="WP">
                                                <streetAddressLine nullFlavor="NI"/>
                                                <city nullFlavor="NI"/>
                                                <state nullFlavor="NI"/>
                                                <postalCode nullFlavor="NI"/>
                                                <country nullFlavor="NI"/>
                                            </addr>
                                            <telecom nullFlavor="NI"/>
                                            <representedOrganization>
                                                <name>*SELF PAY*</name>
                                                <telecom nullFlavor="NI"/>
                                                <addr use="WP">
                                                    <streetAddressLine nullFlavor="NI"/>
                                                    <city nullFlavor="NI"/>
                                                    <state nullFlavor="NI"/>
                                                    <postalCode nullFlavor="NI"/>
                                                    <country nullFlavor="NI"/>
                                                </addr>
                                            </representedOrganization>
                                        </assignedEntity>
                                    </performer>
                                    <performer typeCode="PRF">
                                        <templateId root="2.16.840.1.113883.**********.88"/>
                                        <assignedEntity>
                                            <id root="952c20df-2ed3-4cbc-a69c-33acb43b14ec"/>
                                            <code code="GUAR" codeSystem="2.16.840.1.113883.5.110" codeSystemName="HL7RoleClass" displayName="Guarantor"/>
                                            <addr use="HP">
                                                <streetAddressLine>238 Arsenal St</streetAddressLine>
                                                <city>WATERTOWN</city>
                                                <state>NY</state>
                                                <postalCode>13601</postalCode>
                                                <country>USA</country>
                                            </addr>
                                            <telecom nullFlavor="NI"/>
                                        </assignedEntity>
                                    </performer>
                                    <participant typeCode="COV">
                                        <templateId root="2.16.840.1.113883.**********.89"/>
                                        <participantRole>
                                            <id root="fca6c01b-b945-4d80-b2c2-f7786d9aece0"/>
                                            <code code="UNK" codeSystem="2.16.840.1.113883.5.111" codeSystemName="HL7RoleCode" displayName="Unknown"/>
                                            <addr use="HP">
                                                <streetAddressLine nullFlavor="NI"/>
                                                <city nullFlavor="NI"/>
                                                <state nullFlavor="NI"/>
                                                <postalCode nullFlavor="NI"/>
                                                <country nullFlavor="NI"/>
                                            </addr>
                                        </participantRole>
                                    </participant>
                                    <participant typeCode="HLD">
                                        <templateId root="2.16.840.1.113883.**********.90"/>
                                        <participantRole>
                                            <id root="56304bcb-3c40-44ed-994b-3a8516ce91f6"/>
                                            <addr use="HP">
                                                <streetAddressLine nullFlavor="NI"/>
                                                <city nullFlavor="NI"/>
                                                <state nullFlavor="NI"/>
                                                <postalCode nullFlavor="NI"/>
                                                <country nullFlavor="NI"/>
                                            </addr>
                                        </participantRole>
                                    </participant>
                                    <entryRelationship typeCode="REFR">
                                        <act classCode="ACT" moodCode="EVN">
                                            <templateId root="2.16.840.1.113883.**********"/>
                                            <id root="1ce042ef-adcc-404d-8261-463c70e16891"/>
                                            <code nullFlavor="NA"/>
                                            <entryRelationship typeCode="SUBJ">
                                                <procedure classCode="PROC" moodCode="PRMS">
                                                    <code nullFlavor="NA"/>
                                                </procedure>
                                            </entryRelationship>
                                        </act>
                                    </entryRelationship>
                                </act>
                            </entryRelationship>
                        </act>
                    </entry>
                </section>
            </component>
            <component>
                <section nullFlavor="NI">
                    <templateId root="2.16.840.1.113883.**********.65"/>
                    <id root="36d20c49-c83d-48d0-9127-ad018ab6fe57"/>
                    <code code="34109-9" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Notes"/>
                    <title>Notes</title>
                    <text>
    None Recorded
    </text>
                </section>
            </component>
            <component>
                <section>
                    <templateId extension="2019-07-01" root="2.16.840.1.113883.**********.500"/>
                    <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.500"/>
                    <code code="85847-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Patient Care team information"/>
                    <title>Care Team</title>
                    <text>
                        <table>
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Role</th>
                                    <th>Member ID</th>
                                    <th>Specialty</th>
                                    <th>Address</th>
                                    <th>Phone</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>None Recorded.</td>
                                </tr>
                            </tbody>
                        </table>
                    </text>
                </section>
            </component>
        </structuredBody>
    </component>
</ClinicalDocument>
