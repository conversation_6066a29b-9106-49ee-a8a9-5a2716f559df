<?xml version="1.0" encoding="windows-1252"?>
<!-- Document ID: 14655647 - DAT: 54350.99-->

  <ClinicalDocument xmlns="urn:hl7-org:v3" xmlns:sdtc="urn:hl7-org:sdtc" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <realmCode code="US"></realmCode>
    <typeId extension="POCD_HD000040" root="2.16.840.1.113883.1.3"></typeId>
    <templateId root="1.2.840.114350.1.72.1.51693"></templateId>
    <templateId root="2.16.840.1.113883.10.20.22.1.1"></templateId>
    <templateId extension="2015-08-01" root="2.16.840.1.113883.10.20.22.1.1"></templateId>
    <templateId extension="2023-05-01" root="2.16.840.1.113883.10.20.22.1.1"></templateId>
    <templateId root="2.16.840.1.113883.10.20.22.1.2"></templateId>
    <templateId extension="2015-08-01" root="2.16.840.1.113883.10.20.22.1.2"></templateId>
    <id assigningAuthorityName="EPC" root="1.2.840.114350.1.13.382.3.7.8.688883.36006361"></id>
    <code code="34133-9" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Summarization of Episode Note"></code>
    <title>Summary of Care</title>
    <effectiveTime value="20241206175047-0500"></effectiveTime>
    <confidentialityCode code="N" codeSystem="2.16.840.1.113883.5.25" displayName="Normal"></confidentialityCode>
    <languageCode code="en-US"></languageCode>
    <setId assigningAuthorityName="EPC" extension="2ce510d5-b41f-11ef-ae18-005056bafd93" root="1.2.840.114350.1.13.382.*******"></setId>
    <versionNumber value="2"></versionNumber>
    <recordTarget>
      <patientRole>
        <id assigningAuthorityName="BHMC" extension="4121929" root="2.16.840.1.113883.3.483"></id>
        <addr use="HP">
          <streetAddressLine>12 Bay Parkway</streetAddressLine>
          <county>KINGS</county>
          <city>BROOKLYN</city>
          <state>NY</state>
          <postalCode>11212</postalCode>
          <country>USA</country>
          <useablePeriod xsi:type="IVL_TS">
            <low value="20240507"></low>
            <high nullFlavor="NA"></high>
          </useablePeriod>
        </addr>
        <telecom use="HP" value="tel:******-245-3456"></telecom>
        <patient>
          <name use="L">
            <given>Five</given>
            <family>Bhmc</family>
            <validTime>
              <low value="20240507105347"></low>
              <high nullFlavor="NA"></high>
            </validTime>
          </name>
          <administrativeGenderCode code="F" codeSystem="2.16.840.1.113883.5.1" codeSystemName="AdministrativeGenderCode" displayName="Female"></administrativeGenderCode>
          <birthTime value="19740727"></birthTime>
          <sdtc:deceasedInd value="false"></sdtc:deceasedInd>
          <maritalStatusCode code="Single" codeSystem="2.16.840.1.113883.5.2" codeSystemName="MaritalStatusCode" displayName="Single"></maritalStatusCode>
          <religiousAffiliationCode code="Christian" codeSystem="2.16.840.1.113883.5.1076" codeSystemName="ReligiousAffiliation"></religiousAffiliationCode>
          <raceCode nullFlavor="UNK"></raceCode>
          <sdtc:raceCode code="2054-5" codeSystem="2.16.840.1.113883.6.238" codeSystemName="CDC Race and Ethnicity" displayName="Black or African American"></sdtc:raceCode>
          <ethnicGroupCode nullFlavor="UNK"></ethnicGroupCode>
          <sdtc:ethnicGroupCode code="2186-5" codeSystem="2.16.840.1.113883.6.238" codeSystemName="CDC Race and Ethnicity" displayName="Not Hispanic or Latino"></sdtc:ethnicGroupCode>
          <languageCommunication>
            <languageCode code="ben"></languageCode>
            <preferenceInd value="true"></preferenceInd>
          </languageCommunication>
          <languageCommunication>
            <languageCode code="eng"></languageCode>
            <modeCode code="RWR" codeSystem="2.16.840.1.113883.5.60" displayName="Received Written"></modeCode>
            <preferenceInd value="true"></preferenceInd>
          </languageCommunication>
        </patient>
        <providerOrganization>
          <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
          <name>One Brooklyn Health - TST</name>
          <telecom nullFlavor="NI"></telecom>
          <addr use="WP">
            <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
            <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
            <city>Brooklyn</city>
            <state>NY</state>
            <postalCode>11212</postalCode>
            <country>USA</country>
          </addr>
        </providerOrganization>
      </patientRole>
    </recordTarget>
    <author>
      <time value="20241206175047-0500"></time>
      <assignedAuthor>
        <id extension="10.9" root="1.2.840.114350.1.1"></id>
        <addr nullFlavor="NA"></addr>
        <telecom nullFlavor="NA"></telecom>
        <assignedAuthoringDevice>
          <manufacturerModelName>Epic - Version 10.9</manufacturerModelName>
          <softwareName>Epic - Version 10.9</softwareName>
        </assignedAuthoringDevice>
        <representedOrganization>
          <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
          <name>One Brooklyn Health - TST</name>
          <telecom nullFlavor="NI"></telecom>
          <addr use="WP">
            <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
            <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
            <city>Brooklyn</city>
            <state>NY</state>
            <postalCode>11212</postalCode>
            <country>USA</country>
          </addr>
        </representedOrganization>
      </assignedAuthor>
    </author>
    <custodian>
      <assignedCustodian>
        <representedCustodianOrganization>
          <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
          <name>One Brooklyn Health - TST</name>
          <telecom nullFlavor="NI"></telecom>
          <addr use="WP">
            <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
            <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
            <city>Brooklyn</city>
            <state>NY</state>
            <postalCode>11212</postalCode>
            <country>USA</country>
          </addr>
        </representedCustodianOrganization>
      </assignedCustodian>
    </custodian>
    <legalAuthenticator>
      <time value="20241206175047-0500"></time>
      <signatureCode code="S"></signatureCode>
      <assignedEntity>
        <id extension=" 100273" root="1.2.840.114350.1.13.382.3.7.2.697780"></id>
        <code nullFlavor="UNK"></code>
        <addr nullFlavor="UNK"></addr>
        <telecom nullFlavor="UNK"></telecom>
        <assignedPerson>
          <name nullFlavor="UNK"></name>
          <sdtc:desc>Careeverywhere, Background</sdtc:desc>
        </assignedPerson>
      </assignedEntity>
    </legalAuthenticator>
    <participant typeCode="IND">
      <templateId extension="2023-05-01" root="2.16.840.1.113883.**********.8"></templateId>
      <time value="20240507"></time>
      <associatedEntity classCode="PRS">
        <id extension="4090530" root="1.2.840.114350.1.13.382.3.7.2.827665"></id>
        <code codeSystem="2.16.840.1.113883.5.111" codeSystemName="RoleCode" nullFlavor="OTH">
          <originalText>Significant other</originalText>
        </code>
        <addr nullFlavor="UNK"></addr>
        <telecom use="HP" value="tel:******-748-2542"></telecom>
        <associatedPerson>
          <name>John Hill</name>
        </associatedPerson>
      </associatedEntity>
    </participant>
    <documentationOf typeCode="DOC">
      <serviceEvent classCode="PCPR">
        <effectiveTime>
          <low value="20241206"></low>
          <high value="20241206164943-0500"></high>
        </effectiveTime>
        <performer typeCode="PRF">
          <functionCode code="PCP" codeSystem="2.16.840.1.113883.5.88" codeSystemName="ParticipationFunction" displayName="Primary Care Provider">
            <originalText>General</originalText>
          </functionCode>
          <time>
            <low value="20240507"></low>
            <high nullFlavor="NI"></high>
          </time>
          <assignedEntity>
            <id nullFlavor="UNK"></id>
            <code code="207Q00000X" codeSystem="2.16.840.1.113883.6.101" displayName="FAMILY MEDICINE PHYSICIAN">
              <originalText>Family Medicine</originalText>
              <translation code="19" codeSystem="1.2.840.114350.********.7.10.688867.4160" codeSystemName="Epic.DXC.StandardProviderSpecialtyType" displayName="Family Medicine"></translation>
              <translation code="9" codeSystem="1.2.840.114350.1.13.382.3.7.10.836982.1050" codeSystemName="Epic.SER.ProviderSpecialty" displayName="Family Medicine"></translation>
            </code>
            <addr use="WP">
              <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
              <city>Brooklyn</city>
              <state>NY</state>
              <postalCode>11201</postalCode>
            </addr>
            <telecom nullFlavor="NA"></telecom>
            <assignedPerson>
              <name use="L">
                <given>No</given>
                <given>Primary Care Provider On</given>
                <family>File</family>
                <suffix qualifier="AC"> MD</suffix>
                <validTime>
                  <low nullFlavor="UNK"></low>
                  <high nullFlavor="UNK"></high>
                </validTime>
              </name>
            </assignedPerson>
            <representedOrganization classCode="ORG">
              <name>One Brooklyn Health - TST</name>
              <telecom nullFlavor="UNK"></telecom>
              <addr use="WP">
                <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                <city>Brooklyn</city>
                <state>NY</state>
                <postalCode>11212</postalCode>
                <country>USA</country>
              </addr>
            </representedOrganization>
          </assignedEntity>
        </performer>
      </serviceEvent>
    </documentationOf>
    <relatedDocument typeCode="RPLC">
      <parentDocument>
        <id assigningAuthorityName="EPC" root="1.2.840.114350.1.13.382.3.7.8.688883.36006360"></id>
        <setId assigningAuthorityName="EPC" extension="2ce510d5-b41f-11ef-ae18-005056bafd93" root="1.2.840.114350.1.13.382.*******"></setId>
        <versionNumber value="1"></versionNumber>
      </parentDocument>
    </relatedDocument>
    <componentOf>
      <encompassingEncounter>
        <id extension="**********" root="1.2.840.114350.1.13.382.3.7.3.698084.8"></id>
        <code code="AMB" codeSystem="2.16.840.1.113883.5.4" displayName="Ambulatory">
          <originalText>Orders Only</originalText>
          <translation code="111" codeSystem="1.2.840.114350.*********" displayName="Orders Only"></translation>
          <translation code="0" codeSystem="1.2.840.114350.*********.1"></translation>
        </code>
        <effectiveTime>
          <low value="20241206"></low>
          <high value="20241206164943-0500"></high>
        </effectiveTime>
        <responsibleParty>
          <assignedEntity>
            <id nullFlavor="UNK"></id>
            <addr use="WP">
              <streetAddressLine>XXXXX Street</streetAddressLine>
              <city>New York</city>
              <state>NY</state>
              <postalCode>11201</postalCode>
            </addr>
            <telecom nullFlavor="NA"></telecom>
            <assignedPerson>
              <name>
                <given>No</given>
                <given>Primary Care Provider On</given>
                <family>File</family>
              </name>
            </assignedPerson>
            <representedOrganization>
              <name>One Brooklyn Health - TST</name>
            </representedOrganization>
          </assignedEntity>
        </responsibleParty>
        <encounterParticipant typeCode="ATND">
          <time value="20241206"></time>
          <assignedEntity>
            <id nullFlavor="UNK"></id>
            <code code="207Q00000X" codeSystem="2.16.840.1.113883.6.101" displayName="FAMILY MEDICINE PHYSICIAN">
              <originalText>Family Medicine</originalText>
              <translation code="19" codeSystem="1.2.840.114350.********.7.10.688867.4160" codeSystemName="Epic.DXC.StandardProviderSpecialtyType" displayName="Family Medicine"></translation>
              <translation code="9" codeSystem="1.2.840.114350.1.13.382.3.7.10.836982.1050" codeSystemName="Epic.SER.ProviderSpecialty" displayName="Family Medicine"></translation>
            </code>
            <addr use="WP">
              <streetAddressLine>XXXXX Street</streetAddressLine>
              <city>New York</city>
              <state>NY</state>
              <postalCode>11201</postalCode>
            </addr>
            <telecom nullFlavor="UNK"></telecom>
            <assignedPerson>
              <name use="L">
                <given>No</given>
                <given>Primary Care Provider On</given>
                <family>File</family>
                <suffix qualifier="AC"> MD</suffix>
                <validTime>
                  <low nullFlavor="UNK"></low>
                  <high nullFlavor="UNK"></high>
                </validTime>
              </name>
            </assignedPerson>
          </assignedEntity>
        </encounterParticipant>
        <location>
          <healthCareFacility>
            <id extension="202002016" root="1.2.840.114350.1.13.382.3.7.2.686980"></id>
            <code nullFlavor="UNK">
              <originalText>Colon and Rectal Surgery</originalText>
              <translation code="14" codeSystem="1.2.840.114350.********.7.10.688867.4150" codeSystemName="Epic.DepartmentSpecialty" displayName="Colon and Rectal Surgery"></translation>
            </code>
            <location>
              <name>Brookdale Hospital Medical Center STAR Anoscopy</name>
              <addr use="WP">
                <streetAddressLine>9620 Church Ave</streetAddressLine>
                <streetAddressLine>1st Floor</streetAddressLine>
                <county>KINGS</county>
                <city>Brooklyn</city>
                <state>NY</state>
                <postalCode>11212</postalCode>
                <country>USA</country>
              </addr>
            </location>
            <serviceProviderOrganization>
              <id extension="202001" root="1.2.840.114350.1.13.382.3.7.2.696570"></id>
              <name>BROOKDALE UNIVERSITY HOSPITAL</name>
              <addr use="WP">
                <streetAddressLine>9620 Church Ave</streetAddressLine>
                <streetAddressLine>1st Floor</streetAddressLine>
                <county>KINGS</county>
                <city>Brooklyn</city>
                <state>NY</state>
                <postalCode>11212</postalCode>
                <country>USA</country>
              </addr>
              <asOrganizationPartOf>
                <wholeOrganization>
                  <name>One Brooklyn Health - TST</name>
                  <addr use="WP">
                    <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                    <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                    <city>Brooklyn</city>
                    <state>NY</state>
                    <postalCode>11212</postalCode>
                    <country>USA</country>
                  </addr>
                </wholeOrganization>
              </asOrganizationPartOf>
            </serviceProviderOrganization>
          </healthCareFacility>
        </location>
      </encompassingEncounter>
    </componentOf>
    <component>
      <structuredBody>
        <component>
          <section>
            <templateId root="2.16.840.1.113883.**********.22"></templateId>
            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.22"></templateId>
            <templateId root="2.16.840.1.113883.**********.22.1"></templateId>
            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.22.1"></templateId>
            <code code="46240-8" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="History of Hospitalizations+Outpatient visits Narrative"></code>
            <title>Encounter Details</title>
            <text>
              <table>
                <colgroup>
                  <col width="10%"></col>
                  <col width="15%"></col>
                  <col span="3" width="25%"></col>
                </colgroup>
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Type</th>
                    <th>Department</th>
                    <th>Care Team (Latest Contact Info)</th>
                    <th>Description</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ID="encounter1" styleCode="xRowNormal">
                    <td>12/06/2024</td>
                    <td ID="encounter1type">Orders Only</td>
                    <td>
                      <paragraph>Brookdale Hospital Medical Center STAR Anoscopy</paragraph>
                      <paragraph>9620 Church Ave</paragraph>
                      <paragraph>1st Floor</paragraph>
                      <paragraph>Brooklyn, NY 11212</paragraph>
                      <paragraph>718-240-8233</paragraph>
                    </td>
                    <td>
                      <paragraph styleCode="Bold">File, No Primary Care Provider On, MD</paragraph>
                      <paragraph>XXXXX Street</paragraph>
                      <paragraph>New York, NY 11201</paragraph>
                    </td>
                    <td>
                      <content ID="encounter1desc">Biomechanical lesion of rib cage (Primary Dx)</content>
                    </td>
                  </tr>
                </tbody>
              </table>
            </text>
            <entry>
              <encounter classCode="ENC" moodCode="EVN">
                <templateId root="2.16.840.1.113883.**********.49"></templateId>
                <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.49"></templateId>
                <id assigningAuthorityName="EPIC" extension="**********" root="1.2.840.114350.1.13.382.3.7.3.698084.8"></id>
                <code code="AMB" codeSystem="2.16.840.1.113883.5.4">
                  <originalText>
                    <reference value="#encounter1type"></reference>
                  </originalText>
                  <translation code="111" codeSystem="1.2.840.114350.1.13.382.3.7.4.698084.30" codeSystemName="Epic.EncounterType"></translation>
                  <translation code="111" codeSystem="1.2.840.114350.*********"></translation>
                  <translation code="0" codeSystem="1.2.840.114350.*********.1"></translation>
                </code>
                <text>
                  <reference value="#encounter1"></reference>
                </text>
                <statusCode code="completed"></statusCode>
                <effectiveTime>
                  <low value="20241206"></low>
                  <high value="20241206164943-0500"></high>
                </effectiveTime>
                <performer typeCode="PRF">
                  <time>
                    <low nullFlavor="UNK"></low>
                    <high nullFlavor="UNK"></high>
                  </time>
                  <assignedEntity classCode="ASSIGNED">
                    <id nullFlavor="UNK"></id>
                    <code code="207Q00000X" codeSystem="2.16.840.1.113883.6.101" displayName="FAMILY MEDICINE PHYSICIAN">
                      <originalText>Family Medicine</originalText>
                      <translation code="19" codeSystem="1.2.840.114350.********.7.10.688867.4160" codeSystemName="Epic.DXC.StandardProviderSpecialtyType" displayName="Family Medicine"></translation>
                      <translation code="9" codeSystem="1.2.840.114350.1.13.382.3.7.10.836982.1050" codeSystemName="Epic.SER.ProviderSpecialty" displayName="Family Medicine"></translation>
                    </code>
                    <addr use="WP">
                      <streetAddressLine>XXXXX Street</streetAddressLine>
                      <city>New York</city>
                      <state>NY</state>
                      <postalCode>11201</postalCode>
                    </addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <assignedPerson>
                      <name>
                        <given>No</given>
                        <given>Primary Care Provider On</given>
                        <family>File</family>
                        <suffix qualifier="AC">MD</suffix>
                      </name>
                    </assignedPerson>
                    <representedOrganization classCode="ORG">
                      <name>One Brooklyn Health - TST</name>
                      <telecom nullFlavor="NI"></telecom>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedEntity>
                </performer>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164943-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
                <participant typeCode="LOC">
                  <participantRole classCode="SDLOC">
                    <templateId root="2.16.840.1.113883.**********.32"></templateId>
                    <id extension="202002016" root="1.2.840.114350.1.13.382.3.7.2.686980"></id>
                    <code nullFlavor="UNK">
                      <originalText>Colon and Rectal Surgery</originalText>
                      <translation code="14" codeSystem="1.2.840.114350.********.7.10.688867.4150" codeSystemName="Epic.DepartmentSpecialty" displayName="Colon and Rectal Surgery"></translation>
                    </code>
                    <addr use="WP">
                      <streetAddressLine>9620 Church Ave</streetAddressLine>
                      <streetAddressLine>1st Floor</streetAddressLine>
                      <county>KINGS</county>
                      <city>Brooklyn</city>
                      <state>NY</state>
                      <postalCode>11212</postalCode>
                      <country>USA</country>
                    </addr>
                    <playingEntity classCode="PLC">
                      <name>Brookdale Hospital Medical Center STAR Anoscopy</name>
                      <desc>Colon and Rectal Surgery</desc>
                    </playingEntity>
                  </participantRole>
                </participant>
                <entryRelationship typeCode="COMP">
                  <act classCode="ACT" moodCode="EVN">
                    <templateId root="2.16.840.1.113883.**********.64"></templateId>
                    <code code="48767-8" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"></code>
                    <text>
                      <reference value="#encounter1desc"></reference>
                    </text>
                    <statusCode code="completed"></statusCode>
                  </act>
                </entryRelationship>
                <entryRelationship typeCode="SUBJ">
                  <act classCode="ACT" moodCode="EVN">
                    <templateId root="2.16.840.1.113883.**********.80"></templateId>
                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.80"></templateId>
                    <id extension="**********-422884-concern" root="1.2.840.114350.1.13.382.*******099.1"></id>
                    <code code="29308-4" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Diagnosis"></code>
                    <statusCode code="active"></statusCode>
                    <entryRelationship inversionInd="false" typeCode="SUBJ">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.4"></templateId>
                        <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.4"></templateId>
                        <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.4"></templateId>
                        <id extension="**********-422884" root="1.2.840.114350.1.13.382.*******099.1"></id>
                        <code code="282291009" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT">
                          <translation code="29308-4" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Diagnosis"></translation>
                        </code>
                        <text>Biomechanical lesion of rib cage</text>
                        <statusCode code="completed"></statusCode>
                        <effectiveTime>
                          <low nullFlavor="UNK"></low>
                        </effectiveTime>
                        <value code="298732005" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" xsi:type="CD">
                          <originalText>Biomechanical lesion of rib cage</originalText>
                          <translation code="M99.9" codeSystem="2.16.840.1.113883.6.90" codeSystemName="ICD10" displayName="Biomechanical lesion of rib cage"></translation>
                          <translation code="739.8" codeSystem="2.16.840.1.113883.6.103" codeSystemName="ICD9" displayName="Biomechanical lesion of rib cage"></translation>
                          <translation code="4555075" codeSystem="2.16.840.1.113883.3.247.1.1" codeSystemName="Intelligent Medical Objects ProblemIT" displayName="Biomechanical lesion of rib cage"></translation>
                        </value>
                        <author>
                          <templateId root="2.16.840.1.113883.**********.119"></templateId>
                          <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                          <time value="20241206164923-0500"></time>
                          <assignedAuthor>
                            <id extension="508447517" root="1.2.840.114350.1.13.382.*******133"></id>
                            <id extension="19746" root="1.2.840.114350.1.13.382.3.7.2.697780"></id>
                            <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                            <addr nullFlavor="UNK"></addr>
                            <telecom nullFlavor="UNK"></telecom>
                            <assignedPerson>
                              <name use="L">
                                <given>Alex</given>
                                <family>Galvin</family>
                                <validTime>
                                  <low nullFlavor="UNK"></low>
                                  <high nullFlavor="UNK"></high>
                                </validTime>
                              </name>
                            </assignedPerson>
                            <representedOrganization>
                              <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                              <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                              <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                              <name>One Brooklyn Health - TST</name>
                              <addr use="WP">
                                <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                                <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                                <city>Brooklyn</city>
                                <state>NY</state>
                                <postalCode>11212</postalCode>
                                <country>USA</country>
                              </addr>
                            </representedOrganization>
                          </assignedAuthor>
                        </author>
                        <participant typeCode="LOC">
                          <participantRole classCode="SDLOC">
                            <templateId root="2.16.840.1.113883.**********.32"></templateId>
                            <code nullFlavor="UNK">
                              <translation code="0" codeSystem="1.2.840.114350.********.7.10.698084.18465" codeSystemName="Epic.isEDDX"></translation>
                            </code>
                            <addr nullFlavor="UNK"></addr>
                            <playingEntity classCode="PLC">
                              <name nullFlavor="UNK"></name>
                            </playingEntity>
                          </participantRole>
                        </participant>
                        <entryRelationship typeCode="REFR">
                          <observation classCode="OBS" moodCode="EVN">
                            <templateId root="2.16.840.1.113883.**********.6"></templateId>
                            <templateId extension="2019-06-20" root="2.16.840.1.113883.**********.6"></templateId>
                            <code code="33999-4" codeSystem="2.16.840.1.113883.6.1" displayName="Status"></code>
                            <statusCode code="completed"></statusCode>
                            <effectiveTime>
                              <low nullFlavor="UNK"></low>
                            </effectiveTime>
                            <value code="55561003" codeSystem="2.16.840.1.113883.6.96" displayName="Active" xsi:type="CD"></value>
                          </observation>
                        </entryRelationship>
                        <entryRelationship typeCode="REFR">
                          <observation classCode="OBS" moodCode="EVN">
                            <templateId extension="2019-12-01" root="2.16.840.1.113883.**********.166"></templateId>
                            <code code="263486008" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Rank"></code>
                            <value value="1" xsi:type="INT"></value>
                          </observation>
                        </entryRelationship>
                      </observation>
                    </entryRelationship>
                  </act>
                </entryRelationship>
              </encounter>
            </entry>
          </section>
        </component>
        <component>
          <section>
            <templateId root="2.16.840.1.113883.**********.6"></templateId>
            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.6"></templateId>
            <templateId root="2.16.840.1.113883.**********.6.1"></templateId>
            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.6.1"></templateId>
            <id root="88D946C6-B424-11EF-AE93-005056BAFD93"></id>
            <code code="48765-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Allergies and adverse reactions Document"></code>
            <title>Allergies</title>
            <text>
              <content ID="nof4">No known active allergies</content>
              <footnote ID="subTitle3" styleCode="xSectionSubTitle">documented as of this encounter (statuses as of 12/06/2024)</footnote>
            </text>
            <entry>
              <act classCode="ACT" moodCode="EVN">
                <templateId root="2.16.840.1.113883.**********.30"></templateId>
                <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.30"></templateId>
                <id nullFlavor="NA"></id>
                <code code="CONC" codeSystem="2.16.840.1.113883.5.6"></code>
                <text>
                  <reference value="#nof4"></reference>
                </text>
                <statusCode code="active"></statusCode>
                <effectiveTime>
                  <low nullFlavor="UNK"></low>
                </effectiveTime>
                <entryRelationship inversionInd="false" typeCode="SUBJ">
                  <observation classCode="OBS" moodCode="EVN" negationInd="true">
                    <templateId root="2.16.840.1.113883.**********.7"></templateId>
                    <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.7"></templateId>
                    <templateId root="2.16.840.1.113883.**********.90"></templateId>
                    <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.90"></templateId>
                    <id nullFlavor="NA"></id>
                    <code code="ASSERTION" codeSystem="2.16.840.1.113883.5.4"></code>
                    <text>
                      <reference value="#nof4"></reference>
                    </text>
                    <statusCode code="completed"></statusCode>
                    <effectiveTime>
                      <low nullFlavor="UNK"></low>
                    </effectiveTime>
                    <value code="419199007" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Allergy to substance (disorder)" xsi:type="CD"></value>
                    <author>
                      <templateId root="2.16.840.1.113883.**********.119"></templateId>
                      <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                      <time value="20240507133440-0400"></time>
                      <assignedAuthor>
                        <id extension="284560602" root="1.2.840.114350.1.13.382.*******133"></id>
                        <id extension="1100256" root="1.2.840.114350.1.13.382.3.7.2.836982"></id>
                        <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                        <code code="183500000X" codeSystem="2.16.840.1.113883.6.101" displayName="PHARMACIST PHARMACIST">
                          <originalText>Pharmacist</originalText>
                          <translation code="113" codeSystem="1.2.840.114350.********.7.10.688867.4160" codeSystemName="Epic.DXC.StandardProviderSpecialtyType" displayName="Pharmacy"></translation>
                          <translation code="124" codeSystem="1.2.840.114350.1.13.382.3.7.10.836982.1050" codeSystemName="Epic.SER.ProviderSpecialty" displayName="Pharmacist"></translation>
                        </code>
                        <addr use="WP">
                          <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                          <streetAddressLine>Dept of Pharmacy</streetAddressLine>
                          <county>KINGS</county>
                          <city>Brooklyn</city>
                          <state>NY</state>
                          <postalCode>11212</postalCode>
                          <country>USA</country>
                        </addr>
                        <telecom use="WP" value="tel:******-240-8093"></telecom>
                        <assignedPerson>
                          <name use="L">
                            <given>Rita</given>
                            <family>Faour</family>
                            <suffix qualifier="AC"> PharmD</suffix>
                            <validTime>
                              <low nullFlavor="UNK"></low>
                              <high nullFlavor="UNK"></high>
                            </validTime>
                          </name>
                        </assignedPerson>
                        <representedOrganization>
                          <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                          <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                          <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                          <name>One Brooklyn Health - TST</name>
                          <addr use="WP">
                            <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                            <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                            <city>Brooklyn</city>
                            <state>NY</state>
                            <postalCode>11212</postalCode>
                            <country>USA</country>
                          </addr>
                        </representedOrganization>
                      </assignedAuthor>
                    </author>
                    <participant typeCode="CSM">
                      <participantRole classCode="MANU">
                        <playingEntity classCode="MMAT">
                          <code nullFlavor="NA"></code>
                        </playingEntity>
                      </participantRole>
                    </participant>
                    <entryRelationship inversionInd="true" typeCode="SUBJ">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.145"></templateId>
                        <code code="82606-5" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Criticality"></code>
                        <text>
                          <reference nullFlavor="UNK"></reference>
                        </text>
                        <statusCode code="completed"></statusCode>
                        <value codeSystem="2.16.840.1.113883.5.1063" nullFlavor="UNK" xsi:type="CD"></value>
                      </observation>
                    </entryRelationship>
                  </observation>
                </entryRelationship>
              </act>
            </entry>
          </section>
        </component>
        <component>
          <section>
            <templateId root="2.16.840.1.113883.**********.1"></templateId>
            <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.1"></templateId>
            <templateId root="2.16.840.1.113883.**********.1.1"></templateId>
            <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.1.1"></templateId>
            <id root="88DA53A6-B424-11EF-AE93-005056BAFD93"></id>
            <code code="10160-0" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="History of Medication use Narrative"></code>
            <title>Medications</title>
            <text>
              <content ID="nof6">No known medications</content>
              <footnote ID="subTitle5" styleCode="xSectionSubTitle">documented as of this encounter (statuses as of 12/06/2024)</footnote>
            </text>
            <entry>
              <substanceAdministration classCode="SBADM" moodCode="INT" negationInd="true">
                <templateId root="2.16.840.1.113883.**********.16"></templateId>
                <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.16"></templateId>
                <id nullFlavor="NA"></id>
                <text>
                  <reference value="#nof6"></reference>
                </text>
                <statusCode code="active"></statusCode>
                <effectiveTime nullFlavor="NA"></effectiveTime>
                <doseQuantity nullFlavor="NA"></doseQuantity>
                <consumable typeCode="CSM">
                  <manufacturedProduct classCode="MANU">
                    <templateId root="2.16.840.1.113883.**********.23"></templateId>
                    <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.23"></templateId>
                    <manufacturedMaterial>
                      <code codeSystem="2.16.840.1.113883.6.88" nullFlavor="OTH">
                        <originalText>
                          <reference value="#nof6"></reference>
                        </originalText>
                        <translation code="410942007" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Drug or medicament (substance)"></translation>
                      </code>
                    </manufacturedMaterial>
                  </manufacturedProduct>
                </consumable>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164902-0500"></time>
                  <assignedAuthor>
                    <id extension="508447517" root="1.2.840.114350.1.13.382.*******133"></id>
                    <id extension="19746" root="1.2.840.114350.1.13.382.3.7.2.697780"></id>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                  </assignedAuthor>
                </author>
              </substanceAdministration>
            </entry>
          </section>
        </component>
        <component>
          <section>
            <templateId root="2.16.840.1.113883.**********.5"></templateId>
            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.5"></templateId>
            <templateId root="2.16.840.1.113883.**********.5.1"></templateId>
            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.5.1"></templateId>
            <id root="88DB209A-B424-11EF-AE93-005056BAFD93"></id>
            <code code="11450-4" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Problem list - Reported"></code>
            <title>Active Problems</title>
            <text>
              <table>
                <colgroup>
                  <col width="60%"></col>
                  <col width="15%"></col>
                  <col width="25%"></col>
                </colgroup>
                <thead>
                  <tr>
                    <th>Problem</th>
                    <th>Noted Date</th>
                    <th>Diagnosed Date</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ID="problem10" styleCode="xRowNormal">
                    <td ID="problem10name">Biomechanical lesion of rib cage</td>
                    <td>12/06/2024</td>
                    <td></td>
                  </tr>
                  <tr ID="problem9" styleCode="xRowAlt">
                    <td ID="problem9name">Hemorrhagic stroke</td>
                    <td>05/23/2024</td>
                    <td></td>
                  </tr>
                  <tr ID="problem8" styleCode="xRowNormal">
                    <td ID="problem8name">Myasthenia gravis</td>
                    <td>05/07/2024</td>
                    <td></td>
                  </tr>
                </tbody>
              </table>
              <footnote ID="subTitle7" styleCode="xSectionSubTitle">documented as of this encounter (statuses as of 12/06/2024)</footnote>
            </text>
            <entry>
              <act classCode="ACT" moodCode="EVN">
                <templateId root="2.16.840.1.113883.**********.3"></templateId>
                <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.3"></templateId>
                <id extension="3299456-concern" root="1.2.840.114350.1.13.382.3.7.2.768076"></id>
                <code code="CONC" codeSystem="2.16.840.1.113883.5.6" codeSystemName="HL7ActClass" displayName="Concern"></code>
                <text>
                  <reference value="#problem8"></reference>
                </text>
                <statusCode code="active"></statusCode>
                <effectiveTime>
                  <low value="20240507"></low>
                </effectiveTime>
                <entryRelationship inversionInd="false" typeCode="SUBJ">
                  <observation classCode="OBS" moodCode="EVN">
                    <templateId root="2.16.840.1.113883.**********.4"></templateId>
                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.4"></templateId>
                    <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.4"></templateId>
                    <id extension="3299456" root="1.2.840.114350.1.13.382.3.7.2.768076"></id>
                    <code code="64572001" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT">
                      <translation code="75323-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"></translation>
                    </code>
                    <text>
                      <reference value="#problem8name"></reference>
                    </text>
                    <statusCode code="completed"></statusCode>
                    <effectiveTime>
                      <low value="20240507"></low>
                    </effectiveTime>
                    <value code="91637004" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" xsi:type="CD">
                      <originalText>
                        <reference value="#problem8name"></reference>
                      </originalText>
                      <translation code="G70.00" codeSystem="2.16.840.1.113883.6.90" codeSystemName="ICD10" displayName="Myasthenia gravis"></translation>
                      <translation code="358.00" codeSystem="2.16.840.1.113883.6.103" codeSystemName="ICD9" displayName="Myasthenia gravis"></translation>
                      <translation code="50152" codeSystem="2.16.840.1.113883.3.247.1.1" codeSystemName="Intelligent Medical Objects ProblemIT" displayName="Myasthenia gravis"></translation>
                    </value>
                    <author>
                      <templateId root="2.16.840.1.113883.**********.119"></templateId>
                      <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                      <time value="20240507124755-0400"></time>
                      <assignedAuthor>
                        <id extension="126302924" root="1.2.840.114350.1.13.382.*******133"></id>
                        <id extension="209825" root="1.2.840.114350.1.13.382.3.7.2.836982"></id>
                        <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                        <addr use="WP">
                          <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                          <county>KINGS</county>
                          <city>BROOKLYN</city>
                          <state>NY</state>
                          <postalCode>11212</postalCode>
                          <country>USA</country>
                        </addr>
                        <telecom use="WP" value="tel:******-240-5000"></telecom>
                        <assignedPerson>
                          <name use="L">
                            <given>Ahmad</given>
                            <family>Malik</family>
                            <suffix qualifier="AC"> MD</suffix>
                            <validTime>
                              <low nullFlavor="UNK"></low>
                              <high nullFlavor="UNK"></high>
                            </validTime>
                          </name>
                        </assignedPerson>
                        <representedOrganization>
                          <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                          <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                          <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                          <name>One Brooklyn Health - TST</name>
                          <addr use="WP">
                            <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                            <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                            <city>Brooklyn</city>
                            <state>NY</state>
                            <postalCode>11212</postalCode>
                            <country>USA</country>
                          </addr>
                        </representedOrganization>
                      </assignedAuthor>
                    </author>
                    <entryRelationship typeCode="REFR">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.6"></templateId>
                        <templateId extension="2019-06-20" root="2.16.840.1.113883.**********.6"></templateId>
                        <code code="33999-4" codeSystem="2.16.840.1.113883.6.1" displayName="Status"></code>
                        <statusCode code="completed"></statusCode>
                        <effectiveTime>
                          <low value="20240507"></low>
                        </effectiveTime>
                        <value code="55561003" codeSystem="2.16.840.1.113883.6.96" displayName="Active" xsi:type="CD"></value>
                      </observation>
                    </entryRelationship>
                  </observation>
                </entryRelationship>
              </act>
            </entry>
            <entry>
              <act classCode="ACT" moodCode="EVN">
                <templateId root="2.16.840.1.113883.**********.3"></templateId>
                <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.3"></templateId>
                <id extension="3299522-concern" root="1.2.840.114350.1.13.382.3.7.2.768076"></id>
                <code code="CONC" codeSystem="2.16.840.1.113883.5.6" codeSystemName="HL7ActClass" displayName="Concern"></code>
                <text>
                  <reference value="#problem9"></reference>
                </text>
                <statusCode code="active"></statusCode>
                <effectiveTime>
                  <low value="20240523"></low>
                </effectiveTime>
                <entryRelationship inversionInd="false" typeCode="SUBJ">
                  <observation classCode="OBS" moodCode="EVN">
                    <templateId root="2.16.840.1.113883.**********.4"></templateId>
                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.4"></templateId>
                    <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.4"></templateId>
                    <id extension="3299522" root="1.2.840.114350.1.13.382.3.7.2.768076"></id>
                    <code code="64572001" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT">
                      <translation code="75323-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"></translation>
                    </code>
                    <text>
                      <reference value="#problem9name"></reference>
                    </text>
                    <statusCode code="completed"></statusCode>
                    <effectiveTime>
                      <low value="20240523"></low>
                    </effectiveTime>
                    <value code="230706003" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" xsi:type="CD">
                      <originalText>
                        <reference value="#problem9name"></reference>
                      </originalText>
                      <translation code="I61.9" codeSystem="2.16.840.1.113883.6.90" codeSystemName="ICD10" displayName="Hemorrhagic stroke"></translation>
                      <translation code="431" codeSystem="2.16.840.1.113883.6.103" codeSystemName="ICD9" displayName="Hemorrhagic stroke"></translation>
                      <translation code="88207" codeSystem="2.16.840.1.113883.3.247.1.1" codeSystemName="Intelligent Medical Objects ProblemIT" displayName="Hemorrhagic stroke"></translation>
                    </value>
                    <author>
                      <templateId root="2.16.840.1.113883.**********.119"></templateId>
                      <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                      <time value="20240523093502-0400"></time>
                      <assignedAuthor>
                        <id extension="124464012" root="1.2.840.114350.1.13.382.*******133"></id>
                        <id extension="**********" root="2.16.840.1.113883.4.6"></id>
                        <code code="207RP1001X" codeSystem="2.16.840.1.113883.6.101" displayName="PULMONARY DISEASE PHYSICIAN">
                          <originalText>Pulmonary Disease</originalText>
                          <translation code="110" codeSystem="1.2.840.114350.********.7.10.688867.4160" codeSystemName="Epic.DXC.StandardProviderSpecialtyType" displayName="Pulmonology"></translation>
                          <translation code="128" codeSystem="1.2.840.114350.1.13.382.3.7.10.836982.1050" codeSystemName="Epic.SER.ProviderSpecialty" displayName="Pulmonary Disease"></translation>
                        </code>
                        <addr use="WP">
                          <streetAddressLine>1 BROOKDALE PLAZA</streetAddressLine>
                          <streetAddressLine>CHC 131</streetAddressLine>
                          <county>KINGS</county>
                          <city>BROOKLYN</city>
                          <state>NY</state>
                          <postalCode>11212</postalCode>
                          <country>USA</country>
                        </addr>
                        <telecom use="WP" value="tel:******-240-8280"></telecom>
                        <assignedPerson>
                          <name use="L">
                            <given>David</given>
                            <family>Rose</family>
                            <suffix qualifier="AC"> MD</suffix>
                            <validTime>
                              <low nullFlavor="UNK"></low>
                              <high nullFlavor="UNK"></high>
                            </validTime>
                          </name>
                        </assignedPerson>
                        <representedOrganization>
                          <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                          <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                          <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                          <name>One Brooklyn Health - TST</name>
                          <addr use="WP">
                            <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                            <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                            <city>Brooklyn</city>
                            <state>NY</state>
                            <postalCode>11212</postalCode>
                            <country>USA</country>
                          </addr>
                        </representedOrganization>
                      </assignedAuthor>
                    </author>
                    <entryRelationship typeCode="REFR">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.6"></templateId>
                        <templateId extension="2019-06-20" root="2.16.840.1.113883.**********.6"></templateId>
                        <code code="33999-4" codeSystem="2.16.840.1.113883.6.1" displayName="Status"></code>
                        <statusCode code="completed"></statusCode>
                        <effectiveTime>
                          <low value="20240523"></low>
                        </effectiveTime>
                        <value code="55561003" codeSystem="2.16.840.1.113883.6.96" displayName="Active" xsi:type="CD"></value>
                      </observation>
                    </entryRelationship>
                  </observation>
                </entryRelationship>
              </act>
            </entry>
            <entry>
              <act classCode="ACT" moodCode="EVN">
                <templateId root="2.16.840.1.113883.**********.3"></templateId>
                <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.3"></templateId>
                <id extension="3300183-concern" root="1.2.840.114350.1.13.382.3.7.2.768076"></id>
                <code code="CONC" codeSystem="2.16.840.1.113883.5.6" codeSystemName="HL7ActClass" displayName="Concern"></code>
                <text>
                  <reference value="#problem10"></reference>
                </text>
                <statusCode code="active"></statusCode>
                <effectiveTime>
                  <low value="20241206"></low>
                </effectiveTime>
                <entryRelationship inversionInd="false" typeCode="SUBJ">
                  <observation classCode="OBS" moodCode="EVN">
                    <templateId root="2.16.840.1.113883.**********.4"></templateId>
                    <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.4"></templateId>
                    <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.4"></templateId>
                    <id extension="3300183" root="1.2.840.114350.1.13.382.3.7.2.768076"></id>
                    <code code="64572001" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT">
                      <translation code="75323-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC"></translation>
                    </code>
                    <text>
                      <reference value="#problem10name"></reference>
                    </text>
                    <statusCode code="completed"></statusCode>
                    <effectiveTime>
                      <low value="20241206"></low>
                    </effectiveTime>
                    <value code="298732005" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" xsi:type="CD">
                      <originalText>
                        <reference value="#problem10name"></reference>
                      </originalText>
                      <translation code="M99.9" codeSystem="2.16.840.1.113883.6.90" codeSystemName="ICD10" displayName="Biomechanical lesion of rib cage"></translation>
                      <translation code="739.8" codeSystem="2.16.840.1.113883.6.103" codeSystemName="ICD9" displayName="Biomechanical lesion of rib cage"></translation>
                      <translation code="4555075" codeSystem="2.16.840.1.113883.3.247.1.1" codeSystemName="Intelligent Medical Objects ProblemIT" displayName="Biomechanical lesion of rib cage"></translation>
                    </value>
                    <author>
                      <templateId root="2.16.840.1.113883.**********.119"></templateId>
                      <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                      <time value="20241206164923-0500"></time>
                      <assignedAuthor>
                        <id extension="508447517" root="1.2.840.114350.1.13.382.*******133"></id>
                        <id extension="19746" root="1.2.840.114350.1.13.382.3.7.2.697780"></id>
                        <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      </assignedAuthor>
                    </author>
                    <entryRelationship typeCode="REFR">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.6"></templateId>
                        <templateId extension="2019-06-20" root="2.16.840.1.113883.**********.6"></templateId>
                        <code code="33999-4" codeSystem="2.16.840.1.113883.6.1" displayName="Status"></code>
                        <statusCode code="completed"></statusCode>
                        <effectiveTime>
                          <low value="20241206"></low>
                        </effectiveTime>
                        <value code="55561003" codeSystem="2.16.840.1.113883.6.96" displayName="Active" xsi:type="CD"></value>
                      </observation>
                    </entryRelationship>
                  </observation>
                </entryRelationship>
              </act>
            </entry>
          </section>
        </component>
        <component>
          <section>
            <templateId root="2.16.840.1.113883.**********.17"></templateId>
            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.17"></templateId>
            <code code="29762-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Social history Narrative"></code>
            <title>Social History</title>
            <text>
              <table ID="sochist15">
                <colgroup>
                  <col span="2" width="25%"></col>
                  <col width="13%"></col>
                  <col width="12%"></col>
                  <col width="25%"></col>
                </colgroup>
                <thead>
                  <tr>
                    <th>Tobacco Use</th>
                    <th>Types</th>
                    <th>Packs/Day</th>
                    <th>Years Used</th>
                    <th>Date</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Smoking Tobacco: Never Assessed</td>
                    <td></td>
                    <td ID="sochist15packsperday"></td>
                    <td></td>
                    <td></td>
                  </tr>
                </tbody>
              </table>
              <table ID="sdohassess2">
                <colgroup>
                  <col width="50%"></col>
                  <col span="2" width="25%"></col>
                </colgroup>
                <thead>
                  <tr>
                    <th>AHC Utilities</th>
                    <th>Answer</th>
                    <th>Date Recorded</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ID="sdohassess2pair1">
                    <td ID="sdohassess2pair1ques">In the past 12 months has the electric, gas, oil, or water company threatened to shut off services in your home?</td>
                    <td ID="sdohassess2pair1ans">Yes</td>
                    <td>12/03/2024</td>
                  </tr>
                </tbody>
              </table>
              <table ID="sdohassess5">
                <colgroup>
                  <col width="50%"></col>
                  <col span="2" width="25%"></col>
                </colgroup>
                <thead>
                  <tr>
                    <th>Hunger Vital Sign</th>
                    <th>Answer</th>
                    <th>Date Recorded</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ID="sdohassess5pair1">
                    <td ID="sdohassess5pair1ques">Within the past 12 months, you worried that your food would run out before you got the money to buy more.</td>
                    <td ID="sdohassess5pair1ans">Sometimes true</td>
                    <td>12/03/2024</td>
                  </tr>
                  <tr ID="sdohassess5pair2">
                    <td ID="sdohassess5pair2ques">Within the past 12 months, the food you bought just didn&apos;t last and you didn&apos;t have money to get more.</td>
                    <td ID="sdohassess5pair2ans">Sometimes true</td>
                    <td>12/03/2024</td>
                  </tr>
                </tbody>
              </table>
              <table ID="sdohassess3">
                <colgroup>
                  <col width="50%"></col>
                  <col span="2" width="25%"></col>
                </colgroup>
                <thead>
                  <tr>
                    <th>AHC - Inadequate Housing</th>
                    <th>Answer</th>
                    <th>Date Recorded</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ID="sdohassess3pair1">
                    <td ID="sdohassess3pair1ques">What is your living situation today?</td>
                    <td ID="sdohassess3pair1ans">I have a steady place to live</td>
                    <td>12/03/2024</td>
                  </tr>
                  <tr ID="sdohassess3pair2">
                    <td ID="sdohassess3pair2ques">Think about the place you live. Do you have problems with any of the following?</td>
                    <td ID="sdohassess3pair2ans">Mold;Lead paint or pipes</td>
                    <td>12/03/2024</td>
                  </tr>
                </tbody>
              </table>
              <table ID="sdohassess1">
                <colgroup>
                  <col width="50%"></col>
                  <col span="2" width="25%"></col>
                </colgroup>
                <thead>
                  <tr>
                    <th>AHC - Transportation</th>
                    <th>Answer</th>
                    <th>Date Recorded</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ID="sdohassess1pair1">
                    <td ID="sdohassess1pair1ques">In the past 12 months, has lack of reliable transportation kept you from medical appointments, meetings, work or from getting things needed for daily living?</td>
                    <td ID="sdohassess1pair1ans">Yes</td>
                    <td>12/03/2024</td>
                  </tr>
                </tbody>
              </table>
              <table ID="sdohassess4">
                <colgroup>
                  <col width="50%"></col>
                  <col span="2" width="25%"></col>
                </colgroup>
                <thead>
                  <tr>
                    <th>AHC - Personal Safety</th>
                    <th>Answer</th>
                    <th>Date Recorded</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ID="sdohassess4pair1">
                    <td ID="sdohassess4pair1ques">How often does anyone, including family and friends, physically hurt you?</td>
                    <td ID="sdohassess4pair1ans">Never</td>
                    <td>12/03/2024</td>
                  </tr>
                  <tr ID="sdohassess4pair2">
                    <td ID="sdohassess4pair2ques">How often does anyone, including family and friends, insult or talk down to you?</td>
                    <td ID="sdohassess4pair2ans">Never</td>
                    <td>12/03/2024</td>
                  </tr>
                  <tr ID="sdohassess4pair3">
                    <td ID="sdohassess4pair3ques">How often does anyone, including family and friends, threaten you with harm?</td>
                    <td ID="sdohassess4pair3ans">Never</td>
                    <td>12/03/2024</td>
                  </tr>
                  <tr ID="sdohassess4pair4">
                    <td ID="sdohassess4pair4ques">How often does anyone, including family and friends, scream or curse at you?</td>
                    <td ID="sdohassess4pair4ans">Never</td>
                    <td>12/03/2024</td>
                  </tr>
                </tbody>
              </table>
              <table ID="sdohassess8">
                <colgroup>
                  <col width="50%"></col>
                  <col span="2" width="25%"></col>
                </colgroup>
                <thead>
                  <tr>
                    <th>AHC - Financial Strain</th>
                    <th>Answer</th>
                    <th>Date Recorded</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ID="sdohassess8pair1">
                    <td ID="sdohassess8pair1ques">How hard is it for you to pay for the very basics like food, housing, medical care, and heating? Would you say it is:</td>
                    <td ID="sdohassess8pair1ans">Somewhat hard</td>
                    <td>12/03/2024</td>
                  </tr>
                </tbody>
              </table>
              <table ID="sdohassess6">
                <colgroup>
                  <col width="50%"></col>
                  <col span="2" width="25%"></col>
                </colgroup>
                <thead>
                  <tr>
                    <th>AHC - Employment</th>
                    <th>Answer</th>
                    <th>Date Recorded</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ID="sdohassess6pair1">
                    <td ID="sdohassess6pair1ques">Do you want help finding or keeping work or a job?</td>
                    <td ID="sdohassess6pair1ans">I do not need or want help</td>
                    <td>12/03/2024</td>
                  </tr>
                </tbody>
              </table>
              <table ID="sdohassess7">
                <colgroup>
                  <col width="50%"></col>
                  <col span="2" width="25%"></col>
                </colgroup>
                <thead>
                  <tr>
                    <th>AHC - Education</th>
                    <th>Answer</th>
                    <th>Date Recorded</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ID="sdohassess7pair1">
                    <td ID="sdohassess7pair1ques">Speaks Language Other Than English at Home</td>
                    <td ID="sdohassess7pair1ans">Not on file</td>
                    <td>12/03/2024</td>
                  </tr>
                  <tr ID="sdohassess7pair2">
                    <td ID="sdohassess7pair2ques">Do you want help with school or training? For example, starting or completing job training or getting a high school diploma, GED or equivalent.</td>
                    <td ID="sdohassess7pair2ans">Yes</td>
                    <td>12/03/2024</td>
                  </tr>
                </tbody>
              </table>
              <table>
                <colgroup>
                  <col width="25%"></col>
                  <col width="75%"></col>
                </colgroup>
                <thead>
                  <tr>
                    <th>Pregnant</th>
                    <th>Comments</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Unknown</td>
                    <td></td>
                  </tr>
                </tbody>
              </table>
              <table>
                <colgroup>
                  <col width="50%"></col>
                  <col span="2" width="25%"></col>
                </colgroup>
                <thead>
                  <tr>
                    <th>Sex and Gender Information</th>
                    <th>Value</th>
                    <th>Date Recorded</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ID="BirthSex18">
                    <td>Sex Assigned at Birth</td>
                    <td ID="BirthSex18Value">Not on file</td>
                    <td></td>
                  </tr>
                  <tr ID="LegalSex19">
                    <td>Legal Sex</td>
                    <td ID="LegalSex19Value">Female</td>
                    <td>05/07/2024 10:53 AM EDT</td>
                  </tr>
                  <tr ID="GenderIdentity16">
                    <td>Gender Identity</td>
                    <td ID="GenderIdentity16Value">Not on file</td>
                    <td></td>
                  </tr>
                  <tr ID="SexualOrientation17">
                    <td>Sexual Orientation</td>
                    <td ID="SexualOrientation17Value">Not on file</td>
                    <td></td>
                  </tr>
                </tbody>
              </table>
              <footnote ID="subTitle14" styleCode="xSectionSubTitle">documented as of this encounter</footnote>
            </text>
            <entry>
              <observation classCode="OBS" moodCode="EVN">
                <templateId root="2.16.840.1.113883.**********.78"></templateId>
                <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.78"></templateId>
                <id extension="Z1006841^^72166-2" root="1.2.840.114350.1.13.382.*******040.1"></id>
                <code code="72166-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Tobacco smoking status NHIS"></code>
                <text>
                  <reference value="#sochist15"></reference>
                </text>
                <statusCode code="completed"></statusCode>
                <effectiveTime nullFlavor="UNK"></effectiveTime>
                <value code="266927001" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Tobacco smoking consumption unknown" xsi:type="CD"></value>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time nullFlavor="UNK"></time>
                  <assignedAuthor>
                    <id extension="508447517" root="1.2.840.114350.1.13.382.*******133"></id>
                    <id extension="19746" root="1.2.840.114350.1.13.382.3.7.2.697780"></id>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="EVN">
                <templateId root="2.16.840.1.113883.**********.38"></templateId>
                <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.38"></templateId>
                <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.38"></templateId>
                <id extension="**********-95001-Z1006841" root="1.2.840.114350.1.13.382.*******040.21"></id>
                <code code="8689-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="History of Social function"></code>
                <statusCode code="completed"></statusCode>
                <effectiveTime value="20241203"></effectiveTime>
                <entryRelationship typeCode="SPRT">
                  <observation classCode="OBS" moodCode="EVN">
                    <templateId root="2.16.840.1.113883.**********.69"></templateId>
                    <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.69"></templateId>
                    <id extension="**********-95001-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                    <code nullFlavor="OTH">
                      <originalText>AHC Utilities</originalText>
                      <translation code="X-SDOH-SDC-95001" codeSystem="1.2.840.114350.********.1" codeSystemName="Epic.Sdoh" displayName="AHC Utilities"></translation>
                    </code>
                    <text>
                      <reference value="#sdohassess2"></reference>
                    </text>
                    <statusCode code="completed"></statusCode>
                    <effectiveTime value="20241203"></effectiveTime>
                    <value nullFlavor="UNK" xsi:type="CD"></value>
                    <interpretationCode nullFlavor="OTH">
                      <originalText>At Risk</originalText>
                      <translation code="X-SDOH-RISK-3" codeSystem="1.2.840.114350.********.1" codeSystemName="Epic.Sdoh" displayName="High Risk"></translation>
                    </interpretationCode>
                    <entryRelationship typeCode="COMP">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.86"></templateId>
                        <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.86"></templateId>
                        <id extension="cer97157-**********-95001-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                        <code code="96779-4" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Has the electric, gas, oil, or water company threatened to shut off services in your home in past 12Mo">
                          <originalText>
                            <reference value="#sdohassess2pair1ques"></reference>
                          </originalText>
                        </code>
                        <text>
                          <reference value="#sdohassess2pair1"></reference>
                        </text>
                        <statusCode code="completed"></statusCode>
                        <value code="LA33-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Yes" xsi:type="CD">
                          <originalText>
                            <reference value="#sdohassess2pair1ans"></reference>
                          </originalText>
                        </value>
                        <author>
                          <templateId root="2.16.840.1.113883.**********.119"></templateId>
                          <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                          <time value="20241203081714-0500"></time>
                          <assignedAuthor>
                            <id extension="50181394" root="1.2.840.114350.1.13.382.*******133"></id>
                            <id extension="100334" root="1.2.840.114350.1.13.382.3.7.2.836982"></id>
                            <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                            <addr use="WP">
                              <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                              <city>Brooklyn</city>
                              <state>NY</state>
                              <postalCode>11201</postalCode>
                            </addr>
                            <telecom use="WP" value="tel:******-240-5000"></telecom>
                            <assignedPerson>
                              <name use="L">
                                <given>Mira</given>
                                <family>Bae</family>
                                <suffix qualifier="AC"> RN</suffix>
                                <validTime>
                                  <low nullFlavor="UNK"></low>
                                  <high nullFlavor="UNK"></high>
                                </validTime>
                              </name>
                            </assignedPerson>
                            <representedOrganization>
                              <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                              <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                              <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                              <name>One Brooklyn Health - TST</name>
                              <addr use="WP">
                                <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                                <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                                <city>Brooklyn</city>
                                <state>NY</state>
                                <postalCode>11212</postalCode>
                                <country>USA</country>
                              </addr>
                            </representedOrganization>
                          </assignedAuthor>
                        </author>
                      </observation>
                    </entryRelationship>
                  </observation>
                </entryRelationship>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="EVN">
                <templateId root="2.16.840.1.113883.**********.38"></templateId>
                <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.38"></templateId>
                <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.38"></templateId>
                <id extension="**********-97008-Z1006841" root="1.2.840.114350.1.13.382.*******040.21"></id>
                <code code="8689-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="History of Social function"></code>
                <statusCode code="completed"></statusCode>
                <effectiveTime value="20241203"></effectiveTime>
                <entryRelationship typeCode="SPRT">
                  <observation classCode="OBS" moodCode="EVN">
                    <templateId root="2.16.840.1.113883.**********.69"></templateId>
                    <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.69"></templateId>
                    <id extension="**********-97008-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                    <code code="88121-9" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Hunger Vital Sign [HVS]">
                      <originalText>Hunger Vital Sign</originalText>
                    </code>
                    <text>
                      <reference value="#sdohassess5"></reference>
                    </text>
                    <statusCode code="completed"></statusCode>
                    <effectiveTime value="20241203"></effectiveTime>
                    <value nullFlavor="UNK" xsi:type="CD"></value>
                    <interpretationCode nullFlavor="OTH">
                      <originalText>Food Insecurity Present</originalText>
                      <translation code="X-SDOH-RISK-3" codeSystem="1.2.840.114350.********.1" codeSystemName="Epic.Sdoh" displayName="High Risk"></translation>
                    </interpretationCode>
                    <entryRelationship typeCode="COMP">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.86"></templateId>
                        <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.86"></templateId>
                        <id extension="cer97070-**********-97008-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                        <code code="88122-7" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="(I/We) worried whether (my/our) food would run out before (I/we) got money to buy more.  ">
                          <originalText>
                            <reference value="#sdohassess5pair1ques"></reference>
                          </originalText>
                        </code>
                        <text>
                          <reference value="#sdohassess5pair1"></reference>
                        </text>
                        <statusCode code="completed"></statusCode>
                        <value code="LA6729-3" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Sometimes true" xsi:type="CD">
                          <originalText>
                            <reference value="#sdohassess5pair1ans"></reference>
                          </originalText>
                        </value>
                        <author>
                          <templateId root="2.16.840.1.113883.**********.119"></templateId>
                          <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                          <time value="20241203081714-0500"></time>
                          <assignedAuthor>
                            <id extension="50181394" root="1.2.840.114350.1.13.382.*******133"></id>
                            <id extension="100334" root="1.2.840.114350.1.13.382.3.7.2.836982"></id>
                            <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                          </assignedAuthor>
                        </author>
                      </observation>
                    </entryRelationship>
                    <entryRelationship typeCode="COMP">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.86"></templateId>
                        <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.86"></templateId>
                        <id extension="cer97071-**********-97008-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                        <code code="88123-5" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="The food that (I/we) bought just didn&apos;t last, and (I/we) didn&apos;t have money to get more.">
                          <originalText>
                            <reference value="#sdohassess5pair2ques"></reference>
                          </originalText>
                        </code>
                        <text>
                          <reference value="#sdohassess5pair2"></reference>
                        </text>
                        <statusCode code="completed"></statusCode>
                        <value code="LA6729-3" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Sometimes true" xsi:type="CD">
                          <originalText>
                            <reference value="#sdohassess5pair2ans"></reference>
                          </originalText>
                        </value>
                        <author>
                          <templateId root="2.16.840.1.113883.**********.119"></templateId>
                          <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                          <time value="20241203081714-0500"></time>
                          <assignedAuthor>
                            <id extension="50181394" root="1.2.840.114350.1.13.382.*******133"></id>
                            <id extension="100334" root="1.2.840.114350.1.13.382.3.7.2.836982"></id>
                            <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                          </assignedAuthor>
                        </author>
                      </observation>
                    </entryRelationship>
                  </observation>
                </entryRelationship>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="EVN">
                <templateId root="2.16.840.1.113883.**********.38"></templateId>
                <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.38"></templateId>
                <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.38"></templateId>
                <id extension="**********-97028-Z1006841" root="1.2.840.114350.1.13.382.*******040.21"></id>
                <code code="8689-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="History of Social function"></code>
                <statusCode code="completed"></statusCode>
                <effectiveTime value="20241203"></effectiveTime>
                <entryRelationship typeCode="SPRT">
                  <observation classCode="OBS" moodCode="EVN">
                    <templateId root="2.16.840.1.113883.**********.69"></templateId>
                    <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.69"></templateId>
                    <id extension="**********-97028-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                    <code nullFlavor="OTH">
                      <originalText>AHC - Inadequate Housing</originalText>
                      <translation code="X-SDOH-SDC-97028" codeSystem="1.2.840.114350.********.1" codeSystemName="Epic.Sdoh" displayName="AHC - Inadequate Housing"></translation>
                    </code>
                    <text>
                      <reference value="#sdohassess3"></reference>
                    </text>
                    <statusCode code="completed"></statusCode>
                    <effectiveTime value="20241203"></effectiveTime>
                    <value nullFlavor="UNK" xsi:type="CD"></value>
                    <interpretationCode nullFlavor="OTH">
                      <originalText>At Risk</originalText>
                      <translation code="X-SDOH-RISK-3" codeSystem="1.2.840.114350.********.1" codeSystemName="Epic.Sdoh" displayName="High Risk"></translation>
                    </interpretationCode>
                    <entryRelationship typeCode="COMP">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.86"></templateId>
                        <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.86"></templateId>
                        <id extension="cer83782-**********-97028-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                        <code code="71802-3" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Housing status">
                          <originalText>
                            <reference value="#sdohassess3pair1ques"></reference>
                          </originalText>
                        </code>
                        <text>
                          <reference value="#sdohassess3pair1"></reference>
                        </text>
                        <statusCode code="completed"></statusCode>
                        <value nullFlavor="OTH" xsi:type="CD">
                          <originalText>
                            <reference value="#sdohassess3pair1ans"></reference>
                          </originalText>
                          <translation code="X-SDOH-FLO-1570000065-1" codeSystem="1.2.840.114350.********.1" codeSystemName="Epic.Sdoh" displayName="I have a steady place to live"></translation>
                        </value>
                        <author>
                          <templateId root="2.16.840.1.113883.**********.119"></templateId>
                          <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                          <time value="20241203081714-0500"></time>
                          <assignedAuthor>
                            <id extension="50181394" root="1.2.840.114350.1.13.382.*******133"></id>
                            <id extension="100334" root="1.2.840.114350.1.13.382.3.7.2.836982"></id>
                            <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                          </assignedAuthor>
                        </author>
                      </observation>
                    </entryRelationship>
                    <entryRelationship typeCode="COMP">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.86"></templateId>
                        <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.86"></templateId>
                        <id extension="cer83783-**********-97028-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                        <code code="96778-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Problems with place where you live">
                          <originalText>
                            <reference value="#sdohassess3pair2ques"></reference>
                          </originalText>
                        </code>
                        <text>
                          <reference value="#sdohassess3pair2"></reference>
                        </text>
                        <statusCode code="completed"></statusCode>
                        <value nullFlavor="OTH" xsi:type="CD">
                          <originalText>
                            <reference value="#sdohassess3pair2ans"></reference>
                          </originalText>
                          <translation code="X-SDOH-FLO-1570000066-Mold;Lead paint or pipes" codeSystem="1.2.840.114350.********.1" codeSystemName="Epic.Sdoh" displayName="Mold;Lead paint or pipes"></translation>
                        </value>
                        <author>
                          <templateId root="2.16.840.1.113883.**********.119"></templateId>
                          <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                          <time value="20241203081714-0500"></time>
                          <assignedAuthor>
                            <id extension="50181394" root="1.2.840.114350.1.13.382.*******133"></id>
                            <id extension="100334" root="1.2.840.114350.1.13.382.3.7.2.836982"></id>
                            <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                          </assignedAuthor>
                        </author>
                      </observation>
                    </entryRelationship>
                  </observation>
                </entryRelationship>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="EVN">
                <templateId root="2.16.840.1.113883.**********.38"></templateId>
                <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.38"></templateId>
                <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.38"></templateId>
                <id extension="**********-97035-Z1006841" root="1.2.840.114350.1.13.382.*******040.21"></id>
                <code code="8689-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="History of Social function"></code>
                <statusCode code="completed"></statusCode>
                <effectiveTime value="20241203"></effectiveTime>
                <entryRelationship typeCode="SPRT">
                  <observation classCode="OBS" moodCode="EVN">
                    <templateId root="2.16.840.1.113883.**********.69"></templateId>
                    <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.69"></templateId>
                    <id extension="**********-97035-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                    <code nullFlavor="OTH">
                      <originalText>AHC - Transportation</originalText>
                      <translation code="X-SDOH-SDC-97035" codeSystem="1.2.840.114350.********.1" codeSystemName="Epic.Sdoh" displayName="AHC - Transportation"></translation>
                    </code>
                    <text>
                      <reference value="#sdohassess1"></reference>
                    </text>
                    <statusCode code="completed"></statusCode>
                    <effectiveTime value="20241203"></effectiveTime>
                    <value nullFlavor="UNK" xsi:type="CD"></value>
                    <interpretationCode nullFlavor="OTH">
                      <originalText>At Risk</originalText>
                      <translation code="X-SDOH-RISK-3" codeSystem="1.2.840.114350.********.1" codeSystemName="Epic.Sdoh" displayName="High Risk"></translation>
                    </interpretationCode>
                    <entryRelationship typeCode="COMP">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.86"></templateId>
                        <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.86"></templateId>
                        <id extension="cer83784-**********-97035-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                        <code code="93030-5" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Has lack of transportation kept you from medical appointments, meetings, work, or from getting things needed for daily living">
                          <originalText>
                            <reference value="#sdohassess1pair1ques"></reference>
                          </originalText>
                        </code>
                        <text>
                          <reference value="#sdohassess1pair1"></reference>
                        </text>
                        <statusCode code="completed"></statusCode>
                        <value code="LA33-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Yes" xsi:type="CD">
                          <originalText>
                            <reference value="#sdohassess1pair1ans"></reference>
                          </originalText>
                        </value>
                        <author>
                          <templateId root="2.16.840.1.113883.**********.119"></templateId>
                          <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                          <time value="20241203081714-0500"></time>
                          <assignedAuthor>
                            <id extension="50181394" root="1.2.840.114350.1.13.382.*******133"></id>
                            <id extension="100334" root="1.2.840.114350.1.13.382.3.7.2.836982"></id>
                            <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                          </assignedAuthor>
                        </author>
                      </observation>
                    </entryRelationship>
                  </observation>
                </entryRelationship>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="EVN">
                <templateId root="2.16.840.1.113883.**********.38"></templateId>
                <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.38"></templateId>
                <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.38"></templateId>
                <id extension="**********-97036-Z1006841" root="1.2.840.114350.1.13.382.*******040.21"></id>
                <code code="8689-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="History of Social function"></code>
                <statusCode code="completed"></statusCode>
                <effectiveTime value="20241203"></effectiveTime>
                <entryRelationship typeCode="SPRT">
                  <observation classCode="OBS" moodCode="EVN">
                    <templateId root="2.16.840.1.113883.**********.69"></templateId>
                    <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.69"></templateId>
                    <id extension="**********-97036-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                    <code nullFlavor="OTH">
                      <originalText>AHC - Personal Safety</originalText>
                      <translation code="X-SDOH-SDC-97036" codeSystem="1.2.840.114350.********.1" codeSystemName="Epic.Sdoh" displayName="AHC - Personal Safety"></translation>
                    </code>
                    <text>
                      <reference value="#sdohassess4"></reference>
                    </text>
                    <statusCode code="completed"></statusCode>
                    <effectiveTime value="20241203"></effectiveTime>
                    <value nullFlavor="UNK" xsi:type="CD"></value>
                    <interpretationCode nullFlavor="OTH">
                      <originalText>Not At Risk</originalText>
                      <translation code="X-SDOH-RISK-1" codeSystem="1.2.840.114350.********.1" codeSystemName="Epic.Sdoh" displayName="Low Risk "></translation>
                    </interpretationCode>
                    <entryRelationship typeCode="COMP">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.86"></templateId>
                        <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.86"></templateId>
                        <id extension="cer83785-**********-97036-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                        <code code="95618-5" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Physically hurt you">
                          <originalText>
                            <reference value="#sdohassess4pair1ques"></reference>
                          </originalText>
                        </code>
                        <text>
                          <reference value="#sdohassess4pair1"></reference>
                        </text>
                        <statusCode code="completed"></statusCode>
                        <value code="LA6270-8" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Never" xsi:type="CD">
                          <originalText>
                            <reference value="#sdohassess4pair1ans"></reference>
                          </originalText>
                        </value>
                        <author>
                          <templateId root="2.16.840.1.113883.**********.119"></templateId>
                          <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                          <time value="20241203081714-0500"></time>
                          <assignedAuthor>
                            <id extension="50181394" root="1.2.840.114350.1.13.382.*******133"></id>
                            <id extension="100334" root="1.2.840.114350.1.13.382.3.7.2.836982"></id>
                            <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                          </assignedAuthor>
                        </author>
                      </observation>
                    </entryRelationship>
                    <entryRelationship typeCode="COMP">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.86"></templateId>
                        <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.86"></templateId>
                        <id extension="cer83786-**********-97036-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                        <code code="95617-7" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Insult you or talk down to you">
                          <originalText>
                            <reference value="#sdohassess4pair2ques"></reference>
                          </originalText>
                        </code>
                        <text>
                          <reference value="#sdohassess4pair2"></reference>
                        </text>
                        <statusCode code="completed"></statusCode>
                        <value code="LA6270-8" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Never" xsi:type="CD">
                          <originalText>
                            <reference value="#sdohassess4pair2ans"></reference>
                          </originalText>
                        </value>
                        <author>
                          <templateId root="2.16.840.1.113883.**********.119"></templateId>
                          <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                          <time value="20241203081714-0500"></time>
                          <assignedAuthor>
                            <id extension="50181394" root="1.2.840.114350.1.13.382.*******133"></id>
                            <id extension="100334" root="1.2.840.114350.1.13.382.3.7.2.836982"></id>
                            <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                          </assignedAuthor>
                        </author>
                      </observation>
                    </entryRelationship>
                    <entryRelationship typeCode="COMP">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.86"></templateId>
                        <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.86"></templateId>
                        <id extension="cer83787-**********-97036-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                        <code code="95616-9" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Threaten you with physical harm">
                          <originalText>
                            <reference value="#sdohassess4pair3ques"></reference>
                          </originalText>
                        </code>
                        <text>
                          <reference value="#sdohassess4pair3"></reference>
                        </text>
                        <statusCode code="completed"></statusCode>
                        <value code="LA6270-8" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Never" xsi:type="CD">
                          <originalText>
                            <reference value="#sdohassess4pair3ans"></reference>
                          </originalText>
                        </value>
                        <author>
                          <templateId root="2.16.840.1.113883.**********.119"></templateId>
                          <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                          <time value="20241203081714-0500"></time>
                          <assignedAuthor>
                            <id extension="50181394" root="1.2.840.114350.1.13.382.*******133"></id>
                            <id extension="100334" root="1.2.840.114350.1.13.382.3.7.2.836982"></id>
                            <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                          </assignedAuthor>
                        </author>
                      </observation>
                    </entryRelationship>
                    <entryRelationship typeCode="COMP">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.86"></templateId>
                        <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.86"></templateId>
                        <id extension="cer83788-**********-97036-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                        <code code="95615-1" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Scream or curse at you">
                          <originalText>
                            <reference value="#sdohassess4pair4ques"></reference>
                          </originalText>
                        </code>
                        <text>
                          <reference value="#sdohassess4pair4"></reference>
                        </text>
                        <statusCode code="completed"></statusCode>
                        <value code="LA6270-8" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Never" xsi:type="CD">
                          <originalText>
                            <reference value="#sdohassess4pair4ans"></reference>
                          </originalText>
                        </value>
                        <author>
                          <templateId root="2.16.840.1.113883.**********.119"></templateId>
                          <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                          <time value="20241203081714-0500"></time>
                          <assignedAuthor>
                            <id extension="50181394" root="1.2.840.114350.1.13.382.*******133"></id>
                            <id extension="100334" root="1.2.840.114350.1.13.382.3.7.2.836982"></id>
                            <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                          </assignedAuthor>
                        </author>
                      </observation>
                    </entryRelationship>
                  </observation>
                </entryRelationship>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="EVN">
                <templateId root="2.16.840.1.113883.**********.38"></templateId>
                <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.38"></templateId>
                <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.38"></templateId>
                <id extension="**********-97037-Z1006841" root="1.2.840.114350.1.13.382.*******040.21"></id>
                <code code="8689-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="History of Social function"></code>
                <statusCode code="completed"></statusCode>
                <effectiveTime value="20241203"></effectiveTime>
                <entryRelationship typeCode="SPRT">
                  <observation classCode="OBS" moodCode="EVN">
                    <templateId root="2.16.840.1.113883.**********.69"></templateId>
                    <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.69"></templateId>
                    <id extension="**********-97037-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                    <code nullFlavor="OTH">
                      <originalText>AHC - Financial Strain</originalText>
                      <translation code="X-SDOH-SDC-97037" codeSystem="1.2.840.114350.********.1" codeSystemName="Epic.Sdoh" displayName="AHC - Financial Strain"></translation>
                    </code>
                    <text>
                      <reference value="#sdohassess8"></reference>
                    </text>
                    <statusCode code="completed"></statusCode>
                    <effectiveTime value="20241203"></effectiveTime>
                    <value nullFlavor="UNK" xsi:type="CD"></value>
                    <interpretationCode nullFlavor="OTH">
                      <originalText>At Risk</originalText>
                      <translation code="X-SDOH-RISK-3" codeSystem="1.2.840.114350.********.1" codeSystemName="Epic.Sdoh" displayName="High Risk"></translation>
                    </interpretationCode>
                    <entryRelationship typeCode="COMP">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.86"></templateId>
                        <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.86"></templateId>
                        <id extension="cer83789-**********-97037-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                        <code code="76513-1" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="How hard is it for you to pay for the very basics like food, housing, medical care, and heating">
                          <originalText>
                            <reference value="#sdohassess8pair1ques"></reference>
                          </originalText>
                        </code>
                        <text>
                          <reference value="#sdohassess8pair1"></reference>
                        </text>
                        <statusCode code="completed"></statusCode>
                        <value code="LA22683-9" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Somewhat hard" xsi:type="CD">
                          <originalText>
                            <reference value="#sdohassess8pair1ans"></reference>
                          </originalText>
                        </value>
                        <author>
                          <templateId root="2.16.840.1.113883.**********.119"></templateId>
                          <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                          <time value="20241203081714-0500"></time>
                          <assignedAuthor>
                            <id extension="50181394" root="1.2.840.114350.1.13.382.*******133"></id>
                            <id extension="100334" root="1.2.840.114350.1.13.382.3.7.2.836982"></id>
                            <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                          </assignedAuthor>
                        </author>
                      </observation>
                    </entryRelationship>
                  </observation>
                </entryRelationship>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="EVN">
                <templateId root="2.16.840.1.113883.**********.38"></templateId>
                <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.38"></templateId>
                <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.38"></templateId>
                <id extension="**********-97038-Z1006841" root="1.2.840.114350.1.13.382.*******040.21"></id>
                <code code="8689-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="History of Social function"></code>
                <statusCode code="completed"></statusCode>
                <effectiveTime value="20241203"></effectiveTime>
                <entryRelationship typeCode="SPRT">
                  <observation classCode="OBS" moodCode="EVN">
                    <templateId root="2.16.840.1.113883.**********.69"></templateId>
                    <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.69"></templateId>
                    <id extension="**********-97038-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                    <code nullFlavor="OTH">
                      <originalText>AHC - Employment</originalText>
                      <translation code="X-SDOH-SDC-97038" codeSystem="1.2.840.114350.********.1" codeSystemName="Epic.Sdoh" displayName="AHC - Employment"></translation>
                    </code>
                    <text>
                      <reference value="#sdohassess6"></reference>
                    </text>
                    <statusCode code="completed"></statusCode>
                    <effectiveTime value="20241203"></effectiveTime>
                    <value nullFlavor="UNK" xsi:type="CD"></value>
                    <interpretationCode nullFlavor="OTH">
                      <originalText>Not At Risk</originalText>
                      <translation code="X-SDOH-RISK-1" codeSystem="1.2.840.114350.********.1" codeSystemName="Epic.Sdoh" displayName="Low Risk "></translation>
                    </interpretationCode>
                    <entryRelationship typeCode="COMP">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.86"></templateId>
                        <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.86"></templateId>
                        <id extension="cer83790-**********-97038-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                        <code code="96780-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Wants help finding or keeping work or a job">
                          <originalText>
                            <reference value="#sdohassess6pair1ques"></reference>
                          </originalText>
                        </code>
                        <text>
                          <reference value="#sdohassess6pair1"></reference>
                        </text>
                        <statusCode code="completed"></statusCode>
                        <value code="LA31983-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="I do not need or want help" xsi:type="CD">
                          <originalText>
                            <reference value="#sdohassess6pair1ans"></reference>
                          </originalText>
                        </value>
                        <author>
                          <templateId root="2.16.840.1.113883.**********.119"></templateId>
                          <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                          <time value="20241203081714-0500"></time>
                          <assignedAuthor>
                            <id extension="50181394" root="1.2.840.114350.1.13.382.*******133"></id>
                            <id extension="100334" root="1.2.840.114350.1.13.382.3.7.2.836982"></id>
                            <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                          </assignedAuthor>
                        </author>
                      </observation>
                    </entryRelationship>
                  </observation>
                </entryRelationship>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="EVN">
                <templateId root="2.16.840.1.113883.**********.38"></templateId>
                <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.38"></templateId>
                <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.38"></templateId>
                <id extension="**********-97040-Z1006841" root="1.2.840.114350.1.13.382.*******040.21"></id>
                <code code="8689-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="History of Social function"></code>
                <statusCode code="completed"></statusCode>
                <effectiveTime value="20241203"></effectiveTime>
                <entryRelationship typeCode="SPRT">
                  <observation classCode="OBS" moodCode="EVN">
                    <templateId root="2.16.840.1.113883.**********.69"></templateId>
                    <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.69"></templateId>
                    <id extension="**********-97040-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                    <code nullFlavor="OTH">
                      <originalText>AHC - Education</originalText>
                      <translation code="X-SDOH-SDC-97040" codeSystem="1.2.840.114350.********.1" codeSystemName="Epic.Sdoh" displayName="AHC - Education"></translation>
                    </code>
                    <text>
                      <reference value="#sdohassess7"></reference>
                    </text>
                    <statusCode code="completed"></statusCode>
                    <effectiveTime value="20241203"></effectiveTime>
                    <value nullFlavor="UNK" xsi:type="CD"></value>
                    <interpretationCode nullFlavor="OTH">
                      <originalText>At Risk</originalText>
                      <translation code="X-SDOH-RISK-3" codeSystem="1.2.840.114350.********.1" codeSystemName="Epic.Sdoh" displayName="High Risk"></translation>
                    </interpretationCode>
                    <entryRelationship typeCode="COMP">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.86"></templateId>
                        <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.86"></templateId>
                        <id extension="cer83793-**********-97040-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                        <code nullFlavor="UNK">
                          <originalText>
                            <reference value="#sdohassess7pair1ques"></reference>
                          </originalText>
                        </code>
                        <text>
                          <reference value="#sdohassess7pair1"></reference>
                        </text>
                        <statusCode code="completed"></statusCode>
                        <value nullFlavor="OTH" xsi:type="CD">
                          <originalText>
                            <reference value="#sdohassess7pair1ans"></reference>
                          </originalText>
                        </value>
                      </observation>
                    </entryRelationship>
                    <entryRelationship typeCode="COMP">
                      <observation classCode="OBS" moodCode="EVN">
                        <templateId root="2.16.840.1.113883.**********.86"></templateId>
                        <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.86"></templateId>
                        <id extension="cer83794-**********-97040-Z1006841" root="1.2.840.114350.1.13.382.3.7.1.83687972"></id>
                        <code code="96782-8" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Wants help with school or training">
                          <originalText>
                            <reference value="#sdohassess7pair2ques"></reference>
                          </originalText>
                        </code>
                        <text>
                          <reference value="#sdohassess7pair2"></reference>
                        </text>
                        <statusCode code="completed"></statusCode>
                        <value code="LA33-6" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Yes" xsi:type="CD">
                          <originalText>
                            <reference value="#sdohassess7pair2ans"></reference>
                          </originalText>
                        </value>
                        <author>
                          <templateId root="2.16.840.1.113883.**********.119"></templateId>
                          <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                          <time value="20241203081714-0500"></time>
                          <assignedAuthor>
                            <id extension="50181394" root="1.2.840.114350.1.13.382.*******133"></id>
                            <id extension="100334" root="1.2.840.114350.1.13.382.3.7.2.836982"></id>
                            <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                          </assignedAuthor>
                        </author>
                      </observation>
                    </entryRelationship>
                  </observation>
                </entryRelationship>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="EVN">
                <templateId root="2.16.840.1.113883.**********.8"></templateId>
                <id extension="Z1006841^^77386006" root="1.2.840.114350.1.13.382.*******040.6"></id>
                <code code="ASSERTION" codeSystem="2.16.840.1.113883.5.4"></code>
                <statusCode code="completed"></statusCode>
                <effectiveTime nullFlavor="UNK"></effectiveTime>
                <value code="146799005" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMED CT" displayName="Possible pregnancy" xsi:type="CD"></value>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="EVN">
                <templateId extension="2016-06-01" root="2.16.840.1.113883.**********.200"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******040.20"></id>
                <code code="76689-9" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Sex assigned at birth"></code>
                <text>
                  <reference value="#BirthSex18"></reference>
                </text>
                <statusCode code="completed"></statusCode>
                <effectiveTime value="19740727"></effectiveTime>
                <value codeSystem="2.16.840.1.113883.5.1" codeSystemName="HL7 Gender" nullFlavor="UNK" xsi:type="CD">
                  <originalText>
                    <reference value="#BirthSex18Value"></reference>
                  </originalText>
                </value>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="EVN">
                <templateId extension="2022-06-01" root="2.16.840.1.113883.10.20.34.3.45"></templateId>
                <templateId root="2.16.840.1.113883.**********.38"></templateId>
                <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.38"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******040.46"></id>
                <code code="76691-5" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Gender identity"></code>
                <text>
                  <reference value="#GenderIdentity16"></reference>
                </text>
                <statusCode code="completed"></statusCode>
                <effectiveTime>
                  <low nullFlavor="UNK"></low>
                </effectiveTime>
                <value nullFlavor="UNK" xsi:type="CD">
                  <originalText>
                    <reference value="#GenderIdentity16Value"></reference>
                  </originalText>
                </value>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="EVN">
                <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.501"></templateId>
                <templateId root="2.16.840.1.113883.**********.38"></templateId>
                <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.38"></templateId>
                <id extension="Z1006841.1" root="1.2.840.114350.1.13.382.*******040.45"></id>
                <code code="76690-7" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Sexual orientation"></code>
                <text>
                  <reference value="#SexualOrientation17"></reference>
                </text>
                <statusCode code="completed"></statusCode>
                <effectiveTime>
                  <low nullFlavor="UNK"></low>
                </effectiveTime>
                <value nullFlavor="UNK" xsi:type="CD">
                  <originalText>
                    <reference value="#SexualOrientation17Value"></reference>
                  </originalText>
                </value>
              </observation>
            </entry>
          </section>
        </component>
        <component>
          <section nullFlavor="NI">
            <templateId root="2.16.840.1.113883.**********.4"></templateId>
            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.4"></templateId>
            <templateId root="2.16.840.1.113883.**********.4.1"></templateId>
            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.4.1"></templateId>
            <code code="8716-3" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Vital signs"></code>
            <title>Last Filed Vital Signs</title>
            <text>
              <content ID="nof21">Not on file</content>
              <footnote ID="subTitle20" styleCode="xSectionSubTitle">documented in this encounter</footnote>
            </text>
          </section>
        </component>
        <component>
          <section>
            <templateId root="1.3.6.1.4.1.19376.1.5.3.1.3.4"></templateId>
            <id root="88E1D4D7-B424-11EF-AE93-005056BAFD93"></id>
            <code code="10164-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="History of Present Illness"></code>
            <title>Progress Notes</title>
            <text>
              <list styleCode="xTOC">
                <item>
                  <caption>Alex Galvin - 12/06/2024  4:44 PM EST</caption>
                  <content ID="Note25">
                    <content>
                      <content styleCode="xLabel">Formatting of this note might be different from the original.</content>
                      <br></br>This is a test</content>
                    <br></br>
                    <content styleCode="xLabel">Electronically signed by Alex Galvin at 12/06/2024  4:49 PM EST</content>
                    <br></br>
                  </content>
                </item>
              </list>
              <footnote ID="subTitle28" styleCode="xSectionSubTitle">documented in this encounter</footnote>
            </text>
            <entry>
              <act classCode="ACT" moodCode="EVN">
                <templateId extension="2016-11-01" root="2.16.840.1.113883.**********.202"></templateId>
                <id extension="269349881" root="1.2.840.114350.1.13.382.3.7.2.727879"></id>
                <id extension="1.2.840.114350.1.13.382.3.7.2.727879_269349881" root="1.2.840.114350.1.72.3.15"></id>
                <code code="34109-9" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Note">
                  <originalText>Progress Notes</originalText>
                  <translation code="11506-3" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Progress note"></translation>
                  <translation code="1" codeSystem="1.2.840.114350.1.72.727879.69848980" codeSystemName="Epic.EpicUCNNoteType" displayName="Progress Notes"></translation>
                  <translation code="1" codeSystem="1.2.840.114350.1.13.382.3.7.4.737880.5010" codeSystemName="Epic.RawUCNNoteType" displayName="Progress Notes"></translation>
                </code>
                <text>
                  <reference value="#Note25"></reference>
                </text>
                <statusCode code="completed"></statusCode>
                <effectiveTime value="20241206164400-0500"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164943-0500"></time>
                  <assignedAuthor>
                    <id extension="508447517" root="1.2.840.114350.1.13.382.*******133"></id>
                    <id extension="19746" root="1.2.840.114350.1.13.382.3.7.2.697780"></id>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                  </assignedAuthor>
                </author>
                <informant>
                  <assignedEntity>
                    <id extension="urn:BROOKDALE:TST-CE" root="1.2.840.114350.1.13.382.3.7.3.688879.110"></id>
                    <representedOrganization>
                      <name>One Brooklyn Health - TST</name>
                    </representedOrganization>
                  </assignedEntity>
                </informant>
                <participant typeCode="LA">
                  <sdtc:functionCode code="1" codeSystem="1.2.840.114350.********.7.10.696784.72072" codeSystemName="Epic.ECT.NoteSignerRole" displayName="Signer"></sdtc:functionCode>
                  <time value="20241206164943-0500"></time>
                  <participantRole>
                    <id extension="508447517" root="1.2.840.114350.1.13.382.*******133"></id>
                  </participantRole>
                </participant>
              </act>
            </entry>
          </section>
        </component>
        <component>
          <section>
            <templateId root="2.16.840.1.113883.**********.10"></templateId>
            <templateId extension="2014-06-09" root="2.16.840.1.113883.**********.10"></templateId>
            <code code="18776-5" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Plan of care note"></code>
            <title>Plan of Treatment</title>
            <text>
              <table>
                <colgroup>
                  <col width="25%"></col>
                  <col width="13%"></col>
                  <col width="12%"></col>
                  <col width="50%"></col>
                </colgroup>
                <thead>
                  <tr>
                    <th>Health Maintenance</th>
                    <th>Due Date</th>
                    <th>Last Done</th>
                    <th>Comments</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ID="hm40">
                    <td ID="hm40name">CT Colonography</td>
                    <td styleCode="Bold">07/27/1974</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm55">
                    <td ID="hm55name">Colonoscopy</td>
                    <td styleCode="Bold">07/27/1974</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm65">
                    <td ID="hm65name">Colorectal Cancer Screening</td>
                    <td styleCode="Bold">07/27/1974</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm58">
                    <td ID="hm58name">FIT-DNA</td>
                    <td styleCode="Bold">07/27/1974</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm66">
                    <td ID="hm66name">FIT</td>
                    <td styleCode="Bold">07/27/1974</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm57">
                    <td ID="hm57name">FOBT</td>
                    <td styleCode="Bold">07/27/1974</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm44">
                    <td ID="hm44name">Lipid Panel</td>
                    <td styleCode="Bold">07/27/1974</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm41">
                    <td ID="hm41name">Mammogram</td>
                    <td styleCode="Bold">07/27/1974</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm56">
                    <td ID="hm56name">Sigmoidoscopy</td>
                    <td styleCode="Bold">07/27/1974</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm54">
                    <td ID="hm54name">MMR VACCINES (1 of 1 - Standard series)</td>
                    <td styleCode="Bold">07/27/1975</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm49">
                    <td ID="hm49name">DTaP/Tdap/Td Vaccines (1 - Tdap)</td>
                    <td styleCode="Bold">07/27/1981</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm60">
                    <td ID="hm60name">Depression Screening</td>
                    <td styleCode="Bold">07/27/1986</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm43">
                    <td ID="hm43name">HIV Screening</td>
                    <td styleCode="Bold">07/27/1989</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm51">
                    <td ID="hm51name">HEPATITIS B VACCINES (1 of 3 - 19+ 3-dose series)</td>
                    <td styleCode="Bold">07/27/1993</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm38">
                    <td ID="hm38name">Annual Wellness Visit (non-Medicare)</td>
                    <td styleCode="Bold">07/27/1995</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm63">
                    <td ID="hm63name">Cervical Cancer Screening</td>
                    <td styleCode="Bold">07/27/1995</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm64">
                    <td ID="hm64name">HPV/Cotest</td>
                    <td styleCode="Bold">07/27/1995</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm42">
                    <td ID="hm42name">Pap Smear</td>
                    <td styleCode="Bold">07/27/1995</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm47">
                    <td ID="hm47name">Zoster Vaccines (1 of 2)</td>
                    <td styleCode="Bold">07/27/2024</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm46">
                    <td ID="hm46name">COVID-19 Vaccine (1 - 2024-25 season)</td>
                    <td styleCode="Bold">09/01/2024</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm45">
                    <td ID="hm45name">Influenza Vaccine (#1)</td>
                    <td styleCode="Bold">09/01/2024</td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr ID="hm39">
                    <td ID="hm39name">Annual Social Determinants of Health Screening</td>
                    <td>12/03/2025</td>
                    <td>12/03/2024</td>
                    <td></td>
                  </tr>
                  <tr ID="hm53" styleCode="xHistoric">
                    <td ID="hm53name">HEPATITIS A VACCINES</td>
                    <td>Aged Out</td>
                    <td></td>
                    <td ID="hm53comment">No longer eligible based on patient&apos;s age to complete this topic</td>
                  </tr>
                  <tr ID="hm50" styleCode="xHistoric">
                    <td ID="hm50name">HIB VACCINES</td>
                    <td>Aged Out</td>
                    <td></td>
                    <td ID="hm50comment">No longer eligible based on patient&apos;s age to complete this topic</td>
                  </tr>
                  <tr ID="hm61" styleCode="xHistoric">
                    <td ID="hm61name">HPV Vaccines</td>
                    <td>Aged Out</td>
                    <td></td>
                    <td ID="hm61comment">No longer eligible based on patient&apos;s age to complete this topic</td>
                  </tr>
                  <tr ID="hm52" styleCode="xHistoric">
                    <td ID="hm52name">IPV VACCINES</td>
                    <td>Aged Out</td>
                    <td></td>
                    <td ID="hm52comment">No longer eligible based on patient&apos;s age to complete this topic</td>
                  </tr>
                  <tr ID="hm48" styleCode="xHistoric">
                    <td ID="hm48name">Meningococcal Vaccine</td>
                    <td>Aged Out</td>
                    <td></td>
                    <td ID="hm48comment">No longer eligible based on patient&apos;s age to complete this topic</td>
                  </tr>
                  <tr ID="hm62" styleCode="xHistoric">
                    <td ID="hm62name">Pneumococcal Vaccine: Pediatrics (0 to 5 Years) and At-Risk Patients (6 to 64 Years)</td>
                    <td>Aged Out</td>
                    <td></td>
                    <td ID="hm62comment">No longer eligible based on patient&apos;s age to complete this topic</td>
                  </tr>
                  <tr ID="hm59" styleCode="xHistoric">
                    <td ID="hm59name">ROTAVIRUS VACCINES</td>
                    <td>Aged Out</td>
                    <td></td>
                    <td ID="hm59comment">No longer eligible based on patient&apos;s age to complete this topic</td>
                  </tr>
                </tbody>
              </table>
              <footnote ID="subTitle36" styleCode="xSectionSubTitle">documented as of this encounter</footnote>
            </text>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.48"></id>
                <code code="275978004" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMEDCT" displayName="Screening for malignant neoplasm of colon (procedure)">
                  <originalText>
                    <reference value="#hm40name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="19740727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.**********"></id>
                <code code="275978004" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMEDCT" displayName="Screening for malignant neoplasm of colon (procedure)">
                  <originalText>
                    <reference value="#hm55name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="19740727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.**********"></id>
                <code code="275978004" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMEDCT" displayName="Screening for malignant neoplasm of colon (procedure)">
                  <originalText>
                    <reference value="#hm65name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="19740727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.**********"></id>
                <code code="275978004" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMEDCT" displayName="Screening for malignant neoplasm of colon (procedure)">
                  <originalText>
                    <reference value="#hm58name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="19740727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.**********"></id>
                <code code="275978004" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMEDCT" displayName="Screening for malignant neoplasm of colon (procedure)">
                  <originalText>
                    <reference value="#hm66name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="19740727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.**********"></id>
                <code code="275978004" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMEDCT" displayName="Screening for malignant neoplasm of colon (procedure)">
                  <originalText>
                    <reference value="#hm57name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="19740727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.64"></id>
                <code code="16254007" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMEDCT" displayName="Lipid panel (procedure)">
                  <originalText>
                    <reference value="#hm44name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="19740727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.49"></id>
                <code code="268547008" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMEDCT" displayName="Screening for malignant neoplasm of breast (procedure)">
                  <originalText>
                    <reference value="#hm41name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="19740727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.**********"></id>
                <code code="275978004" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMEDCT" displayName="Screening for malignant neoplasm of colon (procedure)">
                  <originalText>
                    <reference value="#hm56name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="19740727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.**********"></id>
                <code nullFlavor="UNK">
                  <originalText>
                    <reference value="#hm54name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="19750727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.**********"></id>
                <code nullFlavor="UNK">
                  <originalText>
                    <reference value="#hm49name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="19810727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.**********"></id>
                <code nullFlavor="UNK">
                  <originalText>
                    <reference value="#hm60name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="19860727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.52"></id>
                <code code="171121004" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMEDCT" displayName="Human immunodeficiency virus screening (procedure)">
                  <originalText>
                    <reference value="#hm43name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="19890727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.**********"></id>
                <code nullFlavor="UNK">
                  <originalText>
                    <reference value="#hm51name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="19930727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.9"></id>
                <code nullFlavor="UNK">
                  <originalText>
                    <reference value="#hm38name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="19950727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.**********"></id>
                <code code="171149006" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMEDCT" displayName="Screening for malignant neoplasm of cervix (procedure)">
                  <originalText>
                    <reference value="#hm63name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="19950727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.**********"></id>
                <code nullFlavor="UNK">
                  <originalText>
                    <reference value="#hm64name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="19950727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.50"></id>
                <code code="171149006" codeSystem="2.16.840.1.113883.6.96" codeSystemName="SNOMEDCT" displayName="Screening for malignant neoplasm of cervix (procedure)">
                  <originalText>
                    <reference value="#hm42name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="19950727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.58580"></id>
                <code nullFlavor="UNK">
                  <originalText>
                    <reference value="#hm47name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="20240727"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.79"></id>
                <code nullFlavor="UNK">
                  <originalText>
                    <reference value="#hm46name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="20240901"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.67"></id>
                <code nullFlavor="UNK">
                  <originalText>
                    <reference value="#hm45name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="20240901"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
              </observation>
            </entry>
            <entry>
              <observation classCode="OBS" moodCode="PRP">
                <templateId root="2.16.840.1.113883.**********.44"></templateId>
                <templateId root="1.2.840.114350.1.72.2.1989.2"></templateId>
                <id extension="Z1006841" root="1.2.840.114350.1.13.382.*******27784.17"></id>
                <code nullFlavor="UNK">
                  <originalText>
                    <reference value="#hm39name"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime value="20251203"></effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20241206164520-0500"></time>
                  <assignedAuthor>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
                <entryRelationship typeCode="COMP">
                  <observation classCode="OBS" moodCode="EVN">
                    <code code="67723-7" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Date of health-related event"></code>
                    <value value="20241203" xsi:type="TS"></value>
                  </observation>
                </entryRelationship>
              </observation>
            </entry>
          </section>
        </component>
        <component>
          <section nullFlavor="NI">
            <templateId root="2.16.840.1.113883.**********.3"></templateId>
            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.3"></templateId>
            <templateId root="2.16.840.1.113883.**********.3.1"></templateId>
            <templateId extension="2015-08-01" root="2.16.840.1.113883.**********.3.1"></templateId>
            <id root="88E6F515-B424-11EF-AE93-005056BAFD93"></id>
            <code code="30954-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Relevant diagnostic tests/laboratory data Narrative"></code>
            <title>Results</title>
            <text>
              <content ID="nof72">Not on file</content>
              <footnote ID="subTitle71" styleCode="xSectionSubTitle">documented in this encounter</footnote>
            </text>
          </section>
        </component>
        <component>
          <section>
            <templateId root="2.16.840.1.113883.**********.8"></templateId>
            <id root="88E70B06-B424-11EF-AE93-005056BAFD93"></id>
            <code code="51848-0" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Assessments"></code>
            <title>Visit Diagnoses</title>
            <text>
              <table>
                <colgroup>
                  <col width="100%"></col>
                </colgroup>
                <thead>
                  <tr>
                    <th>Diagnosis</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ID="vdx2" styleCode="xRowNormal">
                    <td>
                      <paragraph>
                        <content ID="vdx2Name">Biomechanical lesion of rib cage</content>
                        <content> - Primary</content>
                      </paragraph>
                    </td>
                  </tr>
                </tbody>
              </table>
              <footnote ID="subTitle73" styleCode="xSectionSubTitle">documented in this encounter</footnote>
            </text>
          </section>
        </component>
        <component>
          <section>
            <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.500"></templateId>
            <templateId extension="2019-07-01" root="2.16.840.1.113883.**********.500"></templateId>
            <code code="85847-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Patient Care team information"></code>
            <title>Care Teams</title>
            <text>
              <table ID="ct77">
                <colgroup>
                  <col span="3" width="25%"></col>
                  <col width="13%"></col>
                  <col width="12%"></col>
                </colgroup>
                <thead>
                  <tr>
                    <th>Team Member</th>
                    <th>Relationship</th>
                    <th>Specialty</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                  </tr>
                </thead>
                <tbody>
                  <tr styleCode="xRowNormal">
                    <td>
                      <paragraph styleCode="Bold">File, No Primary Care Provider On, MD</paragraph>
                      <paragraph>1 Brookdale Plaza</paragraph>
                      <paragraph>Brooklyn, NY 11201</paragraph>
                    </td>
                    <td>PCP - General</td>
                    <td>Family Medicine</td>
                    <td>5/7/24</td>
                    <td></td>
                  </tr>
                </tbody>
              </table>
              <footnote ID="subTitle76" styleCode="xSectionSubTitle">documented as of this encounter</footnote>
            </text>
            <entry>
              <organizer classCode="CLUSTER" moodCode="EVN">
                <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.500"></templateId>
                <templateId extension="2019-07-01" root="2.16.840.1.113883.**********.500"></templateId>
                <id nullFlavor="NI"></id>
                <code code="86744-0" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Care Team">
                  <originalText>
                    <reference value="#ct77"></reference>
                  </originalText>
                </code>
                <statusCode code="active"></statusCode>
                <effectiveTime>
                  <low value="20240507"></low>
                </effectiveTime>
                <author>
                  <templateId root="2.16.840.1.113883.**********.119"></templateId>
                  <templateId extension="2019-10-01" root="2.16.840.1.113883.**********.6"></templateId>
                  <time value="20240507105753-0400"></time>
                  <assignedAuthor>
                    <id extension="404583958" root="1.2.840.114350.1.13.382.*******133"></id>
                    <id extension="110346" root="1.2.840.114350.1.13.382.3.7.2.697780"></id>
                    <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                    <addr nullFlavor="UNK"></addr>
                    <telecom nullFlavor="UNK"></telecom>
                    <assignedPerson>
                      <name use="L">
                        <given>Nicola</given>
                        <family>Dawkins</family>
                        <validTime>
                          <low nullFlavor="UNK"></low>
                          <high nullFlavor="UNK"></high>
                        </validTime>
                      </name>
                    </assignedPerson>
                    <representedOrganization>
                      <id extension="12800" root="1.2.840.114350.1.13.382.3.7.2.688879"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.2"></id>
                      <id nullFlavor="UNK" root="2.16.840.1.113883.4.6"></id>
                      <name>One Brooklyn Health - TST</name>
                      <addr use="WP">
                        <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                        <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                        <city>Brooklyn</city>
                        <state>NY</state>
                        <postalCode>11212</postalCode>
                        <country>USA</country>
                      </addr>
                    </representedOrganization>
                  </assignedAuthor>
                </author>
                <component>
                  <act classCode="PCPR" moodCode="EVN">
                    <templateId extension="2022-06-01" root="2.16.840.1.113883.**********.500.1"></templateId>
                    <templateId extension="2019-07-01" root="2.16.840.1.113883.**********.500.1"></templateId>
                    <id nullFlavor="UNK"></id>
                    <code code="85847-2" codeSystem="2.16.840.1.113883.6.1" codeSystemName="LOINC" displayName="Care Team Information"></code>
                    <statusCode code="active"></statusCode>
                    <effectiveTime>
                      <low value="20240507"></low>
                    </effectiveTime>
                    <performer typeCode="PRF">
                      <sdtc:functionCode code="PCP" codeSystem="2.16.840.1.113883.5.88" codeSystemName="ParticipationFunction" displayName="Primary Care Provider">
                        <originalText>General</originalText>
                      </sdtc:functionCode>
                      <assignedEntity>
                        <id nullFlavor="UNK"></id>
                        <code code="207Q00000X" codeSystem="2.16.840.1.113883.6.101" displayName="FAMILY MEDICINE PHYSICIAN">
                          <originalText>Family Medicine</originalText>
                          <translation code="19" codeSystem="1.2.840.114350.********.7.10.688867.4160" codeSystemName="Epic.DXC.StandardProviderSpecialtyType" displayName="Family Medicine"></translation>
                          <translation code="9" codeSystem="1.2.840.114350.1.13.382.3.7.10.836982.1050" codeSystemName="Epic.SER.ProviderSpecialty" displayName="Family Medicine"></translation>
                        </code>
                        <addr use="WP">
                          <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                          <city>Brooklyn</city>
                          <state>NY</state>
                          <postalCode>11201</postalCode>
                        </addr>
                        <telecom nullFlavor="NA"></telecom>
                        <assignedPerson>
                          <name use="L">
                            <given>No</given>
                            <given>Primary Care Provider On</given>
                            <family>File</family>
                            <suffix qualifier="AC"> MD</suffix>
                            <validTime>
                              <low nullFlavor="UNK"></low>
                              <high nullFlavor="UNK"></high>
                            </validTime>
                          </name>
                        </assignedPerson>
                        <representedOrganization>
                          <name>One Brooklyn Health - TST</name>
                          <telecom nullFlavor="UNK"></telecom>
                          <addr use="WP">
                            <streetAddressLine>1 Brookdale Plaza</streetAddressLine>
                            <streetAddressLine>Brooklyn, NY 11201</streetAddressLine>
                            <city>Brooklyn</city>
                            <state>NY</state>
                            <postalCode>11212</postalCode>
                            <country>USA</country>
                          </addr>
                        </representedOrganization>
                      </assignedEntity>
                    </performer>
                  </act>
                </component>
              </organizer>
            </entry>
          </section>
        </component>
      </structuredBody>
    </component>
  </ClinicalDocument>

