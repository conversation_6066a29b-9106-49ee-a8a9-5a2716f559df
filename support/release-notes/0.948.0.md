## Key Features
 
- Updated Test SHIN-NY IG version to **v1.7.2**  
  [#2477](https://github.com/tech-by-design/polyglot-prime/issues/2477)
 
- Defined unique Resource ID logic for **Observation** resources
 
## Enhancements & Fixes
 
- Updated XSLT files to correct issues with **Organization** and **Patient** resources for HL7 files  
  [#2281](https://github.com/tech-by-design/polyglot-prime/issues/2281)
 
- Added test case documentation  
  [#2327](https://github.com/tech-by-design/polyglot-prime/issues/2327)
 
- Updated XSLT files to handle **null values** for Patient, Organization, and Encounter resources in HL7 files  
  [#2281](https://github.com/tech-by-design/polyglot-prime/issues/2281)
 
- Updated XSLT files and channel configuration to handle **null values** for HL7 files  
  [#2281](https://github.com/tech-by-design/polyglot-prime/issues/2281)
 
- Updated XSLT files to create **unique Resource IDs** using facility ID and extension values for CCDA files  
  [#2470](https://github.com/tech-by-design/polyglot-prime/issues/2470)
 
- Updated XSLT to modify `derivedFrom` references using the new logic for creating **Observation Resource IDs**  
  [#2470](https://github.com/tech-by-design/polyglot-prime/issues/2470)
 
- Updated SHIN-NY IG QA configuration in **nexus-core-lib** to **v1.7.2**  
  [#2477](https://github.com/tech-by-design/polyglot-prime/issues/2477)
 
- Updated **CCDA to FHIR conversion specification** documentation  
  [#2470](https://github.com/tech-by-design/polyglot-prime/issues/2470)
 
- Updated XSLT files to change the location of **Encounter Status** for Mohawk and Guthrie CCDA files  
  [#2338](https://github.com/tech-by-design/polyglot-prime/issues/2338)
 
- Updated **Consent** and **Encounter Status** logic for **AthenaHealth**  
  [#1949](https://github.com/tech-by-design/polyglot-prime/issues/1949)