## Key Features

* Updated **Test SHIN-NY IG** version to **v1.7.3** in **QA**
  [#2546](https://github.com/tech-by-design/polyglot-prime/issues/2546)

* Updated **SHIN-NY IG** version to **v1.7.3** for **Production**
  [#2545](https://github.com/tech-by-design/polyglot-prime/issues/2545)


## Enhancements & Fixes

* Updated `error_message` column in the `sat_interaction_fhir_request` table with error messages from **techByDesignDisposition** discard messages
  [#2500](https://github.com/tech-by-design/polyglot-prime/issues/2500)

* Updated `get_missing_datalake_submission_details` function to retrieve submission `error_message` details
  [#2500](https://github.com/tech-by-design/polyglot-prime/issues/2500)

* Enhanced **FHIR Needs Attention** drilldown functionality and improved error message handling in the detail grid
  [#2500](https://github.com/tech-by-design/polyglot-prime/issues/2500)

* Added negative test cases for bundles
  [#2281](https://github.com/tech-by-design/polyglot-prime/issues/2281)

* Enhanced value calculation for valid bundles on the **Needs Attention** page
  [#2500](https://github.com/tech-by-design/polyglot-prime/issues/2500)

* Updated the **nature** filter in the Interaction HTTP Request view to `techByDesignDisposition`
  [#2500](https://github.com/tech-by-design/polyglot-prime/issues/2500)
