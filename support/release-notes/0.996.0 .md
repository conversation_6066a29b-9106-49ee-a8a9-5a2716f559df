## Key Features

- Added **`/bundles/status/operation-outcome` API endpoint** to retrieve the OperationOutcome of a bundle sent to NYeC  
  [#2629](https://github.com/tech-by-design/polyglot-prime/issues/2629)

- Updated **XSLT files** to change the resource ID generation logic for **Observation** resources in **Epic, Medent, and AthenaHealth CCDA files**  
  [#2470](https://github.com/tech-by-design/polyglot-prime/issues/2470)

- Added **mandatory CCDA field validation** in the **Mirth Connect channel** for FHIR bundle conversion  
  [#2523](https://github.com/tech-by-design/polyglot-prime/issues/2523)


## Enhancements & Fixes

- Corrected error message for **invalid or missing multipart Content-Type**  
  [#2380](https://github.com/tech-by-design/polyglot-prime/issues/2380)

- Updated `get_fhir_needs_attention` function to correctly calculate  
  **`techbd_processed`** and **`valid_bundles`** counts  
  [#2565](https://github.com/tech-by-design/polyglot-prime/issues/2565)

- Updated **TechBD CCD workflow** to include **response status code**  
  [#1709](https://github.com/tech-by-design/polyglot-prime/issues/1709)

- Improved error message when an invalid **`X-TechBD-Base-FHIR-URL`** is provided  
  [#2380](https://github.com/tech-by-design/polyglot-prime/issues/2380)

- Updated **XSLT files** to include both **NPI ID** and **TIN** in the `identifier` array of the **Organization** resource  
  [#2338](https://github.com/tech-by-design/polyglot-prime/issues/2338)

- Added request body validation for **`FhirBundleSubmission`**  
  [#2380](https://github.com/tech-by-design/polyglot-prime/issues/2380)

- Added **OperationOutcome API path** to the **FHIR API group** in Swagger configuration  
  [#2629](https://github.com/tech-by-design/polyglot-prime/issues/2629)

- Added **“View All”** button to **Needs Attention** drill-down views  
  [#2565](https://github.com/tech-by-design/polyglot-prime/issues/2565)

- Updated **CCDA-to-FHIR conversion specification documents** to reflect:
  - Observation resource ID changes
  - Organization identifier updates  
  [#2470](https://github.com/tech-by-design/polyglot-prime/issues/2470)
