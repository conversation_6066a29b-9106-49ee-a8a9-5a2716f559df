**Note:** This release is currently available in the **Stage environment**  
https://synthetic.hub.stage.techbd.org/


## Enhancements & Fixes

- Removed encounter class validation.  
  [#2523](https://github.com/tech-by-design/polyglot-prime/issues/2523)

- Send NACK response after storing oversized HL7 messages.  
  [#2646](https://github.com/tech-by-design/polyglot-prime/issues/2646)

- Updated HL7 XSLT to correctly resolve OrgTIN and OrgNPI headers and include both in the response.  
  [#2380](https://github.com/tech-by-design/polyglot-prime/issues/2380)

- Validate mandatory request header variables before saving HL7 data or validation results.  
  [#2658](https://github.com/tech-by-design/polyglot-prime/issues/2658)

- Resolved issue with unexpected comma in array literal during HL7 processing.  
  [#2628](https://github.com/tech-by-design/polyglot-prime/issues/2628)

- Fixed race and ethnicity text mapping issues while converting HL7 to FHIR bundles.  
  [#2628](https://github.com/tech-by-design/polyglot-prime/issues/2628)

- Fixed CSV drill-down issue by correcting masterInteractionId and groupInteractionId handling.  
  [#2380](https://github.com/tech-by-design/polyglot-prime/issues/2380)

- Updated Frictionless JSON schema to support new 1115 CSV changes and enhanced lookup tables to maintain correct code, description, and system values from the reference tab.  
  [#2571](https://github.com/tech-by-design/polyglot-prime/issues/2571)

- Improved handling of CSV null character validation errors with clearer descriptions in Nexus.  
  [#2380](https://github.com/tech-by-design/polyglot-prime/issues/2380)

- Added case-insensitive validation for new fields and extended telecom and race/ethnicity validation mappings.  
  [#2571](https://github.com/tech-by-design/polyglot-prime/issues/2571)

- Synced CCDA artifacts with the latest Synthetic updates.  
  [#2380](https://github.com/tech-by-design/polyglot-prime/issues/2380)

- Added validation to ensure SDOH observation category matches DB lookup values.  
  [#2571](https://github.com/tech-by-design/polyglot-prime/issues/2571)

- Updated XSLT to avoid generating observations without answers while creating grouper Observation resources for HL7 files.  
  [#2628](https://github.com/tech-by-design/polyglot-prime/issues/2628)

- Moved refactored logic into integration artifacts to resolve JSON formatting issues related to unexpected commas in HL7 files.  
  [#2628](https://github.com/tech-by-design/polyglot-prime/issues/2628)

- Updated `documentation.md` and `1115_SDOH.Template_v6_02042026.xlsx`.  
  [#2571](https://github.com/tech-by-design/polyglot-prime/issues/2571)



