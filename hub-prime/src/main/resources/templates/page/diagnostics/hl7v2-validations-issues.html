<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <style>
        .grid-description {
            font-size: 14px;
            margin: 5px 0px 8px 15px;
        }

        .grid-title {
            font-size: 18px;
            font-weight: bold;
            margin: 12px 0px 11px 15px;
        }
    </style> 
    <!-- Include Font Awesome in your HTML -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- if JSON Viewer is not already in the layout, add the following -->
    <!-- <script src="https://unpkg.com/@alenaksu/json-viewer@2.0.0/dist/json-viewer.bundle.js"></script> -->

    <th:block th:insert="./fragments/common-head-aggrid :: common-head-aggrid"></th:block>
    <script type="module">
        import { AGGridAide, AGGridAideBuilder } from '@presentation/shell/aggrid-aide.js';
        import ModalAide from '@presentation/shell/modal-aide.js';

        const schemaName = 'techbd_udi_ingress';
        const viewName = 'interaction_hl7v2_validation_errors';
 
        document.addEventListener('DOMContentLoaded', function () {
 
            const modalAide = new ModalAide();

            // Function to get URL parameters
            function getUrlParameter(name) {
                const urlParams = new URLSearchParams(window.location.search);
                const value = urlParams.get(name);
                return value ? decodeURIComponent(value) : null;
            }

            // Get hub_interaction_id from URL if present
            const hubInteractionIdFromUrl = getUrlParameter('hub_interaction_id');

            const agGridInstance = new AGGridAideBuilder()
                 .withColumnDefs([
                            {
                                headerName: "Request Time",
                                field: "created_at",
                                sort: "desc",
                                filter: "agDateColumnFilter",
                                headerTooltip: "Timestamp when the validation error was recorded"
                            },
                            {
                                headerName: "TechBD Interaction ID",
                                field: "hub_interaction_id",
                                filter: "agTextColumnFilter",
                                headerTooltip: "Unique interaction identifier associated with this validation error"
                            },
                            {
                                headerName: "TechBD Tenant ID",
                                field: "tenant_id",
                                filter: "agTextColumnFilter",
                                headerTooltip: "ID of the tenant involved in this HL7v2 interaction"
                            },
                            {
                                headerName: "URI",
                                field: "uri",
                                filter: "agTextColumnFilter",
                                headerTooltip: "The URI linked to the HL7v2 validation request or response"
                            },
                            {
                                headerName: "Error Details",
                                field: "error",
                                filter: "agTextColumnFilter",
                                headerTooltip: "Detailed description of the validation error encountered"
                            },
                            {
                                headerName: "File Name",
                                field: "file_name",
                                filter: "agTextColumnFilter",
                                headerTooltip: "Name of the file where the validation error occurred"
                            }
                        ])
                .withServerSideDatasource(
                    window.shell.serverSideUrl(`/api/ux/tabular/jooq/${schemaName}/${viewName}.json`),
                    (data, valueCols) => {
                        return valueCols.map(col => ({
                            headerName: col.displayName,
                            field: col.field
                        }));
                    },
                    {
                        beforeRequest: async (reqPayload, dataSourceUrl) => {
                            // Add hub_interaction_id filter from URL if present and not already in filterModel
                            if (hubInteractionIdFromUrl && (!reqPayload.body.filterModel || !reqPayload.body.filterModel.hub_interaction_id)) {
                                reqPayload.body.filterModel = {
                                    ...reqPayload.body.filterModel,
                                    hub_interaction_id: {
                                        filterType: "text",
                                        type: "equals",
                                        filter: hubInteractionIdFromUrl
                                    }
                                };
                            }
                        }
                    }                    
                )
                .withModalAide(modalAide)
                .withGridDivStyles({ height: "750px", width: "100%" })
                .build();

            agGridInstance.init('serverDataGrid');

            // Show visual indicator if hub_interaction_id filter is applied from URL
            if (hubInteractionIdFromUrl) {
                // Update grid description to show active filter
                const gridDescription = document.querySelector('.grid-description');
                if (gridDescription) {
                    const filterNotice = document.createElement('div');
                    filterNotice.style.cssText = 'background-color: #e3f2fd; border: 1px solid #2196f3; padding: 8px; margin: 10px 0; border-radius: 4px; color: #1976d2; font-weight: bold; display: flex; justify-content: space-between; align-items: center;';

                    const filterText = document.createElement('span');
                    filterText.innerHTML = `TechBD Interaction ID: ${hubInteractionIdFromUrl}`;

                    const clearButton = document.createElement('button');
                    clearButton.innerHTML = 'Show All';
                    clearButton.style.cssText = 'background-color: #1976d2; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px; margin-left: 10px;';
                    clearButton.title = 'Clear TechBD Interaction ID filter and return to original view';

                    // Add hover effect
                    clearButton.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = '#1565c0';
                    });
                    clearButton.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = '#1976d2';
                    });

                    // Add click handler to Show All and refresh page
                    clearButton.addEventListener('click', function() {
                        // Remove hub_interaction_id parameter from URL and reload page
                        const url = new URL(window.location);
                        url.searchParams.delete('hub_interaction_id');
                        window.location.href = url.toString();
                    });

                    filterNotice.appendChild(filterText);
                    filterNotice.appendChild(clearButton);
                    gridDescription.parentNode.insertBefore(filterNotice, gridDescription.nextSibling);
                }
            }             
        });
    </script>
</head>

<body>
    <div layout:fragment="content">
        <div class="grid-description">
          This widget provides an overview of HL7v2 validation errors encountered during the data processing pipeline. It displays columns such as Request Time, Interaction ID, Tenant ID, URI, Error Details and File Name.
           Users can filter or sort the grid to analyze errors by tenant, file, or timeframe. Clicking on the Interaction ID can be extended to open a detailed view showing the full validation context and associated payload for troubleshooting purposes.
        </div>
        <div id="serverDataGrid" class="ag-theme-alpine"></div>
    </div>
</body>

</html>