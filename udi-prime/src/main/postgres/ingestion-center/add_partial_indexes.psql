\echo '===================================================='
\echo 'START: Creating partial index idx_sat_diag_fn_fast'
\echo '===================================================='

-- Prevent concurrent execution
SELECT pg_advisory_lock(hashtext('partial_index'));

-- Drop the index
DROP INDEX CONCURRENTLY IF EXISTS techbd_udi_ingress.idx_sat_diag_fn_fast;

-- Create index
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sat_diag_fn_fast
ON techbd_udi_ingress.sat_interaction_fhir_session_diagnostic
(
  hub_interaction_id,
  created_at DESC
)
INCLUDE (
  tenant_id,
  bundle_id,
  uri,
  ig_version,
  techbd_version_number,
  validation_engine
)
WHERE (
    severity IN ('warning','error','fatal')
    AND message IS DISTINCT FROM ''
);

-- Release lock
SELECT pg_advisory_unlock(hashtext('partial_index'));

\echo '===================================================='
\echo 'END: Partial index idx_sat_diag_fn_fast completed'
\echo '===================================================='