 
/****************************************************************************************
 * Comprehensive view of HTTP interactions and their associated requests.
 * 
 * This view joins the hub_interaction table with the sat_interaction_http_request table
 * to provide a detailed record of each interaction and its related HTTP requests.
 * 
 * Purpose:
 * - To offer a business-level overview of HTTP interactions processed by the system.
 * - Each request is typically represented by a single row. However, in scenarios where 
 *   completing a request involves multiple asynchronous HTTP requests, multiple rows 
 *   might be associated with a single interaction.
 * - This is particularly useful for understanding complex interactions that involve 
 *   several steps or dependent requests to fulfill a single client request.
 ****************************************************************************************/
DROP VIEW IF EXISTS techbd_udi_ingress.interaction_http_request CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_http_request
AS 
SELECT interaction_id,
    uri,
    interaction_created_at,
    interaction_created_by,
    interaction_provenance,
    sat_interaction_http_request_id,
    nature,
    tenant_id,
    user_agent,
    client_ip_address,
    content_type,
    elaboration,
    from_state,
    to_state,
    state_transition_reason,
    request_created_at,
    request_created_by,
    request_provenance,
    issues_count,
    resource_types,
    start_time,
    finish_time,
    duration_millisecs
   FROM ( SELECT htt_req.hub_interaction_id AS interaction_id,
            htt_req.uri,
            htt_req.created_at AS interaction_created_at,
            htt_req.created_by AS interaction_created_by,
            htt_req.provenance AS interaction_provenance,
            htt_req.sat_interaction_fhir_request_id AS sat_interaction_http_request_id,
            htt_req.nature,
            htt_req.tenant_id,
            htt_req.user_agent,
            htt_req.client_ip_address,
            'application/json'::text AS content_type,
            htt_req.elaboration,
            htt_req.from_state,
            htt_req.to_state,
            htt_req.state_transition_reason,
            htt_req.created_at AS request_created_at,
            htt_req.created_by AS request_created_by,
            htt_req.provenance AS request_provenance,
            htt_req.issues_count,
            htt_req.resource_type_set AS resource_types,
            htt_req.interaction_start_time AS start_time,
            htt_req.interaction_end_time AS finish_time,
            EXTRACT(epoch FROM htt_req.interaction_end_time::timestamp without time zone - htt_req.interaction_start_time::timestamp without time zone) * 1000::numeric AS duration_millisecs
           FROM techbd_udi_ingress.sat_interaction_fhir_request htt_req
        UNION ALL
         SELECT user_req.hub_interaction_id AS interaction_id,
            user_req.uri,
            user_req.created_at AS interaction_created_at,
            user_req.created_by AS interaction_created_by,
            user_req.provenance AS interaction_provenance,
            user_req.sat_interaction_user_id AS sat_interaction_http_request_id,
            user_req.nature,
            user_req.tenant_id,
            user_req.user_agent,
            user_req.client_ip_address,
            'application/json'::text AS content_type,
            user_req.elaboration,
            NULL::unknown AS from_state,
            NULL::unknown AS to_state,
            NULL::unknown AS state_transition_reason,
            user_req.created_at AS request_created_at,
            user_req.created_by AS request_created_by,
            user_req.provenance AS request_provenance,
            NULL::unknown AS issue_count,
            NULL::unknown AS resource_types,
            user_req.interaction_start_time AS start_time,
            user_req.interaction_end_time AS finish_time,
            EXTRACT(epoch FROM user_req.interaction_end_time::timestamp without time zone - user_req.interaction_start_time::timestamp without time zone) * 1000::numeric AS duration_millisecs
           FROM techbd_udi_ingress.sat_interaction_user user_req where user_req.uri not ilike '%/Bundle%') combined_query
  ORDER BY interaction_created_at DESC;

/*******************************************************************************************
This view combines data from hub_interaction and sat_interaction_http_request tables to 
provide a consolidated view of HTTP FHIR requests, including resource types, 
interaction details, request attributes, and validation issues.
******************************************************************************************/
DROP VIEW IF EXISTS techbd_udi_ingress.interaction_http_fhir_request CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_http_fhir_request
AS SELECT hub_interaction_id AS interaction_id,
    uri,
    bundle_id,
    created_at AS interaction_created_at,
    created_by AS interaction_created_by,
    provenance AS interaction_provenance,
    sat_interaction_fhir_request_id AS sat_interaction_http_request_id,
    nature,
    tenant_id,
    user_agent,
    client_ip_address,
    'application/json'::text AS content_type,
    payload,
    elaboration,
    from_state,
    to_state,
    state_transition_reason,
    created_at AS request_created_at,
    created_by AS request_created_by,
    provenance AS request_provenance,
    issues_count,
    resource_type_set AS resource_types,
    interaction_start_time::text AS start_time,
    interaction_end_time::text AS finish_time,
    EXTRACT(epoch FROM interaction_end_time::timestamp without time zone - interaction_start_time::timestamp without time zone) * 1000::numeric AS duration_millisecs,
    patient_id,
    patient_mrn,
    patient_mrn_source_system,
    organization_id,
    organization_name,
    concat_ws('-'::text, tenant_id, organization_id, patient_mrn) AS source_mrn,
    htt_req.bundle_type AS bundle_resource_type,
    CASE 
    	WHEN (source_type = 'CSV') then 'CSV'
        WHEN (source_type = 'CCDA') then 'CCDA'
        WHEN (source_type = 'HL7V2') then 'HL7V2'
    	WHEN (source_type = 'fhir') then 'FHIR'
    	WHEN (source_type = 'FHIR') then 'FHIR'
    	ELSE 'FHIR'
    END as source_type,   
    source_hub_interaction_id,
    	CASE
            WHEN is_bundle_valid = true THEN 'Valid'::text
            WHEN is_bundle_valid = false THEN 'Invalid'::text
        END AS is_bundle_valid  
   FROM techbd_udi_ingress.sat_interaction_fhir_request htt_req
   WHERE uri != '/api/expect/fhir/bundle'; 
   
/*******************************************************************************************
-- View to provide detailed information about FHIR HTTP requests linked to hub interactions.  
-- Retrieves request metadata, workflow state transitions, patient identifiers, and issue counts.  
-- Excludes system-generated expectation requests (uri = '/api/expect/fhir/bundle').  
-- Useful for auditing, monitoring, and analytics of interaction-level FHIR requests.  
*******************************************************************************************/

DROP VIEW IF EXISTS techbd_udi_ingress.interaction_http_fhir_request_details CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_http_fhir_request_details
AS  
SELECT hub_interaction_id AS interaction_id,
    uri,
    bundle_id,
    created_at AS interaction_created_at, 
    sat_interaction_fhir_request_id AS sat_interaction_http_request_id,
    nature,
    tenant_id,  
    from_state,
    to_state,
    state_transition_reason,
    created_at AS request_created_at,
    created_by AS request_created_by,
    provenance AS request_provenance,
    issues_count, 
    patient_id,
    patient_mrn,   
    source_hub_interaction_id 
   FROM techbd_udi_ingress.sat_interaction_fhir_request htt_req
   WHERE uri != '/api/expect/fhir/bundle'; 

/*******************************************************************************************
-- View to provide detailed information about FHIR HTTP requests linked to hub interactions.  
-- Retrieves request metadata, payload, workflow state transitions, patient identifiers, and issue counts.  
-- Excludes system-generated expectation requests (uri = '/api/expect/fhir/bundle').  
-- Useful for auditing, monitoring, and analytics of interaction-level FHIR requests.  
*******************************************************************************************/


DROP VIEW IF EXISTS techbd_udi_ingress.interaction_http_fhir_request_payload_details CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_http_fhir_request_payload_details
AS  
SELECT hub_interaction_id AS interaction_id,
    uri,
    bundle_id,
    created_at AS interaction_created_at, 
    sat_interaction_fhir_request_id AS sat_interaction_http_request_id,
    nature,
    tenant_id,  
    from_state,
    to_state,
    state_transition_reason,
    created_at AS request_created_at,
    created_by AS request_created_by,
    provenance AS request_provenance,
    issues_count, 
    payload,
    patient_id,
    patient_mrn,   
    source_hub_interaction_id 
   FROM techbd_udi_ingress.sat_interaction_fhir_request htt_req
   WHERE uri != '/api/expect/fhir/bundle';    

/*******************************************************************************************
This view provides a summary of HTTP FHIR requests, selecting the most recent record per 
interaction ID. It includes metadata such as URI, tenant, organization, patient MRN, 
resource types, and source classification ('FHIR', 'CSV', or 'CCDA'). It also flags whether 
the bundle is valid and excludes records targeting the '/api/expect/fhir/bundle' endpoint.
*******************************************************************************************/


DROP VIEW IF EXISTS techbd_udi_ingress.interaction_http_fhir_request_summary CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_http_fhir_request_summary
AS SELECT  created_at AS interaction_created_at,
    hub_interaction_id AS interaction_id,
    uri,
    tenant_id,
    bundle_id,
    patient_mrn,
    organization_id,
    bundle_type AS bundle_resource_type,
    concat_ws('-'::text, tenant_id, organization_id, patient_mrn) AS source_mrn,
    patient_mrn_source_system,
    user_agent,
    client_ip_address,
    resource_type_set AS resource_types,
    source_hub_interaction_id,
        CASE
            WHEN source_type = ANY (ARRAY['CSV'::text, 'CCDA'::text, 'HL7V2'::text]) THEN source_type
            ELSE 'FHIR'::text
        END AS source_type,
        CASE
            WHEN is_bundle_valid = true THEN 'Valid'::text
            WHEN is_bundle_valid = false THEN 'Invalid'::text
            ELSE NULL::text
        END AS is_bundle_valid,
    CASE
    WHEN trim(elaboration->>'status') = 'TechBD-Generated' THEN 'TechBD Generated'
    ELSE ''
	END AS is_consent_generated_by_techbd,
	ig_version,
	techbd_version_number
   FROM techbd_udi_ingress.sat_interaction_fhir_request htt_req
  WHERE uri <> '/api/expect/fhir/bundle'::text
  AND nature = 'Original FHIR Payload' ;


-- techbd_udi_ingress.interaction_http_request_payload source
DROP VIEW IF EXISTS techbd_udi_ingress.interaction_http_request_payload;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_http_request_payload
AS SELECT sat_interaction_http_request_id,
    hub_interaction_id as interaction_id,
    payload
   FROM techbd_udi_ingress.sat_interaction_http_request sihr;   

/*******************************************************************************************
This view captures the most recent observation for each HTTP interaction based on the 
longest duration. It calculates the duration (in milliseconds) between `start_time` and 
`finish_time` for each interaction and retains only entries with non-null timestamps. 
The result is sorted in descending order of interaction duration.
*******************************************************************************************/

DROP VIEW IF EXISTS techbd_udi_ingress.interaction_observe CASCADE;
/* CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_observe
AS 
SELECT *
FROM (
    SELECT DISTINCT ON (intr_observe.interaction_id)
        intr_observe.interaction_id,
        intr_observe.uri,
        intr_observe.start_time,
        intr_observe.finish_time,
        EXTRACT(epoch FROM intr_observe.finish_time - intr_observe.start_time) * 1000::numeric AS duration_millisecs,
        intr_observe.sat_interaction_http_request_id AS interaction_http_request_id
    FROM techbd_udi_ingress.interaction_http_request intr_observe
    WHERE intr_observe.start_time IS NOT NULL
      AND intr_observe.finish_time IS NOT NULL
    ORDER BY intr_observe.interaction_id, 
             EXTRACT(epoch FROM intr_observe.finish_time - intr_observe.start_time) * 1000 DESC
) AS sub
ORDER BY duration_millisecs DESC;
*/

/*******************************************************************************************
This function returns HTTP interaction metrics filtered by date range. It provides detailed
interaction data including interaction ID, URI, start time, finish time, and calculated
duration in milliseconds. The function accepts start_date and end_date parameters to filter
interactions within the specified time range.
*******************************************************************************************/

DROP FUNCTION IF EXISTS techbd_udi_ingress.get_interaction_observe(DATE, DATE);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.get_interaction_observe(
    start_date DATE,
    end_date DATE
)
RETURNS TABLE (
    interaction_id TEXT,
    uri TEXT,
    start_time TIMESTAMP WITH TIME ZONE,
    finish_time TIMESTAMP WITH TIME ZONE,
    duration_millisecs NUMERIC,
    interaction_http_request_id TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH combined_query AS (
        SELECT 
            htt_req.hub_interaction_id AS interaction_id_val,
            htt_req.uri AS uri_val,
            htt_req.interaction_start_time AS start_time_val,
            htt_req.interaction_end_time   AS finish_time_val,
            (EXTRACT(EPOCH FROM (htt_req.interaction_end_time - htt_req.interaction_start_time)) * 1000)::NUMERIC AS duration_millisecs_val,
            htt_req.sat_interaction_fhir_request_id AS interaction_http_request_id_val
        FROM techbd_udi_ingress.sat_interaction_fhir_request htt_req
        WHERE htt_req.interaction_start_time IS NOT NULL
          AND htt_req.interaction_end_time   IS NOT NULL
          AND htt_req.interaction_start_time >= start_date::timestamp
          AND htt_req.interaction_start_time <  end_date::timestamp + INTERVAL '1 day'

        UNION ALL

        SELECT 
            user_req.hub_interaction_id AS interaction_id_val,
            user_req.uri AS uri_val,
            user_req.interaction_start_time AS start_time_val,
            user_req.interaction_end_time   AS finish_time_val,
            (EXTRACT(EPOCH FROM (user_req.interaction_end_time - user_req.interaction_start_time)) * 1000)::NUMERIC AS duration_millisecs_val,
            user_req.sat_interaction_user_id AS interaction_http_request_id_val
        FROM techbd_udi_ingress.sat_interaction_user user_req
        WHERE user_req.uri NOT ILIKE '%/Bundle%' 
          AND user_req.interaction_start_time IS NOT NULL
          AND user_req.interaction_end_time   IS NOT NULL
          AND user_req.interaction_start_time >= start_date::timestamp
          AND user_req.interaction_start_time <  end_date::timestamp + INTERVAL '1 day'
    ),
    ranked AS (
        SELECT *,
               ROW_NUMBER() OVER (
                   PARTITION BY interaction_id_val
                   ORDER BY duration_millisecs_val DESC
               ) AS rn
        FROM combined_query
    )
    SELECT 
        interaction_id_val AS interaction_id,
        uri_val AS uri,
        start_time_val AS start_time,
        finish_time_val AS finish_time,
        duration_millisecs_val AS duration_millisecs,
        interaction_http_request_id_val AS interaction_http_request_id
    FROM ranked
    WHERE rn = 1;
END;
$$ LANGUAGE plpgsql;


/*******************************************************************************************************************************
 * Function to register an HTTP interaction request, handling potential unique constraint violations
 * and logging exceptions when necessary.
 *
 * Parameters:
 * @param interaction_id TEXT - The unique identifier of the interaction.
 * @param interaction_key TEXT - The key to identify the interaction.
 * @param payload JSONB (nullable) - The JSON payload associated with the interaction. Default is NULL.
 * @param payload_text TEXT (nullable) - The text version of the payload. Default is NULL.
 * @param rule_namespace TEXT (nullable) - The namespace for JSON action rules. Default is NULL.
 * @param elaboration JSONB (nullable) - Additional JSON data to elaborate on the interaction. Default is NULL.
 * @param nature JSONB (nullable) - Describes the nature of the HTTP interaction, including tenant details. Default is NULL.
 * @param content_type TEXT (nullable) - The content type of the HTTP interaction. Default is NULL.
 * @param from_state TEXT (nullable) - The state before the HTTP interaction. Default is NULL.
 * @param to_state TEXT (nullable) - The state after the HTTP interaction. Default is NULL.
 * @param state_transition_reason TEXT (nullable) - The reason for the state transition. Default is NULL.
 * @param user_id TEXT (nullable) - The ID of the user involved in the interaction. Default is NULL.
 * @param user_name TEXT (nullable) - The name of the user involved in the interaction. Default is NULL.
 * @param user_session TEXT (nullable) - The session ID of the user involved in the interaction. Default is NULL.
 * @param user_role TEXT (nullable) - The role of the user involved in the interaction. Default is NULL.
 * @param created_at TIMESTAMPTZ (nullable) - The timestamp of when the interaction was created. Defaults to the current timestamp.
 * @param created_by TEXT (nullable) - The user who created the interaction. Defaults to the current user.
 * @param provenance TEXT (nullable) - Metadata providing the provenance or origin of the interaction. Defaults to 'unknown'.
 * @param hub_upsert_behavior BOOLEAN - Controls whether unique constraint violations should be treated as an error (FALSE) or ignored (TRUE). Defaults to TRUE.
 *
 * Returns:
 * @return JSONB - The JSON result of the operation or NULL if the interaction registration fails.
 *
 * Description:
 * This function handles the upserting (inserting or updating) of an HTTP interaction request into the system. It first attempts to insert
 * the interaction into the 'hub_interaction' table and handles any unique constraint violations based on the `hub_upsert_behavior`.
 * It then proceeds to insert the interaction into the 'sat_interaction_http_request' table, extracting relevant details from the `nature` parameter.
 * Depending on the `interaction_key` and `nature`, the function conditionally upserts additional data into satellite tables for FHIR and HL7 requests.
 * Recursive function calls are used to further process FHIR bundles and perform additional validation. 
 * If an error occurs during any step, the function logs the error using the `register_issue` procedure and returns NULL.
 *******************************************************************************************************************************/

DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_http_request(text, text, jsonb, text, text, jsonb, jsonb, text, text, text, text, text, text, text, text, timestamptz, text, text, bool, text, bytea, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text);


CREATE OR REPLACE FUNCTION techbd_udi_ingress.register_interaction_http_request(interaction_id text, interaction_key text, payload jsonb DEFAULT NULL::jsonb, payload_text text DEFAULT NULL::text, rule_namespace text DEFAULT NULL::text, elaboration jsonb DEFAULT NULL::jsonb, nature jsonb DEFAULT NULL::jsonb, content_type text DEFAULT NULL::text, from_state text DEFAULT NULL::text, to_state text DEFAULT NULL::text, state_transition_reason text DEFAULT NULL::text, user_id text DEFAULT NULL::text, user_name text DEFAULT NULL::text, user_session text DEFAULT NULL::text, user_role text DEFAULT NULL::text, created_at timestamp with time zone DEFAULT NULL::timestamp with time zone, created_by text DEFAULT NULL::text, provenance text DEFAULT NULL::text, hub_upsert_behavior boolean DEFAULT true, csv_zip_file_name text DEFAULT NULL::text, csv_zip_file_content bytea DEFAULT NULL::bytea, csv_group_id text DEFAULT NULL::text, csv_status text DEFAULT NULL::text, csv_screening_observation_data_payload_text text DEFAULT NULL::text, csv_screening_profile_data_payload_text text DEFAULT NULL::text, csv_demographic_data_payload_text text DEFAULT NULL::text, csv_qe_admin_data_payload_text text DEFAULT NULL::text, csv_screening_observation_data_file_name text DEFAULT NULL::text, csv_screening_profile_data_file_name text DEFAULT NULL::text, csv_demographic_data_file_name text DEFAULT NULL::text, csv_qe_admin_data_file_name text DEFAULT NULL::text, source_hub_interaction_id text DEFAULT NULL::text, client_ip_address text DEFAULT NULL::text, user_agent text DEFAULT NULL::text, origin text DEFAULT NULL::text, source_type text DEFAULT NULL::text, group_hub_interaction_id text DEFAULT NULL::text, sftp_session_id text DEFAULT NULL::text)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_error_msg 		TEXT;
    v_error_type 		TEXT;
    v_sqlstate 			TEXT;
    v_pg_detail 		TEXT;
    v_pg_hint 			TEXT;
    v_pg_context 		TEXT;
    v_created_at 		TIMESTAMPTZ := COALESCE(created_at, CURRENT_TIMESTAMP);
    v_created_by 		TEXT 		:= COALESCE(created_by, current_user);
    v_provenance 		TEXT 		:= COALESCE(provenance, 'unknown');
    v_exception_id 		TEXT		:= gen_random_uuid()::text;
   	v_nature_denorm 	TEXT;
   	v_tenant_id_denorm 	TEXT;
   
   	v_from_state				TEXT;
   	v_to_state					TEXT;
   	v_state_transition_reason 	TEXT;
   	v_nature 					JSONB;   
   	v_return 					JSONB := NULL;
   	v_disposition_json			JSONB := NULL;
   	v_payload					JSONB := NULL;
    v_elaboration 				JSONB := NULL;
    v_user_id 					TEXT  := COALESCE(register_interaction_http_request.user_id, current_user);	
   
BEGIN 
	/* This function is designed to handle the registration and upserting of HTTP interaction requests into various tables in the 
     * techbd_udi_ingress schema. It first attempts to insert a record into the hub_interaction table and handles unique constraint violations 
     * based on the hub_upsert_behavior flag. It then inserts data into the sat_interaction_http_request table, capturing details such as 
     * payload, state transitions, and user information. Based on the interaction key and nature of the request, the function conditionally calls 
     * upsert functions for user, FHIR, HL7, CCDA or CSV request data. Additionally, it recursively handles FHIR bundle requests, 
     * processing them through further state transitions. In case of any error, it logs detailed exception information using the 
     * register_issue function. The function returns a JSONB object, representing either the inserted data or a processed result.
     * origin : http/sftp
     * source_type : fhir/hl7/csv/ccda
     */
    -- Attempt to insert into hub_interaction
	BEGIN	   	    
        INSERT INTO techbd_udi_ingress.hub_interaction (hub_interaction_id, key, created_at, created_by, provenance)
        VALUES (register_interaction_http_request.interaction_id, interaction_key, v_created_at, v_created_by, v_provenance);
        /*---register diagnostic log---*/
       	v_elaboration := jsonb_build_object(
         						'interaction_id', register_interaction_http_request.interaction_id,
         						'interaction_key', register_interaction_http_request.interaction_key,
         						'nature', register_interaction_http_request.nature->>'nature',
         						'from_state', register_interaction_http_request.from_state,
         						'to_state', register_interaction_http_request.to_state,
         						'step', '1. Insert into hub_interaction'
         						);
        CALL techbd_udi_ingress.register_diagnostic_log(v_exception_id, register_interaction_http_request.interaction_key, v_created_by, v_provenance, 'info'::text, 
       																'Insert into hub_interaction'::text, v_user_id, 'success'::text, NULL::text, 0, v_elaboration);
        /*------------------------------*/   
    EXCEPTION
        WHEN unique_violation THEN
            IF NOT hub_upsert_behavior THEN
                -- Capture exception details
                GET STACKED DIAGNOSTICS
                    v_error_msg = MESSAGE_TEXT,
                    v_sqlstate = RETURNED_SQLSTATE,
                    v_pg_detail = PG_EXCEPTION_DETAIL,
                    v_pg_hint = PG_EXCEPTION_HINT,
                    v_pg_context = PG_EXCEPTION_CONTEXT;
                    v_error_type = 'SQL';

                -- Call register_issue to log the exception and get the exception ID
                v_exception_id := techbd_udi_ingress.register_issue(
                    COALESCE(v_exception_id, NULL), register_interaction_http_request.interaction_key, v_error_msg, v_error_type, v_sqlstate, v_pg_detail, v_pg_hint, v_pg_context, v_created_by, v_provenance
                );
            END IF; 
       WHEN OTHERS THEN
        	GET STACKED DIAGNOSTICS
                v_error_msg = MESSAGE_TEXT,
                v_sqlstate = RETURNED_SQLSTATE,
                v_pg_detail = PG_EXCEPTION_DETAIL,
                v_pg_hint = PG_EXCEPTION_HINT,
                v_pg_context = PG_EXCEPTION_CONTEXT;
                v_error_type = 'SQL';

         	v_elaboration := jsonb_build_object(
         						'interaction_id', register_interaction_http_request.interaction_id,
         						'interaction_key', register_interaction_http_request.interaction_key,
         						'payload', register_interaction_http_request.payload,
         						'payload_text', register_interaction_http_request.payload_text,
         						'nature', register_interaction_http_request.nature,
         						'from_state', register_interaction_http_request.from_state,
         						'to_state', register_interaction_http_request.to_state
         						);        
               
            -- Log the exception, reusing the previous exception ID if it exists
            v_exception_id := techbd_udi_ingress.register_issue(
                COALESCE(v_exception_id, NULL), register_interaction_http_request.interaction_key, v_error_msg, v_error_type, v_sqlstate, v_pg_detail, v_pg_hint, v_pg_context, v_created_by, v_provenance, v_elaboration
            );
           RETURN NULL;
    END;
    -- Attempt to insert into sat_interaction_http_request
    BEGIN
  
	    v_nature_denorm = trim(nature->>'nature');
	   	v_tenant_id_denorm = nature->>'tenant_id';
	   	/*INSERT INTO techbd_udi_assurance.pgtap_fixtures_json("name","jsonb")
	   	values(gen_random_uuid()::TEXT, register_interaction_http_request.payload);*/
	  
	    -- The insertion to sat_interaction_http_request table needs only once. Need to be corrected later.
        INSERT INTO techbd_udi_ingress.sat_interaction_http_request (
            sat_interaction_http_request_id, hub_interaction_id, nature, content_type, payload, payload_text, from_state, 
            to_state, state_transition_reason, elaboration, created_at, created_by, provenance, nature_denorm, tenant_id_denorm
        )
        VALUES (
            gen_random_uuid()::text, register_interaction_http_request.interaction_id, nature, content_type, payload, payload_text, from_state, to_state, 
            state_transition_reason, elaboration, v_created_at, v_created_by, v_provenance, v_nature_denorm, v_tenant_id_denorm
        );
        /*---register diagnostic log---*/
       	v_elaboration := jsonb_build_object(
         						'interaction_id', register_interaction_http_request.interaction_id,
         						'interaction_key', register_interaction_http_request.interaction_key,
         						'nature', register_interaction_http_request.nature->>'nature',
         						'from_state', register_interaction_http_request.from_state,
         						'to_state', register_interaction_http_request.to_state,
         						'step', '2. Insert into sat_interaction_http_request'
         						);
        CALL techbd_udi_ingress.register_diagnostic_log(v_exception_id, register_interaction_http_request.interaction_key, v_created_by, v_provenance, 'info'::text, 
       																'Insert into sat_interaction_http_request'::text, v_user_id, 'success'::text, NULL::text, 0, v_elaboration);
        /*------------------------------*/
       	--1) Call the function with the defined parameters to upsert user info
       	IF((v_nature_denorm = 'org.techbd.service.http.Interactions$RequestResponseEncountered' AND trim(register_interaction_http_request.interaction_key) NOT IN ('/Hl7/v2', '/Hl7/v2/', '/flatfile/csv/Bundle', '/flatfile/csv/Bundle/', '/flatfile/csv/Bundle/$validate', '/flatfile/csv/Bundle/$validate/', '/ccda/Bundle', '/ccda/Bundle/', '/ccda/Bundle/$validate', '/ccda/Bundle/$validate/')) /*fhir bundle*/
       	   OR (v_nature_denorm = 'Original HL7 Payload'	AND trim(register_interaction_http_request.interaction_key) IN ('/Hl7/v2', '/Hl7/v2/'))			/*hl7*/
       	   OR (v_nature_denorm = 'Original CSV Zip Archive' AND trim(register_interaction_http_request.interaction_key) IN ('/flatfile/csv/Bundle', '/flatfile/csv/Bundle/', '/flatfile/csv/Bundle/$validate', '/flatfile/csv/Bundle/$validate/'))   	/*csv*/
		   OR (v_nature_denorm = 'Original CCDA Payload' AND trim(register_interaction_http_request.interaction_key) IN ('/ccda/Bundle', '/ccda/Bundle/', '/ccda/Bundle/$validate', '/ccda/Bundle/$validate/'))   			/*ccda*/	
		  ) THEN     		       	
	    	PERFORM techbd_udi_ingress.sat_interaction_user_upserted(
		        hub_interaction_id 			=> register_interaction_http_request.interaction_id,
		        hub_interaction_key			=> register_interaction_http_request.interaction_key, 
	            nature						=> v_nature_denorm,
		        payload						=> register_interaction_http_request.payload, 
		        user_id						=> register_interaction_http_request.user_id, 
		        user_name					=> register_interaction_http_request.user_name, 
		        user_session				=> register_interaction_http_request.user_session, 
		        user_role					=> register_interaction_http_request.user_role,
		        tenant_id					=> v_tenant_id_denorm,
		        elaboration					=> register_interaction_http_request.elaboration,
		        created_at 					=> v_created_at,
		        created_by 					=> v_created_by,
		        provenance 					=> v_provenance
	       	); 
	      	/*---register diagnostic log---*/
	      	v_elaboration := jsonb_build_object(
         						'interaction_id', register_interaction_http_request.interaction_id,
         						'interaction_key', register_interaction_http_request.interaction_key,
         						'nature', register_interaction_http_request.nature->>'nature',
         						'from_state', register_interaction_http_request.from_state,
         						'to_state', register_interaction_http_request.to_state,
         						'step', '3. Insert into user info'
         						);
         	CALL techbd_udi_ingress.register_diagnostic_log(v_exception_id, register_interaction_http_request.interaction_key, v_created_by, v_provenance, 'info'::text, 
       																'Insert into user info'::text, v_user_id, 'success'::text, NULL::text, 0, v_elaboration);
	      	/*------------------------------*/
	    END IF;
	   
	    --2.1) Call the function with the defined parameters to upsert interaction FHIR request info.
	   	IF trim(register_interaction_http_request.interaction_key) IN ('/Bundle', '/Bundle/', '/Bundle/$validate', '/api/expect/fhir/bundle', '/Hl7/v2', '/Hl7/v2/', '/flatfile/csv/Bundle', '/flatfile/csv/Bundle/', '/flatfile/csv/Bundle/$validate', '/flatfile/csv/Bundle/$validate/', '/ccda/Bundle', '/ccda/Bundle/', '/ccda/Bundle/$validate', '/ccda/Bundle/$validate/')
	   		AND v_nature_denorm NOT IN ('Original HL7 Payload', 'Original CSV Zip Archive', 'Original Flat File CSV', 'CSV Validation Result', 'Converted to FHIR', 'Original CCDA Payload') THEN
	   		
	   		PERFORM techbd_udi_ingress.sat_interaction_fhir_request_upserted(
	   			hub_interaction_id 			=> register_interaction_http_request.interaction_id,
		        tenant_id 					=> v_tenant_id_denorm,
		        uri 						=> register_interaction_http_request.interaction_key,
		        nature 						=> v_nature_denorm,
		        from_state 					=> register_interaction_http_request.from_state,
		        to_state 					=> register_interaction_http_request.to_state,
		        payload						=> register_interaction_http_request.payload,
		        state_transition_reason		=> register_interaction_http_request.state_transition_reason,
		        created_at 					=> v_created_at,
		        created_by 					=> v_created_by,
		        provenance 					=> v_provenance,
		        elaboration					=> register_interaction_http_request.elaboration,	     
		      	source_type					=> register_interaction_http_request.source_type, 
		      	source_hub_interaction_id	=> register_interaction_http_request.source_hub_interaction_id,
		      	group_hub_interaction_id	=> register_interaction_http_request.group_hub_interaction_id
	       	);
	       	/*---register diagnostic log---*/
	       	v_elaboration := jsonb_build_object(
         						'interaction_id', register_interaction_http_request.interaction_id,
         						'interaction_key', register_interaction_http_request.interaction_key,
         						'nature', register_interaction_http_request.nature->>'nature',
         						'from_state', register_interaction_http_request.from_state,
         						'to_state', register_interaction_http_request.to_state,
         						'step', '4. Insert into sat_interaction_fhir_request'
         						);
           	CALL techbd_udi_ingress.register_diagnostic_log(v_exception_id, register_interaction_http_request.interaction_key, v_created_by, v_provenance, 'info'::text, 
       																'Insert into sat_interaction_fhir_request'::text, v_user_id, 'success'::text, NULL::text, 0, v_elaboration);
       	   	/*------------------------------*/															
	    --2.2) Call the function with the defined parameters to upsert interaction HL7 request info.
	    ELSIF trim(register_interaction_http_request.interaction_key) IN ('/Hl7/v2', '/Hl7/v2/') AND v_nature_denorm = 'Original HL7 Payload' THEN
	   		PERFORM techbd_udi_ingress.sat_interaction_hl7_request_upserted(
		        hub_interaction_id 			=> register_interaction_http_request.interaction_id,
		        tenant_id 					=> v_tenant_id_denorm,
		        uri 						=> register_interaction_http_request.interaction_key,
		        nature 						=> v_nature_denorm,
		        from_state 					=> register_interaction_http_request.from_state,
		        to_state 					=> register_interaction_http_request.to_state,
		        payload						=> register_interaction_http_request.payload,
		        state_transition_reason		=> register_interaction_http_request.state_transition_reason,
		        created_at 					=> v_created_at,
		        created_by 					=> v_created_by,
		        provenance 					=> v_provenance,
		        elaboration					=> register_interaction_http_request.elaboration,
		        client_ip_address 			=> register_interaction_http_request.client_ip_address, 
		        user_agent					=> register_interaction_http_request.user_agent, 
		        origin						=> register_interaction_http_request.origin
	       	);
	       	/*---register diagnostic log---*/
	       	v_elaboration := jsonb_build_object(
         						'interaction_id', register_interaction_http_request.interaction_id,
         						'interaction_key', register_interaction_http_request.interaction_key,
         						'nature', register_interaction_http_request.nature->>'nature',
         						'from_state', register_interaction_http_request.from_state,
         						'to_state', register_interaction_http_request.to_state,
         						'step', '4. Insert into sat_interaction_hl7_request'
         						);
           	CALL techbd_udi_ingress.register_diagnostic_log(v_exception_id, register_interaction_http_request.interaction_key, v_created_by, v_provenance, 'info'::text, 
       																'Insert into sat_interaction_hl7_request'::text, v_user_id, 'success'::text, NULL::text, 0, v_elaboration);
       	   	/*------------------------------*/
       	--2.3) Call the function with the defined parameters to upsert interaction CSV request info.													
		ELSIF trim(register_interaction_http_request.interaction_key) IN ('/flatfile/csv/Bundle', '/flatfile/csv/Bundle/', '/flatfile/csv/Bundle/$validate', '/flatfile/csv/Bundle/$validate/') AND v_nature_denorm IN ('Original CSV Zip Archive', 'Original Flat File CSV', 'CSV Validation Result', 'Converted to FHIR') THEN
			PERFORM techbd_udi_ingress.sat_interaction_csv_request_upserted(
		        interaction_id 				=> register_interaction_http_request.interaction_id,
		        uri 						=> register_interaction_http_request.interaction_key,
		        nature 						=> v_nature_denorm,
		        tenant_id 					=> v_tenant_id_denorm,
		        csv_zip_file_name 			=> register_interaction_http_request.csv_zip_file_name,
		        csv_zip_file_content 		=> register_interaction_http_request.csv_zip_file_content,
		        group_id 					=> register_interaction_http_request.csv_group_id,
		        status						=> register_interaction_http_request.csv_status,
		        csv_screening_observation_data_payload_text => register_interaction_http_request.csv_screening_observation_data_payload_text, 
		        csv_screening_profile_data_payload_text		=> register_interaction_http_request.csv_screening_profile_data_payload_text, 
		        csv_demographic_data_payload_text 			=> register_interaction_http_request.csv_demographic_data_payload_text, 
		        csv_qe_admin_data_payload_text 				=> register_interaction_http_request.csv_qe_admin_data_payload_text,
				csv_screening_observation_data_file_name 	=> register_interaction_http_request.csv_screening_observation_data_file_name, 
		        csv_screening_profile_data_file_name 		=> register_interaction_http_request.csv_screening_profile_data_file_name, 
		        csv_demographic_data_file_name 				=> register_interaction_http_request.csv_demographic_data_file_name, 
		        csv_qe_admin_data_file_name 				=> register_interaction_http_request.csv_qe_admin_data_file_name,
		        validation_result_payload 					=> register_interaction_http_request.payload,		        
		        from_state 					=> register_interaction_http_request.from_state,
		        to_state 					=> register_interaction_http_request.to_state,
		        state_transition_reason 	=> register_interaction_http_request.state_transition_reason,
		        created_at 					=> v_created_at,
		        created_by 					=> v_created_by,
		        provenance 					=> v_provenance,
		        elaboration 				=> NULL::jsonb,
		        zip_file_hub_interaction_id => register_interaction_http_request.source_hub_interaction_id,
		        client_ip_address 			=> register_interaction_http_request.client_ip_address, 
		        user_agent 					=> register_interaction_http_request.user_agent,
		        origin						=> register_interaction_http_request.origin,
		        sftp_session_id				=> register_interaction_http_request.sftp_session_id
	       	);
	       	/*---register diagnostic log---*/
	       	v_elaboration := jsonb_build_object(
         						'interaction_id', register_interaction_http_request.interaction_id,
         						'interaction_key', register_interaction_http_request.interaction_key,
         						'nature', register_interaction_http_request.nature->>'nature',
         						'from_state', register_interaction_http_request.from_state,
         						'to_state', register_interaction_http_request.to_state,
         						'step',  '4. Insert into ' || CASE WHEN v_nature_denorm = 'Original CSV Zip Archive' THEN 'sat_interaction_flat_file_csv_request' ELSE 'sat_interaction_zip_file_request' END 
         						);
           	CALL techbd_udi_ingress.register_diagnostic_log(v_exception_id, register_interaction_http_request.interaction_key, v_created_by, v_provenance, 'info'::text, 
       																'Insert into sat_interaction_csv_request'::text, v_user_id, 'success'::text, NULL::text, 0, v_elaboration);
       	   	/*------------------------------*/ 
		--2.4) Call the function with the defined parameters to upsert interaction CCDA request info.
	    ELSIF trim(register_interaction_http_request.interaction_key) IN ('/ccda/Bundle', '/ccda/Bundle/','/ccda/Bundle/$validate', '/ccda/Bundle/$validate/') AND v_nature_denorm IN ('Original CCDA Payload') THEN
	   		PERFORM techbd_udi_ingress.sat_interaction_ccda_request_upserted(
	        register_interaction_http_request.interaction_id,
	        v_tenant_id_denorm,
	        register_interaction_http_request.interaction_key,
	        v_nature_denorm,
	        register_interaction_http_request.from_state,
	        register_interaction_http_request.to_state,
	        register_interaction_http_request.payload,
			register_interaction_http_request.payload_text,
	        register_interaction_http_request.state_transition_reason,
	        v_created_at,
	        v_created_by,
	        v_provenance,
	        register_interaction_http_request.elaboration
	       );
       	END IF;	
	    /*==============================================================================================================================*/
       
		--RECURSIVE FUNCTION CALL
	   	/*----------------------------- FHIR Bundle inserts into sat_interaction_http_request ----------------------*/
		IF trim(register_interaction_http_request.interaction_key) IN ('/Bundle', '/Bundle/', '/Hl7/v2', '/Hl7/v2/', '/flatfile/csv/Bundle', '/flatfile/csv/Bundle/', '/flatfile/csv/Bundle/$validate', '/flatfile/csv/Bundle/$validate/', '/ccda/Bundle', '/ccda/Bundle/', '/ccda/Bundle/$validate', '/ccda/Bundle/$validate/') THEN  
			IF (v_nature_denorm = 'org.techbd.service.http.Interactions$RequestResponseEncountered') THEN	--Check previous nature				
				v_from_state 	:= 'NONE';
				v_to_state 		:= 'ACCEPT_FHIR_BUNDLE';
				v_nature 		:= jsonb_build_object('nature', 'Original FHIR Payload', 'tenant_id', v_tenant_id_denorm);
				v_payload 		:= COALESCE(register_interaction_http_request.payload->'request'->'requestBody', 'null'::jsonb);
			
			   	/*---register diagnostic log---*/
	       		v_elaboration := jsonb_build_object(
         						'interaction_id', register_interaction_http_request.interaction_id,
         						'interaction_key', register_interaction_http_request.interaction_key,
         						'nature', register_interaction_http_request.nature->>'nature',
         						'from_state', register_interaction_http_request.from_state,
         						'to_state', register_interaction_http_request.to_state,
         						'step', '5. Insert into sat_interaction_http_request - in recursive call'
         						);
           		CALL techbd_udi_ingress.register_diagnostic_log(v_exception_id, register_interaction_http_request.interaction_key, v_created_by, v_provenance, 'info'::text, 
       																'Insert into sat_interaction_http_request'::text, v_user_id, 'success'::text, NULL::text, 0, v_elaboration);
       	   		/*------------------------------*/
			
				   
				-- Recursive call with adjusted parameters
				v_return := techbd_udi_ingress.register_interaction_http_request(
					interaction_id				=> register_interaction_http_request.interaction_id, 
					interaction_key				=> register_interaction_http_request.interaction_key,  
					payload						=> v_payload,--register_interaction_http_request.payload,  -- Adjust this if needed
					payload_text				=> register_interaction_http_request.payload_text, -- payload_text IS NULL FOR FHIR
					rule_namespace				=> register_interaction_http_request.rule_namespace,
					elaboration					=> register_interaction_http_request.elaboration, 
					nature						=> v_nature,   -- Adjust this if needed
					content_type				=> register_interaction_http_request.content_type, 
					from_state					=> v_from_state, 
					to_state					=> v_to_state, 
					state_transition_reason		=> register_interaction_http_request.state_transition_reason, 
					user_id						=> register_interaction_http_request.user_id, 
					user_name					=> register_interaction_http_request.user_name, 
					user_session				=> register_interaction_http_request.user_session, 
					user_role					=> register_interaction_http_request.user_role, 
					created_at					=> register_interaction_http_request.created_at, 
					created_by					=> register_interaction_http_request.created_by, 
					provenance					=> register_interaction_http_request.provenance, 
					hub_upsert_behavior			=> register_interaction_http_request.hub_upsert_behavior,
					source_hub_interaction_id	=> register_interaction_http_request.source_hub_interaction_id,
					origin 						=> register_interaction_http_request.origin,
					source_type					=> register_interaction_http_request.source_type,
					group_hub_interaction_id	=> register_interaction_http_request.group_hub_interaction_id
			    );
			ELSEIF (v_nature_denorm = 'Original FHIR Payload') THEN	
				v_from_state 	:= 'ACCEPT_FHIR_BUNDLE';
				v_to_state		:= 'DISPOSITION';
				v_nature 		:= jsonb_build_object('nature', 'techByDesignDisposition', 'tenant_id', v_tenant_id_denorm);
				
				SELECT sat_intr_req.payload
				INTO v_payload
				FROM techbd_udi_ingress.sat_interaction_http_request sat_intr_req
				WHERE sat_intr_req.hub_interaction_id = register_interaction_http_request.interaction_id
				AND sat_intr_req.nature_denorm = 'org.techbd.service.http.Interactions$RequestResponseEncountered'
				AND sat_intr_req.tenant_id_denorm = v_tenant_id_denorm;
				
				--Function call to validate with Json Action Rule
				v_disposition_json := techbd_udi_ingress.process_json_action_rules(
					v_payload,
					register_interaction_http_request.rule_namespace,
					register_interaction_http_request.interaction_key
				);
			
				/*---register diagnostic log---*/
	       		v_elaboration := jsonb_build_object(
         						'interaction_id', register_interaction_http_request.interaction_id,
         						'interaction_key', register_interaction_http_request.interaction_key,
         						'nature', register_interaction_http_request.nature->>'nature',
         						'from_state', register_interaction_http_request.from_state,
         						'to_state', register_interaction_http_request.to_state,
         						'v_disposition_json', v_disposition_json::TEXT,
         						'step', '6. Insert into sat_interaction_http_request - in recursive call after process_json_action_rules'
         						);
           		CALL techbd_udi_ingress.register_diagnostic_log(v_exception_id, register_interaction_http_request.interaction_key, v_created_by, v_provenance, 'info'::text, 
       																'Insert into sat_interaction_http_request'::text, v_user_id, 'success'::text, NULL::text, 0, v_elaboration);
       	   		/*------------------------------*/
			
				v_disposition_json := v_disposition_json-> 'response'->'responseBody';
			
					
				-- Recursive call with adjusted parameters
				v_return := techbd_udi_ingress.register_interaction_http_request(
					interaction_id				=> register_interaction_http_request.interaction_id, 
					interaction_key				=> register_interaction_http_request.interaction_key,    
					payload						=> v_disposition_json, -- Adjust this if needed
					payload_text				=> register_interaction_http_request.payload_text, -- payload_text IS NULL FOR FHIR
					rule_namespace				=> register_interaction_http_request.rule_namespace,
					elaboration					=> register_interaction_http_request.elaboration, 
					nature						=> v_nature,   -- Adjust this if needed
				    content_type				=> register_interaction_http_request.content_type, 
					from_state					=> v_from_state, 
					to_state					=> v_to_state, 
					state_transition_reason		=> register_interaction_http_request.state_transition_reason, 
					user_id						=> register_interaction_http_request.user_id, 
					user_name					=> register_interaction_http_request.user_name, 
					user_session				=> register_interaction_http_request.user_session, 
					user_role					=> register_interaction_http_request.user_role, 
					created_at					=> register_interaction_http_request.created_at, 
					created_by					=> register_interaction_http_request.created_by, 
					provenance					=> register_interaction_http_request.provenance, 
					hub_upsert_behavior			=> register_interaction_http_request.hub_upsert_behavior,
					source_hub_interaction_id	=> register_interaction_http_request.source_hub_interaction_id,
					origin 						=> register_interaction_http_request.origin,
					source_type					=> register_interaction_http_request.source_type,
					group_hub_interaction_id	=> register_interaction_http_request.group_hub_interaction_id
			    );
			   
			ELSEIF (v_nature_denorm = 'techByDesignDisposition') THEN
			
				--v_return := register_interaction_http_request.payload;
			
				SELECT sat_intr_req.payload
					INTO v_payload
					FROM techbd_udi_ingress.sat_interaction_http_request sat_intr_req
					WHERE sat_intr_req.hub_interaction_id = register_interaction_http_request.interaction_id
					AND sat_intr_req.nature_denorm = 'techByDesignDisposition'
					AND sat_intr_req.tenant_id_denorm = v_tenant_id_denorm;
	
				v_return := v_payload;
			
				/*---register diagnostic log---*/
	       		v_elaboration := jsonb_build_object(
         						'interaction_id', register_interaction_http_request.interaction_id,
         						'interaction_key', register_interaction_http_request.interaction_key,
         						'nature', register_interaction_http_request.nature->>'nature',
         						'from_state', register_interaction_http_request.from_state,
         						'to_state', register_interaction_http_request.to_state,
         						'v_disposition_json', v_disposition_json::TEXT,
         						'step', '7. Insert into sat_interaction_http_request - in recursive call techByDesignDisposition'
         						);
           		CALL techbd_udi_ingress.register_diagnostic_log(v_exception_id, register_interaction_http_request.interaction_key, v_created_by, v_provenance, 'info'::text, 
       																'Insert into sat_interaction_http_request'::text, v_user_id, 'success'::text, NULL::text, 0, v_elaboration);
       	   		/*------------------------------*/
			
			END IF;	
		END IF;	
	/*==============================================================================================================================*/	 	
    EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception details
            GET STACKED DIAGNOSTICS
                v_error_msg = MESSAGE_TEXT,
                v_sqlstate = RETURNED_SQLSTATE,
                v_pg_detail = PG_EXCEPTION_DETAIL,
                v_pg_hint = PG_EXCEPTION_HINT,
                v_pg_context = PG_EXCEPTION_CONTEXT;
                v_error_type = 'SQL';

         	--RAISE NOTICE 'Error occurred: %', SQLERRM; /*Test purpose*/ 
         	v_elaboration := jsonb_build_object(
         						'interaction_id', register_interaction_http_request.interaction_id,
         						'interaction_key', register_interaction_http_request.interaction_key,
         						'payload', register_interaction_http_request.payload,
         						'payload_text', register_interaction_http_request.payload_text,
         						'nature', register_interaction_http_request.nature->>'nature',
         						'from_state', register_interaction_http_request.from_state,
         						'to_state', register_interaction_http_request.to_state
         						);        
               
            -- Log the exception, reusing the previous exception ID if it exists
            v_exception_id := techbd_udi_ingress.register_issue(
                COALESCE(v_exception_id, NULL), register_interaction_http_request.interaction_key, v_error_msg, v_error_type, v_sqlstate, v_pg_detail, v_pg_hint, v_pg_context, v_created_by, v_provenance, v_elaboration
            );
           v_return := NULL;
    END;
   	RETURN v_return;
END;
$function$
;







/*******************************************************************************************
 * Comprehensive view of Orchctl issue classification. * 
 ******************************************************************************************/
 /*
DROP VIEW IF EXISTS techbd_udi_ingress.orch_session_issue_classification CASCADE;
CREATE OR REPLACE
VIEW techbd_udi_ingress.orch_session_issue_classification as
WITH cte_business_rule AS (
SELECT
	worksheet AS worksheet,
	field AS field,
	required AS required,
	"Resolved by QE/QCS" AS resolved_by_qe_qcs,
	CONCAT(
          case
		when UPPER("True Rejection") = 'YES' then 'REJECTION'
		else ''
	end,
	case
		when UPPER("Warning Layer") = 'YES' then 'WARNING'
		else ''
	end
      ) AS record_action
FROM
	techbd_orch_ctl.business_rules
GROUP BY
	worksheet,
	field,
	required,
	resolved_by_qe_qcs,
	record_action
)
SELECT
	DISTINCT ON
	(isi.orch_session_issue_id) 
  isi.*,
	CASE
		WHEN
        UPPER(isi.issue_type) = 'MISSING COLUMN'
      THEN
        'STRUCTURAL ISSUE'
		ELSE
        br.record_action
	END
  AS disposition,
	CASE
		WHEN UPPER(br.resolved_by_qe_qcs) = 'YES' THEN 'Resolved By QE/QCS'
		ELSE null
	END AS remediation2
FROM
	techbd_orch_ctl.orch_session_issue AS isi
LEFT JOIN cte_business_rule br ON
	br.field = isi.issue_column
WHERE
	isi.orch_session_issue_id is not null
;

*/

/*******************************************************************************************
 * Comprehensive view of SFTP interactions and their associated requests. * 
 ******************************************************************************************/
DROP VIEW IF EXISTS techbd_udi_ingress.interaction_sftp CASCADE;
/* CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_sftp AS 
WITH issue_count AS (
SELECT
	osi.session_id,
	count(osi.orch_session_issue_id) AS issue_count
FROM
	techbd_udi_ingress.orch_session_issue_classification osi
GROUP BY
	osi.session_id
),
rejection_count AS (
SELECT
	osi.session_id,
	count(osi.orch_session_issue_id) AS rejection_count
FROM
	techbd_udi_ingress.orch_session_issue_classification osi
WHERE 
		osi.disposition = 'REJECTION'
GROUP BY
	osi.session_id
),
warning_count AS (
SELECT
	osi.session_id,
	count(osi.orch_session_issue_id) AS warning_count
FROM
	techbd_udi_ingress.orch_session_issue_classification osi
WHERE 
		osi.disposition = 'WARNING'
GROUP BY
	osi.session_id
)
SELECT
	"substring"(ose.ingest_src::text,
	'/SFTP/([^/]+)/'::text) AS qe,
	os.orch_started_at::timestamptz AS request_time,
	ose.session_id,
	os."version",
	count(ose.ingest_src) AS ingress_count,
	count(ose.ingest_src) AS consumed_count,
	coalesce(ic.issue_count,
	0) AS issue_count,
	coalesce(rc.rejection_count,
	0) AS rejection_count,
	coalesce(wc.warning_count,
	0) AS warning_count
FROM
	techbd_orch_ctl.orch_session_entry ose
JOIN techbd_orch_ctl.orch_session os ON
	ose.session_id = os.orch_session_id
LEFT JOIN issue_count ic ON
	ose.session_id = ic.session_id
LEFT JOIN rejection_count rc ON
	ose.session_id = rc.session_id
LEFT JOIN warning_count wc ON
	ose.session_id = wc.session_id
WHERE
	"substring"(ose.ingest_src::text,
	'/SFTP/([^/]+)/'::text) is not null
GROUP BY
	ose.session_id,
	qe,
	os."version",
	os.orch_started_at::timestamptz,
	ic.issue_count,
	rc.rejection_count,
	wc.warning_count;
*/

/*******************************************************************************************
This view selects the most recent FHIR interaction per tenant from the 
`sat_interaction_fhir_request` table where the request URI matches specific '/Bundle'-related 
paths. It returns the latest interaction details along with the total count of such 
interactions per tenant. Only the most recent interaction per tenant is retained.
*******************************************************************************************/

DROP VIEW IF EXISTS techbd_udi_ingress.interaction_recent_fhir CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_recent_fhir AS
  SELECT DISTINCT ON (tenant_id_lower)
    sihr.sat_interaction_fhir_request_id AS sat_interaction_http_request_id,
    sihr.hub_interaction_id AS interaction_id,
    sihr.tenant_id,
	sihr.tenant_id_lower,
    sihr.created_at AS interaction_created_at,
    COUNT(*) OVER (PARTITION BY sihr.tenant_id_lower) AS interaction_count
FROM techbd_udi_ingress.sat_interaction_fhir_request sihr
WHERE sihr.uri IN ('/Bundle', '/Bundle/$validate', '/Bundle/', '/Bundle/$validate/')
ORDER BY tenant_id_lower, sihr.created_at DESC;


/**
 * View to summarize FHIR submission interactions from the database.
 * This view provides an aggregated overview of HTTP request interactions based on
 * different types of nature values recorded in the `sat_interaction_http_request` table.
 * The view calculates the following metrics over the past 7 days:
 *
 * 1. `total_submissions`: Counts submissions where the nature is 'Original FHIR Payload'.
 * 2. `accepted_submissions`: Counts submissions where the nature is 'Forwarded HTTP Response'.
 * 3. `rejected_submissions`: Counts submissions where the nature is 'Forwarded HTTP Response Error'.
 * 4. `pending_submissions`: Counts submissions where the nature is neither 'Forwarded HTTP Response',
 *    'Forwarded HTTP Response Error', nor 'Original FHIR Payload'.
 *
 * This view aggregates data for all interactions related to FHIR submissions based on the following:
 * - Interaction metadata including the nature of HTTP requests.
 * - Filtering interactions that occurred in the last 7 days.
 * - Using a specific key value (`/Bundle`) to filter interactions.
 *
 * The view helps in monitoring and analyzing FHIR interaction statuses, providing insight into:
 * - Total count of submissions made in the specified period.
 * - Number of submissions that were accepted or rejected.
 * - Submissions that are pending or still not categorized as accepted or rejected.
 *
 * Usage:
 * SELECT * FROM techbd_udi_ingress.fhir_submission_summary;
 *
 * @return TABLE - A table summarizing total, accepted, rejected, and pending FHIR submissions
 *         for interactions within the past 7 days.
 */

DROP VIEW IF EXISTS techbd_udi_ingress.fhir_submission_summary CASCADE;

CREATE OR REPLACE VIEW techbd_udi_ingress.fhir_submission_summary AS 
   WITH submission_counts AS (
    SELECT  
        --NONE    -> ACCEPT_FHIR_BUNDLE
        COUNT(CASE WHEN sihr.nature = 'Original FHIR Payload' THEN 1 END) AS total_submissions,
        --FORWARD-> COMPLETE
        COUNT(CASE WHEN sihr.nature = 'Forwarded HTTP Response' THEN 1 END) AS accepted_submissions,
        --FORWARD-> FAIL
        COUNT(CASE WHEN sihr.nature = 'Forwarded HTTP Response Error' THEN 1 END) AS rejected_submissions
    FROM 
        techbd_udi_ingress.sat_interaction_fhir_request AS sihr
    WHERE 
        sihr."uri" = '/Bundle'
        AND sihr.created_at >= CURRENT_DATE - INTERVAL '7 days'
)
SELECT  
    total_submissions,
    accepted_submissions,
    rejected_submissions,
    total_submissions - accepted_submissions - rejected_submissions AS pending_submissions
FROM 
    submission_counts;


/****************************************************************************************
 * View for tracking recent FHIR submissions and identifying missed submissions per tenant.
 *
 * Purpose:
 * - Summarizes total and successful FHIR interactions for each tenant over the last 7 days.
 * - Highlights missed submissions not forwarded to Shinny Datalake.
 *
 * Columns:
 * - **qe_name**: Tenant name.
 * - **qe_total_submissions**: Total interactions with request-response nature.
 * - **shinny_datalake_submissions**: Count of forwarded submissions.
 * - **missed_shinny_datalake_submissions**: Submissions not forwarded.
 * - **recently_created_at**: Most recent interaction timestamp.
 *
 * Filters:
 * - Focused on '/Bundle/' URIs and recent interactions, excluding 'N/A' tenants.
 * - Ordered by recent activity for quick prioritization.
 ****************************************************************************************/

DROP VIEW IF EXISTS techbd_udi_ingress.fhir_needs_attention CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.fhir_needs_attention AS  
WITH submission_counts AS (
         SELECT sihr.tenant_id_lower AS qe_name,
            count(
                CASE
                    WHEN sihr.nature = 'Original FHIR Payload'::text THEN 1
                    ELSE NULL::integer
                END) AS qe_total_submissions,
 
            count(
                CASE
                    WHEN sihr.nature = 'Forwarded HTTP Response'::text THEN 1
                    ELSE NULL::integer
                END) AS shinny_datalake_submissions,
           count(
                CASE
                    WHEN sihr.nature = 'Forwarded HTTP Response Error'::text THEN 1
                    ELSE NULL::integer
                END) AS shinny_datalake_submissions_failed,
            max(sihr.created_at) AS recently_created_at
           FROM techbd_udi_ingress.sat_interaction_fhir_request sihr
          WHERE (sihr.uri = ANY (ARRAY['/Bundle/'::text, '/Bundle'::text])) AND sihr.created_at >= (CURRENT_DATE - '7 days'::interval) AND sihr.tenant_id <> 'N/A'::text
          GROUP BY sihr.tenant_id_lower
        )
  SELECT 
    qe_name,
    qe_total_submissions,  
    shinny_datalake_submissions,
    shinny_datalake_submissions_failed,
    recently_created_at 
   FROM submission_counts sc
  ORDER BY recently_created_at DESC;


/*************************************
* The stored procedure register_expectation_http_request aims to insert expectation data into two tables: hub_expectation and sat_expectation_http_request. It performs the following steps:

* Input Parameters:

* expectation_id: Unique identifier for the expectation.
* expectation_key: Key associated with the expectation.
* payload: JSONB data containing the request payload.
* content_type (optional): Type of content being registered.
* created_at (optional): Timestamp when the record was created.
* created_by (optional): User who created the record.
* provenance (optional): Source of the record.
* hub_upsert_behavior (optional): Boolean flag to determine upsert behavior (default is true).
*
* Default Values:
*
* v_created_at: Defaults to the current timestamp if not provided.
* v_created_by: Defaults to the current user if not provided.
* v_provenance: Defaults to 'unknown' if not provided.
*
* Insert into hub_expectation:
*
* Attempts to insert the expectation data into the hub_expectation table.
* If a unique constraint violation occurs and hub_upsert_behavior is false, it logs the exception details by calling techbd_udi_ingress.register_issue.
* Insert into sat_expectation_http_request:

* Attempts to insert the HTTP request details into the sat_expectation_http_request table.
* If any exception occurs, it logs the exception details and associates it with a previously logged exception if available.
* Exception Handling:

* Captures various exception details such as error message, SQL state, and additional context.
* Logs the captured exception details using techbd_udi_ingress.register_issue. 
****************************************************************************/

DROP PROCEDURE IF EXISTS techbd_udi_ingress.register_expectation_http_request;
CREATE OR REPLACE PROCEDURE techbd_udi_ingress.register_expectation_http_request(IN expectation_id text, IN expectation_key text, IN payload jsonb, IN content_type text DEFAULT NULL::text, IN created_at timestamp with time zone DEFAULT NULL::timestamp with time zone, IN created_by text DEFAULT NULL::text, IN provenance text DEFAULT NULL::text, IN hub_upsert_behavior boolean DEFAULT true)
 LANGUAGE plpgsql
AS $procedure$
DECLARE
    v_error_msg TEXT;
    v_error_type TEXT;
    v_sqlstate TEXT;
    v_pg_detail TEXT;
    v_pg_hint TEXT;
    v_pg_context TEXT;
    v_created_at TIMESTAMPTZ := COALESCE(created_at, CURRENT_TIMESTAMP);
    v_created_by TEXT := COALESCE(created_by, current_user);
    v_provenance TEXT := COALESCE(provenance, 'unknown');
    v_exception_id TEXT;
BEGIN
    -- Attempt to insert into hub_expectation
    BEGIN
        INSERT INTO techbd_udi_ingress.hub_expectation (hub_expectation_id, key, created_at, created_by, provenance)
        VALUES (expectation_id, expectation_key, v_created_at, v_created_by, v_provenance);

    EXCEPTION
        WHEN unique_violation THEN
            IF NOT hub_upsert_behavior THEN
                -- Capture exception details
                GET STACKED DIAGNOSTICS
                    v_error_msg = MESSAGE_TEXT,
                    v_sqlstate = RETURNED_SQLSTATE,
                    v_pg_detail = PG_EXCEPTION_DETAIL,
                    v_pg_hint = PG_EXCEPTION_HINT,
                    v_pg_context = PG_EXCEPTION_CONTEXT;
                    v_error_type = 'SQL';

                -- Call register_issue to log the exception and get the exception ID
                v_exception_id := techbd_udi_ingress.register_issue(
                    NULL, expectation_key, v_error_msg, v_error_type, v_sqlstate, v_pg_detail, v_pg_hint, v_pg_context, v_created_by, v_provenance
                );
            END IF;
    END;

    -- Attempt to insert into sat_expectation_http_request
    BEGIN
        INSERT INTO techbd_udi_ingress.sat_expectation_http_request (
            sat_expectation_http_request_id, hub_expectation_id, content_type, payload, created_at, created_by, provenance
        )
        VALUES (
            gen_random_uuid()::text, expectation_id, content_type, payload, v_created_at, v_created_by, v_provenance);

    EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception details
            GET STACKED DIAGNOSTICS
                v_error_msg = MESSAGE_TEXT,
                v_sqlstate = RETURNED_SQLSTATE,
                v_pg_detail = PG_EXCEPTION_DETAIL,
                v_pg_hint = PG_EXCEPTION_HINT,
                v_pg_context = PG_EXCEPTION_CONTEXT;
                v_error_type= 'SQL';

            -- Log the exception, reusing the previous exception ID if it exists
            v_exception_id := techbd_udi_ingress.register_issue(
                COALESCE(v_exception_id, NULL), expectation_key, v_error_msg, v_error_type, v_sqlstate, v_pg_detail, v_pg_hint, v_pg_context, v_created_by, v_provenance
            );
    END;
END;
$procedure$
;    

/****************************************************************************************
 * View: fhir_needs_attention_details
 * 
 * Purpose:
 * - Provides a filtered view of recent FHIR interactions that encountered HTTP response errors.
 * - Focuses on records related to 'Forwarded HTTP Response Error' events within the last 7 days.
 * - This view is especially useful for monitoring and troubleshooting FHIR Bundle-related issues 
 *   and identifying tenants with recurring errors.
 * 
 * Key Fields:
 * - hub_interaction_id: Unique identifier for each interaction within the hub.
 * - sat_interaction_http_request_id: Maps to the associated HTTP request ID for tracking purposes.
 * - qe_name: Tenant identifier (in lowercase) representing the source of the interaction.
 * - created_at: Timestamp for when the interaction was recorded.
 * 
 * Notes:
 * - The view filters by specific URI patterns ('/Bundle/' and '/Bundle') to target Bundle interactions.
 * - Data is restricted to interactions within the last 7 days to focus on recent issues.
 * - Results are ordered by creation date in descending order, highlighting the most recent interactions.
 ****************************************************************************************/

 
DROP VIEW IF EXISTS techbd_udi_ingress.fhir_needs_attention_details CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.fhir_needs_attention_details
AS select
	sihr.hub_interaction_id,
	sihr.sat_interaction_fhir_request_id as sat_interaction_http_request_id,
	sihr.tenant_id_lower as qe_name,
	sihr.created_at
from
	techbd_udi_ingress.sat_interaction_fhir_request sihr
where
	sihr.nature = 'Forwarded HTTP Response Error'
	and (uri = any (array['/Bundle/',
	'/Bundle']))
	and created_at >= (CURRENT_DATE - '7 days'::interval)
order by
	created_at desc;

/****************************************************************************************
 * Summary of recent FHIR interactions for key tenants.
 * 
 * This view provides a breakdown of HTTP interactions for the past 7 days, grouped by
 * tenant (qe_name), focusing on:
 * - Total submissions by tenant, filtered by specific URI patterns.
 * - Submissions forwarded to Shinny Datalake by tenant.
 * 
 * Purpose:
 * - To monitor recent FHIR-related activity across specific tenants.
 * - Enables tracking of each tenant's submissions, including those forwarded to
 *   the Shinny Datalake.
 * - Useful for identifying trends and ensuring consistent data ingestion.
 ****************************************************************************************/

DROP VIEW IF EXISTS techbd_udi_ingress.fhir_needs_attention_dashbaord CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.fhir_needs_attention_dashbaord AS     
WITH combined_counts AS (
         SELECT sihr.tenant_id_lower AS qe_name,
            count(
                CASE
                    WHEN sihr.nature = 'Original FHIR Payload'::text THEN 1
                    ELSE NULL::integer
                END) AS qe_total_submissions,
            count(
                CASE
                    WHEN sihr.nature = 'Forwarded HTTP Response'::text THEN 1
                    ELSE NULL::integer
                END) AS shinny_datalake_submissions,
            max(sihr.created_at) AS recently_created_at
           FROM techbd_udi_ingress.sat_interaction_fhir_request sihr
          WHERE sihr.created_at >= (CURRENT_DATE - '7 days'::interval) AND (sihr.uri = ANY (ARRAY['/Bundle/', '/Bundle','/flatfile/csv/Bundle', '/flatfile/csv/Bundle/', '/ccda/Bundle', '/ccda/Bundle/', '/hl7v2/Bundle', '/hl7v2/Bundle/'])) AND (sihr.tenant_id_lower = ANY (ARRAY['healthelink'::text, 'healtheconn'::text, 'healthix'::text, 'grrhio'::text, 'hixny'::text]))
          GROUP BY sihr.tenant_id_lower
        )
 SELECT  
    sum(
        CASE
            WHEN qe_name = 'healthelink'::text THEN qe_total_submissions
            ELSE 0::bigint
        END) AS healthelink_total_submissions,
    sum(
        CASE
            WHEN qe_name = 'healtheconn'::text THEN qe_total_submissions
            ELSE 0::bigint
        END) AS healtheconnections_total_submissions,
    sum(
        CASE
            WHEN qe_name = 'healthix'::text THEN qe_total_submissions
            ELSE 0::bigint
        END) AS healthix_total_submissions,
    sum(
        CASE
            WHEN qe_name = 'grrhio'::text THEN qe_total_submissions
            ELSE 0::bigint
        END) AS grrhio_total_submissions,
    sum(
        CASE
            WHEN qe_name = 'hixny'::text THEN qe_total_submissions
            ELSE 0::bigint
        END) AS hixny_total_submissions,
    sum(
        CASE
            WHEN qe_name = 'healthelink'::text THEN shinny_datalake_submissions
            ELSE 0::bigint
        END) AS healthelink_shinny_datalake_submissions,
    sum(
        CASE
            WHEN qe_name = 'healtheconn'::text THEN shinny_datalake_submissions
            ELSE 0::bigint
        END) AS healtheconnections_shinny_datalake_submissions,
    sum(
        CASE
            WHEN qe_name = 'healthix'::text THEN shinny_datalake_submissions
            ELSE 0::bigint
        END) AS healthix_shinny_datalake_submissions,
    sum(
        CASE
            WHEN qe_name = 'grrhio'::text THEN shinny_datalake_submissions
            ELSE 0::bigint
        END) AS grrhio_shinny_datalake_submissions,
    sum(
        CASE
            WHEN qe_name = 'hixny'::text THEN shinny_datalake_submissions
            ELSE 0::bigint
        END) AS hixny_shinny_datalake_submissions
   FROM combined_counts
  WHERE qe_name = ANY (ARRAY['healthelink'::text, 'healtheconn'::text, 'healthix'::text, 'grrhio'::text, 'hixny'::text]);

 
-- techbd_udi_ingress.interaction_fhir_request_failed_needs_attention source
DROP VIEW IF EXISTS techbd_udi_ingress.interaction_http_request_failed_needs_attention CASCADE;
DROP VIEW IF EXISTS techbd_udi_ingress.interaction_http_fhir_request_needs_attention CASCADE; 


/**
 * View to summarize HTTP request interactions with client IP and user agent filtering.
 * 
 * This view aggregates and summarizes recent HTTP request interactions within the system,
 * focusing on the past 7 days. It extracts key details such as tenant ID, client IP address, 
 * user agent, and the total count of requests per combination of these elements. 
 * This summary is particularly useful for identifying patterns of interaction across 
 * different tenants, IP addresses, and user agents, allowing for efficient monitoring and analysis.
 * 
 * Key components:
 * - tenant_id: Identifies the tenant responsible for each HTTP request.
 * - client_ip_address: Extracted from the 'x-forwarded-for' header within the request's payload,
 *   representing the IP address from which the request originated.
 * - user_agent: Captures the user agent string from the request headers, identifying the 
 *   client software or device making the request.
 * - request_count: The total number of requests for each unique combination of tenant ID, 
 *   client IP address, and user agent, providing insight into interaction volume.
 * 
 * CTE (Common Table Expression):
 * - The CTE (`cte`) is used to extract the client IP address from the 'x-forwarded-for' header 
 *   if available, falling back to NULL if not. It also filters out requests with a user agent of 
 *   'httpyac' to ensure that only relevant requests are included in the summary.
 * 
 * Filtering:
 * - URI: The view includes only interactions with the URIs '/Bundle' or '/Bundle/'.
 * - Nature: It specifically targets interactions categorized as 'Original FHIR Payload'.
 * - User Agent: Requests with a user agent of 'httpyac' are excluded to focus on interactions from other sources.
 * - Time Frame: The view is restricted to interactions that occurred within the last 7 days, ensuring the summary is recent and relevant.
 * 
 * Grouping and Ordering:
 * - The results are grouped by tenant_id, client_ip_address, and user_agent to provide a breakdown 
 *   of request counts for each combination.
 * - The final output is ordered by tenant_id, client_ip_address, and user_agent to facilitate 
 *   easy identification of patterns across tenants and clients.
 * 
 * Utility:
 * - This view is useful for system administrators and security analysts who need to monitor and 
 *   analyze HTTP request patterns. By summarizing interaction data based on key factors like 
 *   client IP and user agent, it helps in identifying trends, potential issues, or unauthorized access attempts.
 * 
 * @return VIEW - A summarized view of HTTP request interactions with counts of requests per tenant ID, client IP address, and user agent.
 */


DROP VIEW IF EXISTS techbd_udi_ingress.interaction_http_request_summary CASCADE; 
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_http_request_summary
AS 
WITH cte AS (
    SELECT tenant_id_lower,
           client_ip_address,
           user_agent
    FROM techbd_udi_ingress.sat_interaction_fhir_request
    WHERE nature = 'Original FHIR Payload'
      AND uri != '/api/expect/fhir/bundle'
      AND user_agent <> 'httpyac'
      AND created_at >= CURRENT_DATE - INTERVAL '7 days' 
)
SELECT tenant_id_lower,
       client_ip_address,
       user_agent,
       COUNT(*) AS request_count
FROM cte
GROUP BY tenant_id_lower, client_ip_address, user_agent
ORDER BY tenant_id_lower, client_ip_address, user_agent;
  

/**
 * View to summarize detailed HTTP request interactions including tenant, client IP, and user agent.
 *
 * This view provides a detailed summary of HTTP request interactions over the past 7 days,
 * capturing essential information such as tenant ID, client IP address, user agent, 
 * hub interaction ID, and the request creation timestamp. This detailed view is valuable 
 * for tracking individual requests and their origins, which can be used for troubleshooting, 
 * auditing, and monitoring purposes.
 * 
 * Key components:
 * - tenant_id: Identifies the tenant responsible for each HTTP request.
 * - client_ip_address: Extracted from the 'x-forwarded-for' header within the request's payload,
 *   representing the IP address from which the request originated. If the header is not present, 
 *   this field will be NULL.
 * - user_agent: Captures the user agent string from the request headers, identifying the 
 *   client software or device making the request.
 * - hub_interaction_id: A unique identifier for each hub interaction, allowing for precise 
 *   tracking and correlation of requests.
 * - request_created_at: The timestamp of when the request was created, providing chronological 
 *   context for the interactions.
 *
 * CTE (Common Table Expression):
 * - The CTE (`cte`) is used to extract and filter the relevant data from the 
 *   `interaction_http_request_mat` table. It includes logic to extract the client IP 
 *   address from the 'x-forwarded-for' header, filter out requests with a user agent 
 *   of 'httpyac', and limit the data to interactions that occurred in the past 7 days.
 * 
 * Filtering:
 * - URI: The view includes only interactions with the URIs '/Bundle' or '/Bundle/'.
 * - Nature: It specifically targets interactions categorized as 
 *   'Original FHIR Payload'.
 * - User Agent: Requests with a user agent of 'httpyac' are excluded to focus on interactions 
 *   from other sources.
 * - Time Frame: The view is restricted to interactions that occurred within the last 7 days, 
 *   ensuring the summary is recent and relevant.
 * 
 * Ordering:
 * - The results are ordered by the `request_created_at` field in descending order, 
 *   ensuring that the most recent interactions are displayed first.
 *
 * Utility:
 * - This view is particularly useful for administrators and security analysts who need to 
 *   monitor detailed HTTP request interactions. By capturing and organizing data based on 
 *   key factors like tenant ID, client IP, and user agent, it helps in identifying patterns, 
 *   diagnosing issues, and ensuring compliance with security and operational standards.
 *
 * @return VIEW - A detailed summary of HTTP request interactions including tenant, client IP, user agent, hub interaction ID, and request timestamp.
 */


DROP VIEW IF EXISTS techbd_udi_ingress.interaction_http_request_summary_details CASCADE; 
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_http_request_summary_details
AS 
WITH cte AS (
    SELECT tenant_id_lower,
           client_ip_address,
           user_agent,
          hub_interaction_id,  
          created_at         
    FROM techbd_udi_ingress.sat_interaction_fhir_request
    WHERE nature = 'Original FHIR Payload'
      AND uri != '/api/expect/fhir/bundle'
      AND user_agent <> 'httpyac'
      AND created_at >= CURRENT_DATE - INTERVAL '7 days'  
)
SELECT tenant_id_lower,
       client_ip_address,
       user_agent,
       hub_interaction_id,
       created_at as request_created_at
FROM cte;



/*******************************************************************************************************************************
 * Function to upsert user details related to an HTTP interaction, ensuring the associated hub interaction record exists and 
 * handling exceptions by logging them when necessary.
 *
 * @param hub_interaction_id TEXT - The unique ID for the associated hub interaction.
 * @param hub_interaction_key TEXT - A key to identify the associated hub interaction.
 * @param user_id TEXT - The ID of the user involved in the interaction.
 * @param user_name TEXT - The name of the user involved in the interaction.
 * @param user_session TEXT - The session identifier of the user involved in the interaction.
 * @param user_role TEXT - The role of the user involved in the interaction.
 * @param tenant_id TEXT (nullable) - The tenant ID associated with the interaction. Default is NULL.
 * @param elaboration JSONB (nullable) - Additional JSON data elaborating the user interaction. Default is NULL.
 * @param created_at TIMESTAMPTZ (nullable) - The creation timestamp of the record. Defaults to the current timestamp if NULL.
 * @param created_by TEXT (nullable) - The user who created the record. Defaults to the current user if NULL.
 * @param provenance TEXT (nullable) - The provenance of the record. Defaults to 'unknown' if NULL.
 * 
 * @returns TEXT - Returns the unique ID of the newly inserted or upserted user interaction record.
 *
 * The function performs the following actions:
 * 1. Verifies the existence of the hub interaction record associated with the provided `hub_interaction_id` and `hub_interaction_key`.
 *    If the record does not exist, the function returns NULL.
 * 2. Inserts a new record into the `sat_interaction_user` table, capturing user details along with the associated tenant and
 *    elaboration information.
 * 3. Handles any exceptions that occur during the process by logging the details of the error using the `register_issue` function.
 *    In the event of an error, the function returns NULL.
 *******************************************************************************************************************************/


DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_user_upserted(text, text, text, jsonb, text, text, text, text, text, jsonb, timestamptz, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_user_upserted(text, text, text, jsonb, text, text, text, text, text, jsonb, timestamptz, text, text, jsonb);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.sat_interaction_user_upserted(hub_interaction_id text, hub_interaction_key text, nature text, payload jsonb, user_id text, user_name text, user_session text, user_role text, tenant_id text DEFAULT NULL::text, elaboration jsonb DEFAULT NULL::jsonb, created_at timestamp with time zone DEFAULT NULL::timestamp with time zone, created_by text DEFAULT NULL::text, provenance text DEFAULT NULL::text, p_additional_details jsonb DEFAULT NULL::jsonb)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_error_msg 					TEXT;
    v_error_type 					TEXT;
    v_sqlstate 						TEXT;
    v_pg_detail 					TEXT;
    v_pg_hint 						TEXT;
    v_pg_context 					TEXT;
    v_created_at 					TIMESTAMPTZ := COALESCE(created_at, CURRENT_TIMESTAMP);
    v_created_by 					TEXT 		:= COALESCE(created_by, CURRENT_USER);
    v_provenance 					TEXT 		:= COALESCE(provenance, 'user');
    v_exception_id 					TEXT 		:= gen_random_uuid()::text;
   	v_hub_interaction_key 			TEXT;
   	v_return						TEXT;
	v_return_status					BOOLEAN 	:= false;
	v_is_exception					BOOLEAN 	:= false;
   	v_client_ip_address				TEXT 		:= NULL;
   	v_client_ip_address_forwarded	TEXT 		:= NULL;
   	v_user_agent		    		TEXT;
   	v_intr_start_time 				TIMESTAMPTZ := NULL;
   	v_intr_finish_time 				TIMESTAMPTZ := NULL;
	v_elaboration 					JSONB 		:= NULL;
	v_elaboration_steps 			JSONB 		:= '[]'::jsonb;
	v_elaboration_steps_text 		TEXT[] 		:= ARRAY[]::text[]; 
    v_elaboration_steps_jsonb 		JSONB 		:= '[]'::jsonb;
	v_user_id 						TEXT  		:= COALESCE(user_id, v_created_by);
	v_techbd_version_number			TEXT  		:= NULL;

BEGIN
	BEGIN
		v_elaboration_steps_text := array_append(v_elaboration_steps_text, '0: In sat_interaction_user_upserted function');
	
	    -- Verify hub_interaction details
	   	SELECT t."key" INTO v_hub_interaction_key
	  	FROM techbd_udi_ingress.hub_interaction t
	  	WHERE t.hub_interaction_id = sat_interaction_user_upserted.hub_interaction_id 
	  	AND t."key" = sat_interaction_user_upserted.hub_interaction_key;
	
	  	IF(v_hub_interaction_key IS NULL) THEN
	  		v_return_status := true;
			v_return := NULL;
			v_error_msg := 'hub_interaction_id is not in the hub_interaction table.';	
	  	END IF;
	
		IF NOT v_return_status THEN
			IF p_additional_details IS NOT NULL THEN
				v_user_agent 			:= p_additional_details->'request'->>'User-Agent';    
			    v_client_ip_address 	:= p_additional_details->'request'->>'remoteAddr';
				v_intr_start_time 		:= p_additional_details->'request'->>'X-Observability-Metric-Interaction-Start-Time';
				v_intr_finish_time 		:= p_additional_details->'request'->>'X-Observability-Metric-Interaction-Finish-Time';
				v_techbd_version_number	:= p_additional_details->>'version';
			END IF;
		
		  	-- Extract Observability Metrics Start and Finish Time
			IF v_intr_start_time IS NULL THEN
				SELECT
				MAX(CASE WHEN header->>'name' = 'X-Observability-Metric-Interaction-Start-Time' THEN header->>'value' ELSE NULL END)
				INTO v_intr_start_time
				FROM jsonb_array_elements(sat_interaction_user_upserted.payload->'response'->'headers') AS header;
			END IF;
			
			IF v_intr_finish_time IS NULL THEN
				SELECT
				MAX(CASE WHEN header->>'name' = 'X-Observability-Metric-Interaction-Finish-Time' THEN header->>'value' ELSE NULL END) 
				INTO v_intr_finish_time
				FROM jsonb_array_elements(sat_interaction_user_upserted.payload->'response'->'headers') AS header;
			END IF;
		
			-- Extract Client IP Address
			IF v_client_ip_address IS NULL THEN
		   		v_client_ip_address := sat_interaction_user_upserted.payload -> 'request'->>'clientIpAddress';
			END IF;
		
			-- Extract Client IP Address Forwarded
		    SELECT h.value ->> 'value'::text INTO v_client_ip_address_forwarded
		    FROM jsonb_array_elements((sat_interaction_user_upserted.payload -> 'request'::text) -> 'headers'::text) h(value)
		    WHERE (h.value ->> 'name'::text) = 'x-forwarded-for'::text;
		   
		   -- Extract User Agent
			IF v_user_agent IS NULL THEN
		   		v_user_agent := sat_interaction_user_upserted.payload -> 'request'->>'userAgent';
			END IF;
			
		    INSERT INTO techbd_udi_ingress.sat_interaction_user (
				sat_interaction_user_id,
				hub_interaction_id,
				uri,
				nature,
				tenant_id,
				user_id,
				user_name,
				user_session,
				user_session_hash,
				user_role,
				client_ip_address,
				user_agent,
				elaboration,
				created_at,
				created_by,
				provenance,
				interaction_start_time,
				interaction_end_time,
				techbd_version_number
		    )
		    VALUES (
			    gen_random_uuid()::text, 
			    sat_interaction_user_upserted.hub_interaction_id,
			    v_hub_interaction_key,
			    sat_interaction_user_upserted.nature,
			    sat_interaction_user_upserted.tenant_id,
			    sat_interaction_user_upserted.user_id,
			    sat_interaction_user_upserted.user_name,
			    sat_interaction_user_upserted.user_session,
				md5(sat_interaction_user_upserted.user_session),
			    sat_interaction_user_upserted.user_role,        
			    COALESCE(v_client_ip_address_forwarded,v_client_ip_address),        
			    v_user_agent,      
			    sat_interaction_user_upserted.elaboration, 
			    v_created_at, 
			    v_created_by, 
			    v_provenance,
			    v_intr_start_time,
			    v_intr_finish_time,
				v_techbd_version_number
		    ) RETURNING sat_interaction_user_id INTO v_return;
		
			v_elaboration_steps_text := array_append(v_elaboration_steps_text, '1: Inserted into sat_interaction_user');
		END IF;
	EXCEPTION
		WHEN OTHERS THEN
		    -- Capture exception details
		    GET STACKED DIAGNOSTICS
		        v_error_msg = MESSAGE_TEXT,
		        v_sqlstate = RETURNED_SQLSTATE,
		        v_pg_detail = PG_EXCEPTION_DETAIL,
		        v_pg_hint = PG_EXCEPTION_HINT,
		        v_pg_context = PG_EXCEPTION_CONTEXT;
		        v_error_type = 'SQL';
			    
		    v_is_exception := true;				
			v_return_status := true;
			v_return := NULL;
	END;
	
	-- Save the log details into table
	v_elaboration := jsonb_build_object(
								'interaction_id', hub_interaction_id,
								'interaction_key', hub_interaction_key,
								'nature', nature,	
								'tenant_id', tenant_id,							
								'p_additional_details', p_additional_details
								);
	-- Convert steps array to JSONB and attach
    IF array_length(v_elaboration_steps_text, 1) IS NOT NULL THEN
        v_elaboration_steps_jsonb := to_jsonb(v_elaboration_steps_text);
        v_elaboration := v_elaboration || jsonb_build_object('steps', v_elaboration_steps_jsonb);
    END IF;

    -- Attach error message if present
    IF v_error_msg IS NOT NULL THEN
        v_elaboration := v_elaboration || jsonb_build_object('error_message', v_error_msg);
    END IF;

    -- Register diagnostic log
    CALL techbd_udi_ingress.register_diagnostic_log(v_exception_id, hub_interaction_key, v_created_by, v_provenance, 'info'::text, 
       																'sat_interaction_user_upserted function Logs'::text, v_created_by, 'success'::text, NULL::text, 0, v_elaboration, tenant_id, hub_interaction_id);

	IF v_is_exception = true THEN    
        -- Log the exception, reusing the previous exception ID if it exists
        v_elaboration := v_elaboration || jsonb_build_array(jsonb_build_object('error_message', v_error_msg));
        v_exception_id := techbd_udi_ingress.register_issue(
            v_exception_id, hub_interaction_key, v_error_msg, v_error_type, v_sqlstate, v_pg_detail, v_pg_hint, v_pg_context, v_created_by, v_provenance, v_elaboration, tenant_id, hub_interaction_id
        );
	END IF;
   	RETURN v_return;
END;
$function$
;

DROP VIEW IF EXISTS techbd_udi_ingress.interaction_user_details CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_user_details
AS SELECT created_at,
    hub_interaction_id,
    uri,
    sat_interaction_user_id,
    user_id,
    user_name,
    user_session,
    user_session_hash,
    user_role,
    client_ip_address,
    user_agent
   FROM techbd_udi_ingress.sat_interaction_user sai;



/*******************************************************************************************
This view lists distinct user sessions by grouping on `user_session` and `user_session_hash`.
It extracts the earliest `created_at` timestamp along with the first available user metadata 
(user ID, name, and role) for each session. The output is ordered by session creation time 
in descending order to prioritize recent sessions.
*******************************************************************************************/


DROP VIEW IF EXISTS techbd_udi_ingress.interaction_user_list CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_user_list
AS SELECT min(created_at) AS created_at,
    min(user_id) AS user_id,
    min(user_name) AS user_name,
    min(user_role) AS user_role,
    user_session,
    user_session_hash
   FROM techbd_udi_ingress.interaction_user_details
  WHERE user_session IS NOT NULL
  GROUP BY user_session, user_session_hash;

/*******************************************************************************************************************************
Procedure to handle the insertion or update of FHIR interaction data, managing default values and error handling.

@param hub_interaction_id TEXT - The unique identifier for the interaction in the hub.
@param tenant_id TEXT - The identifier for the tenant or client.
@param uri TEXT - The URI associated with the FHIR request.
@param nature TEXT - The nature or type of the HTTP interaction.
@param from_state TEXT - The state before the interaction occurred.
@param to_state TEXT - The state after the interaction.
@param payload JSONB - The JSON payload containing the details of the interaction.
@param state_transition_reason TEXT (nullable) - The reason for the state transition. Default is NULL.
@param created_at TIMESTAMP WITH TIME ZONE (nullable) - The timestamp of record creation. Defaults to the current timestamp if NULL.
@param created_by TEXT (nullable) - The user who created the record. Defaults to the current user if NULL.
@param provenance TEXT (nullable) - The provenance or source of the record. Defaults to 'unknown' if NULL.
@param elaboration JSONB (nullable) - Additional elaborative JSON data related to the interaction. Default is NULL.
@returns TEXT - A textual representation of the result of the operation, typically used to convey success or error messages. 
 *******************************************************************************************************************************/
DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_fhir_request_upserted(text, text, text, text, text, text, jsonb, text, timestamptz, text, text, jsonb);
DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_fhir_request_upserted(text, text, text, text, text, text, jsonb, text, timestamptz, text, text, jsonb, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_fhir_request_upserted(text, text, text, text, text, text, jsonb, text, timestamptz, text, text, jsonb, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_fhir_request_upserted(text, text, text, text, text, text, jsonb, text, timestamptz, text, text, jsonb, text, text, text, jsonb);
DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_fhir_request_upserted(text, text, text, text, text, text, jsonb, text, timestamptz, text, text, jsonb, text, text, text, jsonb, text);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.sat_interaction_fhir_request_upserted(
	p_hub_interaction_id text, 
	p_tenant_id text, 
	p_uri text, 
	p_nature text, 
	p_from_state text, 
	p_to_state text, 
	p_payload jsonb, 
	p_state_transition_reason text DEFAULT NULL::text, 
	p_created_at timestamp with time zone DEFAULT NULL::timestamp with time zone, 
	p_created_by text DEFAULT NULL::text, 
	p_provenance text DEFAULT NULL::text, 
	p_elaboration jsonb DEFAULT NULL::jsonb, 
	p_source_type text DEFAULT NULL::text, 
	p_source_hub_interaction_id text DEFAULT NULL::text, 
	p_group_hub_interaction_id text DEFAULT NULL::text,
	p_additional_details jsonb DEFAULT NULL::jsonb,
	p_techbd_version_number text DEFAULT NULL::text
)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    --source_type : fhir/hl7/ccda/csv
    --source_hub_interaction_id : first interaction_id for the registration process, 'zip_file_hub_interaction_id' in case of csv.

    -- Declare variables to hold error and extracted data
    v_error_msg                     TEXT;
    v_error_type                    TEXT;
    v_sqlstate                      TEXT;
    v_pg_detail                     TEXT;
    v_pg_hint                       TEXT;
    v_pg_context                    TEXT;
    v_created_at                    TIMESTAMPTZ := COALESCE(p_created_at, CURRENT_TIMESTAMP);  -- Ensure created_at is not null
    v_created_by                    TEXT        := COALESCE(p_created_by, CURRENT_USER);  -- Default created_by to the current user if not provided
    v_provenance                    TEXT        := COALESCE(p_provenance, 'unknown');  -- Set default provenance
    v_exception_id 					TEXT		:= gen_random_uuid()::text;
    
    -- Additional variables for extracting and holding data from the payload JSONB
    v_client_ip_address             TEXT    := NULL;
    v_client_ip_address_forwarded   TEXT    := NULL;
    v_user_agent                    TEXT    := NULL;
    v_outbound_http_message         TEXT    := NULL;
    v_error_message                 TEXT    := NULL;
    v_issues_count                  INTEGER := 0;
    v_passed                        BOOLEAN := NULL;
    v_bundle_id                     TEXT    := NULL;
    v_bundle_session_id             TEXT    := NULL;
    v_bundle_last_updated           TEXT    := NULL;
    v_organization_id               TEXT    := NULL;
    v_organization_name             TEXT    := NULL;
    v_patient_id                    TEXT    := NULL;
    v_patient_mrn                   TEXT    := NULL;
    v_patient_mrn_source_system     TEXT    := NULL;
    v_medicaid_cin                  TEXT    := NULL;
    v_ssn                           TEXT    := NULL;
    v_resource_type_set             TEXT    := NULL;   
    v_validation_initiated_at       TEXT    := NULL;
    v_validation_completed_at       TEXT    := NULL;   
    v_validation_engine             TEXT    := NULL;
    v_ig_version                    TEXT    := NULL;
    v_profile_url                   TEXT    := NULL;
    v_interaction_start_time        TEXT    := NULL;
    v_interaction_end_time          TEXT    := NULL;
    v_duration_millisecs            TEXT    := NULL;
    v_response_status               TEXT    := NULL;
    v_return                        TEXT    := NULL;
    v_techbd_disposition_action     TEXT    := NULL;
    v_elaboration_steps             JSONB   := '[]'::jsonb;
	v_elaboration_steps_text 		TEXT[] 	:= ARRAY[]::text[]; 
    v_elaboration_steps_jsonb 		JSONB 	:= '[]'::jsonb;
    v_elaboration                   JSONB   := NULL;
    v_diagnostic_id                 TEXT    := gen_random_uuid()::text;
    v_is_bundle_valid               BOOLEAN := false;
    v_bundle_type                   TEXT    := NULL;
    v_return_status                 BOOLEAN := false;
    v_is_exception                  BOOLEAN := false;

BEGIN
    BEGIN    
    v_elaboration_steps_text := array_append(v_elaboration_steps_text, '0: In sat_interaction_fhir_request_upserted function');
	
    IF NOT EXISTS(SELECT 1 FROM techbd_udi_ingress.hub_interaction t
        WHERE t.hub_interaction_id = p_hub_interaction_id) THEN
            v_return_status := true; 
            v_return := NULL;
			v_error_msg := 'hub_interaction_id is not in the hub_interaction table.';
    END IF;
  
	IF (NOT v_return_status) THEN
    IF(p_nature <> 'Original FHIR Payload') THEN
        SELECT 
        fhir_req.client_ip_address,         fhir_req.user_agent,                fhir_req.bundle_id, 
        fhir_req.bundle_last_updated,       fhir_req.organization_id,           fhir_req.organization_name, 
        fhir_req.patient_id,                fhir_req.patient_mrn,               fhir_req.resource_type_set, 
        fhir_req.validation_initiated_at,   fhir_req.validation_completed_at,   fhir_req.validation_engine,
        fhir_req.ig_version,                fhir_req.profile_url,               fhir_req.medicaid_cin,
        fhir_req.interaction_start_time,    fhir_req.interaction_end_time,      fhir_req.bundle_session_id,
        fhir_req.patient_mrn_source_system, fhir_req.is_bundle_valid,           fhir_req.bundle_type
        INTO        
        v_client_ip_address,                v_user_agent,                       v_bundle_id,
        v_bundle_last_updated,              v_organization_id,                  v_organization_name,
        v_patient_id,                       v_patient_mrn,                      v_resource_type_set,
        v_validation_initiated_at,          v_validation_completed_at,          v_validation_engine,
        v_ig_version,                       v_profile_url,                      v_medicaid_cin,
        v_interaction_start_time,           v_interaction_end_time,             v_bundle_session_id,
        v_patient_mrn_source_system,        v_is_bundle_valid,                  v_bundle_type   
        FROM
        techbd_udi_ingress.sat_interaction_fhir_request fhir_req
        WHERE 1 = 1
        AND fhir_req.hub_interaction_id = p_hub_interaction_id
        AND fhir_req.nature = 'Original FHIR Payload';  
    ELSE
        
        v_user_agent                := p_additional_details->'request'->>'User-Agent';    
        v_client_ip_address         := p_additional_details->'request'->>'remoteAddr';  
        v_interaction_start_time 	:= p_additional_details->'request'->>'X-Observability-Metric-Interaction-Start-Time';
        v_interaction_end_time 		:= v_created_at;
                 
		WITH patient_resource AS (
		    SELECT jsonb_path_query_first(
		               p_payload,
		               '$.entry[*].resource?(@.resourceType == "Patient")'
		           ) AS pr
		)
		SELECT
			jsonb_path_query_first(pr, '$.id') #>> '{}',
		    jsonb_path_query_first(pr, '$.identifier[*]?(@.type.coding[0].code == "MR").value') #>> '{}',		
		    jsonb_path_query_first(pr, '$.identifier[*]?(@.type.coding[0].code == "MR").system') #>> '{}',
			jsonb_path_query_first(pr, '$.identifier[*]?(@.type.coding[0].code == "MA").value') #>> '{}',		
		    regexp_replace(
		        COALESCE(
		            jsonb_path_query_first(
		                pr,
		                '$.identifier[*]?(@.type.coding[0].code == "MR").assigner.reference'
		            ) #>> '{}',
		            ''
		        ),
		        '^Organization/', ''
		    )
		INTO v_patient_id,
			 v_patient_mrn,
		     v_patient_mrn_source_system,		     
			 v_medicaid_cin,
			 v_organization_id
		FROM patient_resource;

		IF v_organization_id IS NOT NULL AND v_organization_id <> '' THEN
	        SELECT COALESCE(jsonb_path_query_first(
	                   p_payload,
	                   format(
	                       '$.entry[*].resource?(@.resourceType == "Organization" && @.id == "%s").name',
	                       v_organization_id
	                   )::jsonpath
	               ) #>> '{}', '')
	        INTO v_organization_name;
	    ELSE
	        v_organization_name := NULL;
	    END IF;
  
		IF p_payload->>'resourceType' = 'Bundle' THEN
		    v_bundle_id := p_payload->>'id';
		    v_bundle_last_updated := p_payload->'meta'->>'lastUpdated';
		END IF;   
    
        SELECT STRING_AGG(DISTINCT entry->'resource'->>'resourceType', ', ') 
        INTO v_resource_type_set
        FROM jsonb_array_elements(p_payload->'entry') AS entry
        WHERE entry ? 'resource';        

		v_bundle_type := techbd_udi_ingress.get_fhir_bundle_type(p_payload);
    
    END IF; /*End of : Original FHIR Payload*/
    
    -- Save the action from the OperationOutcome->techByDesignDisposition
    IF p_nature = 'techByDesignDisposition' THEN 
        v_issues_count              := COALESCE(jsonb_array_length(((((p_payload -> 'OperationOutcome'::text) -> 'validationResults'::text) -> 0) -> 'operationOutcome'::text) -> 'issue'::text), 0);            
        v_outbound_http_message     := substring((p_payload -> 'OperationOutcome'::text) ->> 'outboundHttpMessage'::text, 'POST[^\n]+'::text);    
        v_bundle_session_id         := p_payload->'OperationOutcome'->>'bundleSessionId';                     
        v_validation_engine         := (p_payload->'OperationOutcome'->'validationResults'->0->'observability'->>'name');    
        v_ig_version                := (p_payload->'OperationOutcome'->'validationResults'->0->>'igVersion');        
        v_profile_url               := (p_payload->'OperationOutcome'->'validationResults'->0->>'profileUrl');      
        v_is_bundle_valid           := (p_payload->'OperationOutcome'->'validationResults'->0->>'valid')::BOOLEAN;     
        v_validation_initiated_at   := to_timestamp((p_payload->'OperationOutcome'->'validationResults'->0->>'initiatedAt')::NUMERIC);
        v_validation_completed_at   := to_timestamp((p_payload->'OperationOutcome'->'validationResults'->0->>'completedAt')::NUMERIC);
        
        v_techbd_disposition_action := p_payload->'OperationOutcome'->'techByDesignDisposition'->0->>'action';
        IF v_techbd_disposition_action = 'discard' THEN
            v_interaction_end_time  := v_created_at;
            v_issues_count          := COALESCE(jsonb_array_length((((p_payload -> 'OperationOutcome'::text) -> 'validationResults'::text) -> 0) -> 'issues'::text), 0);
            v_error_message			:= p_payload->'OperationOutcome'->'techByDesignDisposition'->0->'actionPayload'->>'message';   
        END IF;
    END IF;

    IF (p_nature IN('Forwarded HTTP Response Error', 'Forwarded HTTP Response', 'Forward HTTP Request', 'Forwarded HTTP Request Replay', 'Forwarded HTTP Response Replay', 'Forwarded HTTP Response Replay Error')) THEN
        v_interaction_end_time := p_additional_details->'request'->>'X-Observability-Metric-Interaction-Finish-Time';

        IF(p_nature IN ('Forwarded HTTP Response Error', 'Forwarded HTTP Response Replay Error')) THEN
            v_passed := FALSE;
            --v_error_message := SUBSTRING(p_payload ->> 'error'::text FROM POSITION((':'::text) IN (p_payload ->> 'error'::text)) + 2); 
            v_error_message :=
					COALESCE(
						-- 1️. responseBody.message (if responseBody exists and is valid JSON)
					    NULLIF(
					        CASE
					            WHEN p_payload ? 'responseBody'
					                 AND (p_payload ->> 'responseBody') ~ '^\s*\{'       --check if responseBody looks like JSON before casting
					            THEN (p_payload ->> 'responseBody')::jsonb ->> 'message'
					        END,
					        ''
					    ),
						-- 2️. message
					    NULLIF(p_payload ->> 'message', ''),
						-- 3️. parsed error (text after ': ')
					    CASE
					        WHEN POSITION(':' IN p_payload ->> 'error') > 0
					        THEN SUBSTRING(
					                 p_payload ->> 'error'
					                 FROM POSITION(':' IN p_payload ->> 'error') + 1
					             )
					        ELSE p_payload ->> 'error'
					    END
					);             
        END IF;
            
        IF(p_nature IN ('Forwarded HTTP Response', 'Forwarded HTTP Response Replay')) THEN 
            v_response_status := substring(p_payload->> 'scoredcardResponse' FROM 'TestResult:\s*(\w+)');                   
            IF v_response_status IS NOT NULL AND v_response_status = 'PASS' THEN
                v_passed := TRUE;
            ELSE
                IF v_error_message IS NULL THEN 
                    v_error_message := p_payload->> 'insertScreeningsResponse';
                    SELECT INTO v_issues_count (regexp_matches(v_error_message, 'An unexpected error occurred: (\d+)'))[1]::INTEGER;
                ELSE                    
                    v_error_message := substring(p_payload->> 'scoredcardResponse' FROM 'Exception:\s*(.*)$');
                    SELECT INTO v_issues_count (regexp_matches(v_error_message, 'An unexpected validation error occurred parsing FHIR: (\d+)'))[1]::INTEGER;
                END IF;             
                v_passed := FALSE;
            END IF;
        END IF;     
            
    END IF; /*End of: Forwarded HTTP Response Error. Forwarded HTTP Response*/
    
    IF NOT v_return_status AND v_interaction_end_time IS NULL AND POSITION('$validate' IN p_uri) > 0 THEN
        v_interaction_end_time := v_created_at;
    END IF;

    BEGIN
        v_bundle_last_updated := v_bundle_last_updated::TIMESTAMPTZ;
    EXCEPTION
        WHEN others THEN v_bundle_last_updated := NULL;  -- Set to NULL if it's an invalid timestamp
    END;

		IF ((p_nature = 'Forwarded HTTP Response Replay Error' OR p_nature = 'Forwarded HTTP Response Replay')
		   	AND EXISTS(SELECT 1 FROM techbd_udi_ingress.sat_interaction_fhir_request t
	        			WHERE t.hub_interaction_id = p_hub_interaction_id AND nature = 'Forwarded HTTP Response Replay Error')) THEN
				-- If p_to_state = 'COMPLETE' THEN update with nature 'Forwarded HTTP Response Replay'
				UPDATE techbd_udi_ingress.sat_interaction_fhir_request SET
	            	nature 						= CASE WHEN p_to_state = 'COMPLETE' THEN 'Forwarded HTTP Response Replay' ELSE p_nature END,
	            	payload						= p_payload,
		            client_ip_address			= COALESCE(v_client_ip_address_forwarded,v_client_ip_address),
		            user_agent					= COALESCE(v_user_agent,''),
		            from_state					= p_from_state,
		            to_state					= p_to_state,
		            state_transition_reason		= p_state_transition_reason,
		            outbound_http_message		= v_outbound_http_message,
		            error_message				= v_error_message,
		            issues_count				= COALESCE(v_issues_count,0),
		            bundle_id					= v_bundle_id,
		            bundle_session_id			= v_bundle_session_id,
		            bundle_last_updated			= COALESCE(NULLIF(v_bundle_last_updated, '')::TIMESTAMPTZ, NULL),
		            organization_id				= v_organization_id,
		            organization_name			= v_organization_name,
		            patient_id					= v_patient_id,
		            patient_mrn					= v_patient_mrn,
		            resource_type_set			= v_resource_type_set,
		            validation_initiated_at		= COALESCE(v_validation_initiated_at::TIMESTAMPTZ,NULL),
		            validation_completed_at		= COALESCE(v_validation_completed_at::TIMESTAMPTZ,NULL),
		            validation_engine			= v_validation_engine,
		            elaboration					= p_elaboration,
		            created_by					= v_created_by,
		            provenance					= p_provenance,
		            ig_version					= v_ig_version,
		            profile_url					= v_profile_url,
		            passed						= v_passed,
		            medicaid_cin				= v_medicaid_cin,
		            interaction_start_time		= COALESCE(v_interaction_start_time::TIMESTAMPTZ,NULL),
		            interaction_end_time		= COALESCE(v_interaction_end_time::TIMESTAMPTZ,NULL),
		            techbd_disposition_action	= v_techbd_disposition_action,
		            patient_mrn_source_system	= v_patient_mrn_source_system,
		            source_type					= p_source_type,
		            source_hub_interaction_id	= p_source_hub_interaction_id,
		            group_hub_interaction_id	= p_group_hub_interaction_id,
		            is_bundle_valid				= COALESCE(v_is_bundle_valid, false),
		            bundle_type					= v_bundle_type,
		            additional_details			= p_additional_details,
		            techbd_version_number		= p_techbd_version_number
				WHERE hub_interaction_id 		= p_hub_interaction_id
				AND nature = 'Forwarded HTTP Response Replay Error'
				AND tenant_id = p_tenant_id;

			v_elaboration_steps_text := array_append(v_elaboration_steps_text, '1: Updated the row in sat_interaction_fhir_request');
    	ELSE
		    -- The INSERT statement inserts a new record into the sat_interaction_fhir_request table, generating a UUID for the primary key.
		    -- Extracted and transformed values from the payload JSONB are inserted into corresponding columns.
		    -- If the operation succeeds, the generated UUID is returned.
	        INSERT INTO techbd_udi_ingress.sat_interaction_fhir_request (
	            sat_interaction_fhir_request_id,
	            hub_interaction_id,
	            tenant_id,
	            tenant_id_lower,
	            uri,
	            nature,
	            payload,
	            client_ip_address,
	            user_agent,
	            from_state,
	            to_state,
	            state_transition_reason,
	            outbound_http_message,
	            error_message,
	            issues_count,
	            bundle_id,
	            bundle_session_id,
	            bundle_last_updated,
	            organization_id,
	            organization_name,
	            patient_id,
	            patient_mrn,
	            resource_type_set,
	            validation_initiated_at,
	            validation_completed_at,
	            validation_engine,
	            elaboration,
	            created_by,
	            provenance,
	            ig_version,
	            profile_url,
	            passed,
	            medicaid_cin,
	            interaction_start_time,
	            interaction_end_time,
	            techbd_disposition_action,
	            patient_mrn_source_system,
	            source_type,
	            source_hub_interaction_id,
	            group_hub_interaction_id,
	            is_bundle_valid,
	            bundle_type,
	            additional_details,
	            techbd_version_number
	        )
	        VALUES (
	            gen_random_uuid()::TEXT,  -- Generate a unique UUID for the primary key
	            p_hub_interaction_id,
	            p_tenant_id,
	            LOWER(p_tenant_id),  -- Store the tenant ID in lowercase for consistency
	            p_uri,
	            p_nature,
	            p_payload,
	            COALESCE(v_client_ip_address_forwarded,v_client_ip_address),
	            COALESCE(v_user_agent,''),
	            p_from_state,
	            p_to_state,
	            p_state_transition_reason,
	            v_outbound_http_message,
	            v_error_message,
	            COALESCE(v_issues_count,0),
	            v_bundle_id,
	            v_bundle_session_id,
	            COALESCE(NULLIF(v_bundle_last_updated, '')::TIMESTAMPTZ, NULL),
	            v_organization_id,
	            v_organization_name,
	            v_patient_id,
	            v_patient_mrn,
	            v_resource_type_set,
	            COALESCE(v_validation_initiated_at::TIMESTAMPTZ,NULL),
	            COALESCE(v_validation_completed_at::TIMESTAMPTZ,NULL),
	            v_validation_engine,
	            p_elaboration,
	            v_created_by,
	            p_provenance,
	            v_ig_version,
	            v_profile_url,
	            v_passed,
	            v_medicaid_cin,
	            COALESCE(v_interaction_start_time::TIMESTAMPTZ,NULL),
	            COALESCE(v_interaction_end_time::TIMESTAMPTZ,NULL),
	            v_techbd_disposition_action,
	            v_patient_mrn_source_system,
	            p_source_type,
	            p_source_hub_interaction_id,
	            p_group_hub_interaction_id,
	            COALESCE(v_is_bundle_valid, false),
	            v_bundle_type,
	            p_additional_details,
	            p_techbd_version_number
	        ) RETURNING sat_interaction_fhir_request_id INTO v_return;  -- Return the generated UUID
	
	        v_elaboration_steps_text := array_append(v_elaboration_steps_text, '1: Insert into sat_interaction_fhir_request');
		END IF;

    IF (v_return IS NOT NULL) THEN
        IF(p_uri='/Bundle' OR p_uri='/Bundle/' OR p_uri='/flatfile/csv/Bundle' OR p_uri='/flatfile/csv/Bundle/' OR p_uri='/ccda/Bundle' OR p_uri='/ccda/Bundle/' OR p_uri='/hl7v2/Bundle' OR p_uri='/hl7v2/Bundle/') THEN
            IF(p_nature = 'techByDesignDisposition') THEN
                CALL techbd_udi_ingress.insert_fhir_session_diagnostic(
                    p_hub_interaction_id, 
                    p_tenant_id, 
                    p_uri, 
                    v_bundle_session_id, 
                    p_payload, 
                    v_created_by, 
                    p_provenance,
                    p_elaboration,
                    v_ig_version,
                    v_validation_engine,
                    v_bundle_id,
                    v_created_at,
                    p_techbd_version_number
                );
                v_elaboration_steps := v_elaboration_steps || jsonb_build_array(jsonb_build_object('2', 'Insert into sat_interaction_fhir_session_diagnostic'));

                CALL techbd_udi_ingress.insert_validation_issues(
                    p_hub_interaction_id, 
                    p_uri, 
                    p_payload, 
                    v_created_by, 
                    p_provenance,
                    p_elaboration,
                    p_tenant_id,
                    p_techbd_version_number
                );
                v_elaboration_steps_text := array_append(v_elaboration_steps_text, '3: Insert into sat_interaction_fhir_validation_issue');
            END IF;              
            IF(p_nature = 'Original FHIR Payload') THEN
                CALL techbd_udi_ingress.insert_fhir_screening_info(
                    p_hub_interaction_id, 
                    p_tenant_id, 
                    p_uri, 
                    v_created_at, 
                    p_payload,
                    v_created_by, 
                    p_provenance,
                    p_elaboration
                );
                v_elaboration_steps_text := array_append(v_elaboration_steps_text, '4: Insert into sat_interaction_fhir_screening_info');

                CALL techbd_udi_ingress.insert_fhir_screening_patient(
                    p_hub_interaction_id, 
                    p_tenant_id, 
                    p_uri, 
                    v_created_at, 
                    p_payload,
                    v_created_by, 
                    p_provenance,
                    p_elaboration,
                    v_organization_id
                );
                v_elaboration_steps_text := array_append(v_elaboration_steps_text, '5: Insert into sat_interaction_fhir_screening_patient');

                CALL techbd_udi_ingress.insert_fhir_screening_organization(
                    p_hub_interaction_id, 
                    p_tenant_id, 
                    p_uri, 
                    v_created_at, 
                    p_payload,
                    v_created_by, 
                    p_provenance,
                    p_elaboration 
                );
                v_elaboration_steps_text := array_append(v_elaboration_steps_text, '6: Insert into sat_interaction_fhir_screening_organization');
            END IF;                  
        END IF;         
    END IF; 

    -- Save the details from the techByDesignDisposition to other natures also
    IF p_nature = 'techByDesignDisposition' THEN    
        UPDATE techbd_udi_ingress.sat_interaction_fhir_request
            SET --issues_count          = COALESCE(v_issues_count, 0),          
                outbound_http_message   = v_outbound_http_message,      
                bundle_session_id       = v_bundle_session_id,                    
                validation_engine       = v_validation_engine,   
                ig_version              = v_ig_version,
                profile_url             = v_profile_url,    
                is_bundle_valid         = COALESCE(v_is_bundle_valid, false),    
                validation_initiated_at = v_validation_initiated_at::TIMESTAMPTZ,
                validation_completed_at = v_validation_completed_at::TIMESTAMPTZ,       
                techbd_disposition_action = v_techbd_disposition_action,
                interaction_end_time    = CASE 
                                               WHEN v_techbd_disposition_action = 'discard' 
                                               THEN v_created_at 
                                               ELSE interaction_end_time 
                                            END,
                error_message			= v_error_message                            
        WHERE hub_interaction_id = p_hub_interaction_id AND nature != 'techByDesignDisposition';
        
        --Update ig_version in sat_interaction_user table
        IF(p_uri='/Bundle' OR p_uri='/Bundle/') THEN
            UPDATE techbd_udi_ingress.sat_interaction_user SET ig_version = v_ig_version WHERE hub_interaction_id = p_hub_interaction_id;
        ELSE
            UPDATE techbd_udi_ingress.sat_interaction_user SET ig_version = v_ig_version WHERE hub_interaction_id = p_source_hub_interaction_id;
        END IF;

        --Update ig_version in sat_interaction_zip_file_request table
        IF(p_uri='/flatfile/csv/Bundle' OR p_uri='/flatfile/csv/Bundle/') THEN          
            UPDATE techbd_udi_ingress.sat_interaction_zip_file_request SET ig_version = v_ig_version WHERE hub_interaction_id = p_source_hub_interaction_id;
        END IF;

        --Update ig_version in sat_interaction_ccda_request table
        IF (p_uri='/ccda/Bundle' OR p_uri='/ccda/Bundle/') THEN             
            UPDATE techbd_udi_ingress.sat_interaction_ccda_request 
                SET ig_version            = v_ig_version,
                    user_agent            = COALESCE(user_agent, v_user_agent),
                    client_ip_address     = COALESCE(client_ip_address, v_client_ip_address_forwarded, v_client_ip_address),
                    techbd_version_number = COALESCE(techbd_version_number, p_techbd_version_number)
                WHERE hub_interaction_id  = p_source_hub_interaction_id;
            
            UPDATE techbd_udi_ingress.sat_interaction_ccda_validation_errors
                SET ig_version            = v_ig_version
                WHERE hub_interaction_id  = p_source_hub_interaction_id;
        END IF;

        --Update ig_version in sat_interaction_hl7_request table
        IF (p_uri='/hl7v2/Bundle' OR p_uri='/hl7v2/Bundle/') THEN           
            UPDATE techbd_udi_ingress.sat_interaction_hl7_request 
                SET ig_version            = v_ig_version, 
                    user_agent            = COALESCE(user_agent, v_user_agent),
                    client_ip_address     = COALESCE(client_ip_address, v_client_ip_address_forwarded, v_client_ip_address),
                    techbd_version_number = COALESCE(techbd_version_number, p_techbd_version_number) 
                WHERE hub_interaction_id  = p_source_hub_interaction_id;
            
            UPDATE techbd_udi_ingress.sat_interaction_hl7_validation_errors
                SET ig_version            = v_ig_version
                WHERE hub_interaction_id  = p_source_hub_interaction_id;            
        END IF;

        v_elaboration_steps := v_elaboration_steps || jsonb_build_array(jsonb_build_object('7', 'Update ig_version, user_agent, client_ip_address and techbd_version_number to various tables'));
    END IF;    
    
    -- Save 'interaction_end_time' from the 'Forwarded HTTP Response' and 'Forwarded HTTP Response error' to other natures also  
    IF (p_nature IN('Forwarded HTTP Response Error', 'Forwarded HTTP Response', 'Forwarded HTTP Response Replay Error', 'Forwarded HTTP Response Replay')) THEN
        UPDATE techbd_udi_ingress.sat_interaction_fhir_request
            SET interaction_end_time = v_interaction_end_time::TIMESTAMPTZ
        WHERE hub_interaction_id = p_hub_interaction_id;

		IF (p_nature IN ('Forwarded HTTP Response Replay Error', 'Forwarded HTTP Response Replay')) THEN
			--Update replay_status and replay_on values in 'Forwarded HTTP Response Error'
			UPDATE techbd_udi_ingress.sat_interaction_fhir_request
	            SET replay_status = CASE WHEN p_to_state = 'COMPLETE' THEN 'True' ELSE 'False' END,
					replay_on  = v_created_at
	        WHERE hub_interaction_id = p_hub_interaction_id
			AND nature = 'Forwarded HTTP Response Error';
		END IF;
    END IF;
	END IF;
    
EXCEPTION
   WHEN OTHERS THEN
        -- Capture exception details
        GET STACKED DIAGNOSTICS
            v_error_msg = MESSAGE_TEXT,
            v_sqlstate = RETURNED_SQLSTATE,
            v_pg_detail = PG_EXCEPTION_DETAIL,
            v_pg_hint = PG_EXCEPTION_HINT,
            v_pg_context = PG_EXCEPTION_CONTEXT;
            v_error_type = 'SQL';
            
            v_is_exception := true;				
			v_return_status := true;
			v_return := NULL;
END;
        
        v_elaboration := jsonb_build_object(
                        'interaction_id', p_hub_interaction_id,
                        'interaction_key', p_uri,
                        'nature', p_nature,
                        'tenant_id', p_tenant_id,
                        'from_state', p_from_state,
                        'to_state', p_to_state,
                        'techbd_version_number', p_techbd_version_number
                        );

    -- Convert steps array to JSONB and attach
    IF array_length(v_elaboration_steps_text, 1) IS NOT NULL THEN
        v_elaboration_steps_jsonb := to_jsonb(v_elaboration_steps_text);
        v_elaboration := v_elaboration || jsonb_build_object('steps', v_elaboration_steps_jsonb);
    END IF;

    -- Attach error message if present
    IF v_error_msg IS NOT NULL THEN
        v_elaboration := v_elaboration || jsonb_build_object('error_message', v_error_msg);
    END IF;

    -- Register diagnostic log
    CALL techbd_udi_ingress.register_diagnostic_log(v_exception_id, p_uri, v_created_by, v_provenance, 'info'::text, 
       						'sat_interaction_fhir_request_upserted function Logs'::text, v_created_by, 'success'::text, NULL::text, 0, v_elaboration, p_tenant_id, p_hub_interaction_id);

	IF v_is_exception = true THEN    
        -- Log the exception, reusing the previous exception ID if it exists
        v_elaboration := v_elaboration || jsonb_build_array(jsonb_build_object('error_message', v_error_msg));
        v_exception_id := techbd_udi_ingress.register_issue(
            v_exception_id, p_uri, v_error_msg, v_error_type, v_sqlstate, v_pg_detail, v_pg_hint, v_pg_context, v_created_by, v_provenance, v_elaboration, p_tenant_id, p_hub_interaction_id
        );
	END IF;
   	RETURN v_return;
END;
$function$;
/*******************************************************************************************
  This view provides a consolidated view of failed HTTP requests and associated interactions.
******************************************************************************************/
DROP VIEW IF EXISTS techbd_udi_ingress.interaction_http_request_forward_failure;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_http_request_forward_failure as
SELECT DISTINCT ON (hub_interaction_id)
    sat_interaction_fhir_request_id,
    hub_interaction_id AS interaction_id,
    tenant_id,
    uri,
    bundle_id,
    nature,
    created_at AS interaction_created_at,
    created_by AS interaction_created_by,
    provenance AS interaction_provenance,
    client_ip_address,
    user_agent,
    from_state,
    to_state,
    outbound_http_message,
    error_message AS error,
    issues_count,
    resource_type_set AS resource_types,
    patient_id,
    patient_mrn,
    concat_ws('-', tenant_id, organization_id, patient_mrn) AS source_mrn,
    CASE 
        WHEN source_type IN ('CSV', 'CCDA', 'HL7V2') THEN source_type
        WHEN lower(source_type) = 'fhir' THEN 'FHIR'
        ELSE 'FHIR'
    END AS source_type,
    source_hub_interaction_id,
    ig_version,
    techbd_version_number
FROM techbd_udi_ingress.sat_interaction_fhir_request as r1
WHERE 
  to_state = 'FAIL'
  AND (
        -- ✅ Failed after resubmission
        (
          nature = 'Forwarded HTTP Response Replay Error'
          AND EXISTS (
              SELECT 1 
              FROM techbd_udi_ingress.sat_interaction_fhir_request r2
              WHERE r2.hub_interaction_id = r1.hub_interaction_id
                AND r2.nature = 'Forwarded HTTP Response Error'
          )
        )
        OR
        -- ✅ Failed without resubmission
        (
          nature = 'Forwarded HTTP Response Error'
          AND NOT EXISTS (
              SELECT 1 
              FROM techbd_udi_ingress.sat_interaction_fhir_request r3
              WHERE r3.hub_interaction_id = r1.hub_interaction_id
                AND r3.nature = 'Forwarded HTTP Request Replay'
          )
        )
     )
ORDER BY hub_interaction_id, created_at DESC;


/*******************************************************************************************************************************
Function to process a set of JSON action rules on a given JSON input.

Parameters:
input_json    - The JSONB input on which the rules will be applied (JSONB)
rule_namespace- The namespace for selecting action rules (TEXT)
key           - A unique key to validate the process (TEXT)
created_by    - The user who triggered the function (optional, default is NULL) (TEXT)
provenance    - The origin of the data or process (optional, default is NULL) (TEXT)

Returns:
Modified JSONB after applying the action rules.
*******************************************************************************************************************************/
DROP FUNCTION IF EXISTS techbd_udi_ingress.process_json_action_rules(jsonb, text, text, text, text);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.process_json_action_rules(input_json jsonb, rule_namespace text, key text, created_by text DEFAULT NULL::text, provenance text DEFAULT NULL::text)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_current_rule 		RECORD;
    v_temp_json 		JSONB := process_json_action_rules.input_json;
    v_json_value 		JSONB;
   	v_json_path 		TEXT;
   	v_found_flag   		BOOLEAN;
   	v_new_action    	JSONB;
   	v_error_msg 		TEXT;
    v_error_type 		TEXT;
    v_sqlstate 			TEXT;
    v_pg_detail 		TEXT;
    v_pg_hint 			TEXT;
    v_pg_context 		TEXT;
   	v_issue_message		JSONB;
   	v_message_path		TEXT;
   	v_action_payload	JSONB;
    v_is_accept_flag	NUMERIC := 1;
BEGIN	
    v_temp_json := process_json_action_rules.input_json;

	IF(process_json_action_rules.key IS NULL) THEN
		RETURN NULL;	
	END IF;

    -- Loop through each row of json_action_rule within the given namespace
    FOR v_current_rule IN 
        SELECT json_path, action, reject_json, modify_json, condition, priority, "namespace" 
        FROM techbd_udi_ingress.json_action_rule 
        WHERE (namespace = process_json_action_rules.rule_namespace OR process_json_action_rules.rule_namespace IS NULL)
        ORDER BY priority DESC -- Apply rules in order of priority
    LOOP
		 -- Check if the json_path starts with '$.'
        IF LEFT(v_current_rule.json_path, 2) = '$.' THEN
            v_json_path := v_current_rule.json_path;
        ELSE
            v_json_path := '$.' || v_current_rule.json_path;
        END IF;
		
	     -- Check if the json_path content exists in v_temp_json
	BEGIN
        	v_found_flag := jsonb_path_exists( v_temp_json, v_json_path::jsonpath); -- Casting to jsonpath;   
    	EXCEPTION
        	WHEN OTHERS THEN
            	RAISE NOTICE 'Invalid JSONPath syntax: %', v_json_path;
            	CONTINUE;
    	END;

		IF v_found_flag = false THEN
			CONTINUE;
		END IF; 
	
		IF v_current_rule.action = 'reject' OR v_current_rule.action = 'discard' THEN  		
			-- Dynamically build the path to extract the "diagnostics" based on v_json_path
            v_message_path := v_json_path || '.diagnostics';        

            -- Retrieve the corresponding "diagnostics" value dynamically using v_message_path
            SELECT jsonb_path_query_first(v_temp_json, v_message_path::jsonpath) 
            INTO v_issue_message;   

			IF v_issue_message IS NULL THEN
				-- Dynamically build the path to extract the "message" based on v_json_path
            	v_message_path := v_json_path || '.message';   
				
				-- Retrieve the corresponding "message" value dynamically using v_message_path
	            SELECT jsonb_path_query_first(v_temp_json, v_message_path::jsonpath) 
	            INTO v_issue_message;   
			END IF;
           
            IF v_issue_message IS NULL THEN
           		SELECT jsonb_path_query_first(v_temp_json, v_json_path::jsonpath) 
            	INTO v_issue_message;   
            END IF;
           
            v_action_payload := jsonb_build_object('description', v_current_rule."namespace", 'message', v_issue_message);
		
            v_new_action := jsonb_build_object(
                'action', v_current_rule.action, --'reject',
                'actionPayload', COALESCE(v_current_rule.reject_json, v_action_payload) -- Including reject_json as JSONB
            );     
      
            -- Check if 'techByDesignDisposition' exists in OperationOutcome
            IF v_temp_json->'OperationOutcome' ? 'techByDesignDisposition' THEN
                -- Append to existing array
                v_temp_json := jsonb_set(
                    v_temp_json,
                    '{OperationOutcome, techByDesignDisposition}',
                    (v_temp_json->'OperationOutcome'->'techByDesignDisposition') || v_new_action
                );
            ELSE
                -- Create the array and add the new action
                v_temp_json := jsonb_set(
                    v_temp_json,
                    '{OperationOutcome, techByDesignDisposition}', 
                    jsonb_build_array(v_new_action)
                );
            END IF;
           
            -- If any 'reject' rule is found, then set the v_is_accept_flag to 0, means it is not fully accepted
            v_is_accept_flag := 0;
		END IF;           

		-- Update the last_applied_at for the rule
		UPDATE techbd_udi_ingress.json_action_rule
			SET last_applied_at = current_timestamp
				WHERE json_path = v_current_rule.json_path 
					AND namespace = process_json_action_rules.rule_namespace;
				
		-- If the action is discard then, no need to continue with other rules, just return.
		IF v_current_rule.action = 'discard' THEN 
			RETURN v_temp_json;
		END IF; 
	
		-- Initialize the varible with default values; 
		v_found_flag 		:= FALSE;
		v_json_path			:= NULL;
		v_new_action		:= NULL;
		v_message_path		:= NULL;
		v_action_payload	:= NULL;
    END LOOP;

    /*If all the selected rules are accepted, then add techByDesignDisposition tag with 'accepted'*/
    IF v_is_accept_flag = 1 THEN 
    	v_new_action := jsonb_build_object(
                'action', 'accept'
            );
   
	   IF v_temp_json->'OperationOutcome' ? 'techByDesignDisposition' THEN
	        -- Append to existing array
	        v_temp_json := jsonb_set(
	            v_temp_json,
	            '{OperationOutcome, techByDesignDisposition}',
	            (v_temp_json->'OperationOutcome'->'techByDesignDisposition') || v_new_action
	        );
	    ELSE
	        -- Create the array and add the new action
	        v_temp_json := jsonb_set(
	            v_temp_json,
	            '{OperationOutcome, techByDesignDisposition}', 
	            jsonb_build_array(v_new_action)
	        );
	    END IF;
    END IF;                      
   
    -- Return the modified JSON
    RETURN v_temp_json;
EXCEPTION
   WHEN OTHERS THEN
	    -- Capture exception details
	    GET STACKED DIAGNOSTICS
	        v_error_msg = MESSAGE_TEXT,
	        v_sqlstate = RETURNED_SQLSTATE,
	        v_pg_detail = PG_EXCEPTION_DETAIL,
	        v_pg_hint = PG_EXCEPTION_HINT,
	        v_pg_context = PG_EXCEPTION_CONTEXT;
	        v_error_type = 'SQL';
	
	    -- Log the exception, reusing the previous exception ID if it exists
		PERFORM techbd_udi_ingress.register_issue(
        NULL,
		process_json_action_rules.interaction_key,
		v_error_msg,
		v_error_type,
		v_sqlstate,
		v_pg_detail,
		v_pg_hint,
		v_pg_context,
		process_json_action_rules.created_by,
		process_json_action_rules.provenance);
		RETURN NULL;
END;
$function$
;

/*******************************************************************************************************************************
Procedure for inserting FHIR session diagnostic data extracted from a JSONB payload.

Parameters:
hub_interaction_id  - Unique identifier of the hub interaction (UUID)
tenant_id           - Unique identifier of the tenant (UUID)
uri                 - URI associated with the FHIR session (TEXT)
v_bundle_session_id - Unique identifier of the bundle session (UUID)
payload             - JSONB object containing the FHIR response and validation results (JSONB)
created_by          - Identifier of the user or system creating the record (TEXT)
provenance          - Metadata providing the provenance or origin of the interaction (TEXT)

Returns:
No return value. Inserts diagnostic records into the 'sat_interaction_fhir_session_diagnostic' table for each issue found in the payload.
*******************************************************************************************************************************/
DROP PROCEDURE IF EXISTS techbd_udi_ingress.insert_fhir_session_diagnostic(text,text,text,text,jsonb,text,text,jsonb);
DROP PROCEDURE IF EXISTS techbd_udi_ingress.insert_fhir_session_diagnostic(text,text,text,text,jsonb,text,text,text,text,text);
DROP PROCEDURE IF EXISTS techbd_udi_ingress.insert_fhir_session_diagnostic(text, text, text, text, jsonb, text, text, jsonb, text, text, text);
DROP PROCEDURE IF EXISTS techbd_udi_ingress.insert_fhir_session_diagnostic(text, text, text, text, jsonb, text, text, jsonb, text, text, text, timestamptz);
CREATE OR REPLACE PROCEDURE techbd_udi_ingress.insert_fhir_session_diagnostic(
    IN hub_interaction_id TEXT,
    IN tenant_id TEXT,
    IN uri TEXT,
    IN bundle_session_id TEXT,
    IN payload JSONB,
    IN created_by TEXT,
    IN provenance TEXT,
    IN elaboration JSONB,
    IN ig_version TEXT,
    IN validation_engine TEXT,
    IN bundle_id TEXT, 
    IN created_at timestamp with time zone DEFAULT NULL::timestamp with time zone,
	IN p_techbd_version_number text DEFAULT NULL::text
)
LANGUAGE plpgsql
AS $$
DECLARE
    session_diagnostic_set          RECORD;
    v_exception_id 	                TEXT;
    v_error_msg 					TEXT;
    v_error_type 					TEXT;
    v_sqlstate 						TEXT;
    v_pg_detail 					TEXT;
    v_pg_hint 						TEXT;
    v_pg_context 					TEXT;
    v_created_by 					TEXT 		:= COALESCE(created_by, current_user);  -- Default created_by to the current user if not provided
    v_provenance 					TEXT 		:= COALESCE(provenance, 'unknown');  
    v_created_at 					TIMESTAMPTZ := COALESCE(created_at, CURRENT_TIMESTAMP);  -- Ensure created_at is not null 
BEGIN
    -- Extract values from the JSONB payload variable
    FOR session_diagnostic_set IN
        SELECT
            issue.value ->> 'severity'::text AS severity,
            COALESCE(issue.value ->> 'diagnostics', issue.value ->> 'message') AS message,
            ((issue.value -> 'extension'::text) -> 0) ->> 'valueInteger'::text AS line,
            ((issue.value -> 'extension'::text) -> 1) ->> 'valueInteger'::text AS "column",
            (issue.value -> 'location'::text) ->> 0 AS diagnostics,
            insert_fhir_session_diagnostic.elaboration
        FROM LATERAL jsonb_array_elements(
            (insert_fhir_session_diagnostic.payload -> 'OperationOutcome'::text) -> 'validationResults'::text
        ) validationresult(value),
        LATERAL jsonb_array_elements(
            COALESCE(validationresult.value -> 'operationOutcome' -> 'issue', validationresult.value -> 'issues')
        ) issue(value)
    LOOP
        -- Insert extracted data into the target table
        INSERT INTO techbd_udi_ingress.sat_interaction_fhir_session_diagnostic (
            sat_interaction_fhir_session_diagnostic_id,
            hub_interaction_id,
            tenant_id,
            uri,
            session_id,
            severity,
            message,
            "line",
            "column",
            diagnostics,
            encountered_at,
            elaboration,
            created_by,
            provenance,
            ig_version,
            validation_engine,
            bundle_id,
			techbd_version_number
        )
        VALUES (
            gen_random_uuid()::TEXT, 
            insert_fhir_session_diagnostic.hub_interaction_id,
            insert_fhir_session_diagnostic.tenant_id,
            insert_fhir_session_diagnostic.uri,
            insert_fhir_session_diagnostic.bundle_session_id,
            LOWER(session_diagnostic_set.severity),
            session_diagnostic_set.message,
            session_diagnostic_set.line,
            session_diagnostic_set.column,
            session_diagnostic_set.diagnostics,
			EXTRACT(EPOCH FROM v_created_at),
            session_diagnostic_set.elaboration,
            insert_fhir_session_diagnostic.created_by,
            insert_fhir_session_diagnostic.provenance,
            insert_fhir_session_diagnostic.ig_version,
            insert_fhir_session_diagnostic.validation_engine,
            insert_fhir_session_diagnostic.bundle_id,
			p_techbd_version_number
        );
    END LOOP;
EXCEPTION
   WHEN OTHERS THEN
	    -- Capture exception details
	    GET STACKED DIAGNOSTICS
	        v_error_msg = MESSAGE_TEXT,
	        v_sqlstate = RETURNED_SQLSTATE,
	        v_pg_detail = PG_EXCEPTION_DETAIL,
	        v_pg_hint = PG_EXCEPTION_HINT,
	        v_pg_context = PG_EXCEPTION_CONTEXT;
	        v_error_type = 'SQL';
	
	    -- RAISE NOTICE 'Error occurred: %', SQLERRM; /*Test purpose*/         
	       
	    -- Log the exception, reusing the previous exception ID if it exists
		v_exception_id := techbd_udi_ingress.register_issue(
        COALESCE(v_exception_id,NULL),
		insert_fhir_session_diagnostic.uri,
		v_error_msg,
		v_error_type,
		v_sqlstate,
		v_pg_detail,
		v_pg_hint,
		v_pg_context,
		v_created_by,
		v_provenance, null::jsonb, tenant_id, hub_interaction_id);
END;
$$;

DROP PROCEDURE IF EXISTS techbd_udi_ingress.insert_fhir_screening_info(text,text,text,TIMESTAMPTZ,jsonb,text,text,jsonb);
CREATE OR REPLACE PROCEDURE techbd_udi_ingress.insert_fhir_screening_info (
    IN hub_interaction_id TEXT,
    IN tenant_id TEXT,
    IN uri TEXT,
    IN created_at TIMESTAMPTZ,
    IN payload JSONB,
    IN created_by TEXT,
    IN provenance TEXT,
    IN elaboration JSONB
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_exception_id  text;
    v_error_msg     text;
    v_error_type    text;
    v_sqlstate      text;
    v_pg_detail     text;
    v_pg_hint       text;
    v_pg_context    text;
    v_created_by    text := COALESCE(created_by, current_user);
    v_provenance    text := COALESCE(provenance, 'unknown');
BEGIN
    INSERT INTO techbd_udi_ingress.sat_interaction_fhir_screening_info (
        sat_interaction_fhir_screening_info_id,
        hub_interaction_id,
        qe_name,
        submitted_date_time,
        survey_date_time,
        patient_mrn,
        full_name,
        last_name,
        first_name,
        org_id,
        org_name,
        areas_of_interest,
        total_safety_score,
        elaboration,
        created_by,
        provenance,
        tenant_id
    )
    WITH json_data AS (
        SELECT payload AS doc
    ),

    patient AS (
        SELECT jsonb_path_query_first(
                   doc,
                   '$.entry[*].resource?(@.resourceType == "Patient")'::jsonpath
               ) AS pr
        FROM json_data
    ),

    organization AS (
        SELECT org
        FROM json_data,
             LATERAL jsonb_path_query(
                 doc,
                 '$.entry[*].resource?(@.resourceType == "Organization")'::jsonpath
             ) AS t(org)
    ),

    observations AS (
        SELECT obs
        FROM json_data,
             LATERAL jsonb_path_query(
                 doc,
                 '$.entry[*].resource?(@.resourceType == "Observation")'::jsonpath
             ) AS t(obs)
    ),

    areas AS (
        SELECT DISTINCT
            jsonb_path_query_first(
                obs,
                '$.category[*].coding[*]?(@.system == "http://hl7.org/fhir/us/sdoh-clinicalcare/CodeSystem/SDOHCC-CodeSystemTemporaryCodes").display'::jsonpath
            ) #>> '{}' AS display
        FROM observations
    ),

    mrn_extracted AS (
        SELECT
            jsonb_path_query_first(
                pr,
                '$.identifier[*]?(@.type.coding[0].code == "MR").value'::jsonpath
            ) #>> '{}' AS mrn_value,
            jsonb_path_query_first(
                pr,
                '$.identifier[*]?(@.type.coding[0].code == "MR").system'::jsonpath
            ) #>> '{}' AS mrn_system,
            regexp_replace(
                COALESCE(
                    jsonb_path_query_first(
                        pr,
                        '$.identifier[*]?(@.type.coding[0].code == "MR").assigner.reference'::jsonpath
                    ) #>> '{}',
                    ''
                ),
                '^Organization/', ''
            ) AS org_from_mrn
        FROM patient
    ),

    patient_name AS (
        SELECT
            jsonb_path_query_first(
                pr,
                '$.name[0].text'::jsonpath
            ) #>> '{}' AS full_name,
            jsonb_path_query_first(
                pr,
                '$.name[0].family'::jsonpath
            ) #>> '{}' AS last_name,
            (
                SELECT string_agg(gval #>> '{}', ' ')
                FROM jsonb_path_query(
                         pr,
                         '$.name[0].given[*]'::jsonpath
                     ) AS g(gval)
            ) AS first_name
        FROM patient
    ),

    organization_strings AS (
        SELECT
            string_agg(org #>> '{id}',   ', ') AS org_ids,
            string_agg(org #>> '{name}', ', ') AS org_names
        FROM organization
    ),

    total_safety AS (
        SELECT
            jsonb_path_query_first(
                obs,
                '$.valueCodeableConcept.coding[*]?(@.code == "{score}").display'::jsonpath
            ) #>> '{}' AS score
        FROM observations
        WHERE jsonb_path_exists(
                  obs,
                  '$.code.coding[*]?(@.display == "Total Safety Score")'::jsonpath
              )
    )

    SELECT
        gen_random_uuid()::text                                   AS sat_screening_id,
        hub_interaction_id                                        AS hub_interaction_id,
        tenant_id                                                 AS qe_name,
        created_at                                                AS submitted_date_time,
        CASE
            WHEN payload->>'resourceType' = 'Bundle'
            THEN payload->'meta'->>'lastUpdated'
            ELSE NULL
        END                                                       AS survey_date_time,
        mrn_extracted.mrn_value                                   AS patient_mrn,
        patient_name.full_name                                    AS full_name,
        patient_name.last_name                                    AS last_name,
        patient_name.first_name                                   AS first_name,
        organization_strings.org_ids                              AS org_id,
        organization_strings.org_names                            AS org_name,
        (SELECT string_agg(display, ', ') FROM areas)             AS areas_of_interest,
        (SELECT score FROM total_safety LIMIT 1)                  AS total_safety_score,
        elaboration                                               AS elaboration,
        v_created_by                                              AS created_by,
        v_provenance                                              AS provenance,
        tenant_id                                                 AS tenant_id
    FROM mrn_extracted
    LEFT JOIN patient_name        ON true
    LEFT JOIN organization_strings ON true
    -- A: skip insert when MRN is missing
    WHERE mrn_extracted.mrn_value IS NOT NULL;

EXCEPTION
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS
            v_error_msg  = MESSAGE_TEXT,
            v_sqlstate   = RETURNED_SQLSTATE,
            v_pg_detail  = PG_EXCEPTION_DETAIL,
            v_pg_hint    = PG_EXCEPTION_HINT,
            v_pg_context = PG_EXCEPTION_CONTEXT;
        v_error_type := 'SQL';

        -- Let register_issue generate an exception id if NULL is passed
        v_exception_id := techbd_udi_ingress.register_issue(
            NULL,
            uri,
            v_error_msg,
            v_error_type,
            v_sqlstate,
            v_pg_detail,
            v_pg_hint,
            v_pg_context,
            v_created_by,
            v_provenance,
            NULL::jsonb,
            tenant_id,
            hub_interaction_id
        );
END;        
$$;

DROP PROCEDURE IF EXISTS techbd_udi_ingress.insert_validation_issues(text,text,jsonb,text,text,jsonb);
DROP PROCEDURE IF EXISTS techbd_udi_ingress.insert_validation_issues(text, text, jsonb, text, text, jsonb, text, text);
CREATE OR REPLACE PROCEDURE techbd_udi_ingress.insert_validation_issues (
    IN hub_interaction_id TEXT,
    IN uri TEXT,
    IN payload JSONB,
    IN created_by TEXT,
    IN provenance TEXT,
    IN elaboration JSONB,
	IN p_tenant_id text DEFAULT NULL::text, 
	IN p_techbd_version_number text DEFAULT NULL::text
)
LANGUAGE plpgsql
AS $$
DECLARE
    issue_set                       RECORD;
    v_exception_id 	                TEXT;
    v_error_msg 					TEXT;
    v_error_type 					TEXT;
    v_sqlstate 						TEXT;
    v_pg_detail 					TEXT;
    v_pg_hint 						TEXT;
    v_pg_context 					TEXT;
    v_created_by 					TEXT 	:= COALESCE(created_by, current_user);  -- Default created_by to the current user if not provided
    v_provenance 					TEXT 	:= COALESCE(provenance, 'unknown');
BEGIN
    -- Extract values from the JSONB payload variable
    FOR issue_set IN
        SELECT
            jsonb_array_elements(validation_result -> 'operationOutcome'::text -> 'issue'::text) AS issue,
            to_timestamp(((validation_result ->> 'completedAt'::text)::numeric)::double precision) AS date_time,
            validation_result ->> 'igVersion'::text AS ig_version,
			validation_result ->> 'profileUrl'::text AS profile_url,			
			regexp_replace(validation_result ->> 'profileUrl'::text, '^https?://([^/]+)/.*$', '\1') AS profile_url_domain,
            TRIM(BOTH FROM regexp_replace(
                (validation_result -> 'observability'::text) ->> 'name'::text, 
                '\(TODO:get from API instead of hard coding\) \(FHIR version 4.0.1\)\s*$'::text, ''::text)) AS validation_engine
        FROM LATERAL jsonb_array_elements(insert_validation_issues.payload -> 'OperationOutcome'-> 'validationResults'::text) validation_result
    LOOP
        -- Insert the extracted data into the target table
       INSERT INTO techbd_udi_ingress.sat_interaction_fhir_validation_issue (
            sat_interaction_fhir_validation_issue_id,
            hub_interaction_id,
            issue,
            date_time,
            validation_engine,
            ig_version,
            elaboration,
            created_by,
            provenance,
            severity,
			tenant_id,
			techbd_version_number,
			profile_url,
			profile_url_domain
        )
        VALUES (
            gen_random_uuid()::TEXT, 
            insert_validation_issues.hub_interaction_id,
            issue_set.issue ->> 'diagnostics'::text, 
            issue_set.date_time,
            issue_set.validation_engine,
            issue_set.ig_version,
            insert_validation_issues.elaboration,
            v_created_by,
            insert_validation_issues.provenance,
            issue_set.issue ->> 'severity'::text,
			p_tenant_id,
			p_techbd_version_number,
			issue_set.profile_url,
			issue_set.profile_url_domain
        );
    END LOOP;
EXCEPTION
    WHEN OTHERS THEN
        -- Capture exception details
        GET STACKED DIAGNOSTICS
            v_error_msg = MESSAGE_TEXT,
            v_sqlstate = RETURNED_SQLSTATE,
            v_pg_detail = PG_EXCEPTION_DETAIL,
            v_pg_hint = PG_EXCEPTION_HINT,
            v_pg_context = PG_EXCEPTION_CONTEXT;
            v_error_type = 'SQL';
        
        -- Log the exception, reusing the previous exception ID if it exists
        v_exception_id := techbd_udi_ingress.register_issue(
            COALESCE(v_exception_id, NULL),
            insert_validation_issues.uri,
            v_error_msg,
            v_error_type,
            v_sqlstate,
            v_pg_detail,
            v_pg_hint,
            v_pg_context,
            v_created_by,
            v_provenance, null::jsonb, p_tenant_id, insert_validation_issues.hub_interaction_id
        );
END;       
$$;

/**
* The function techbd_udi_ingress.json_action_rule_upserted is designed to either insert a new record or 
* update an existing record in the json_action_rule table, depending on whether the action_rule_id is provided. 
* If the action_rule_id is not NULL, the function attempts to update the existing record. If action_rule_id is NULL, 
* a new record is inserted with a randomly generated UUID as the primary key.

* The function also handles exception logging. In the event of an error during the execution of an insert or 
* update operation, the exception details are captured, and the issue is logged in a diagnostic table using the * register_issue function.
*/
DROP FUNCTION IF EXISTS techbd_udi_ingress.json_action_rule_upserted(text, text, text, text, jsonb, jsonb, jsonb, int4, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.json_action_rule_upserted(text, text, text, text, jsonb, jsonb, jsonb, int4, text, text, text, text);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.json_action_rule_upserted(action_rule_id text, namespace text, json_path text, action text, condition jsonb, reject_json jsonb, modify_json jsonb, priority integer, description text, updated_by text, created_by text, provenance text)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    -- Declare variables to hold error and extracted data
    v_error_msg                 TEXT;
    v_error_type                TEXT;
    v_sqlstate                  TEXT;
    v_pg_detail                 TEXT;
    v_pg_hint                   TEXT;
    v_pg_context                TEXT;
    v_exception_id              TEXT;
    v_created_by                TEXT        := COALESCE(json_action_rule_upserted.created_by, current_user);  -- Default created_by to the current user if not provided    
    v_updated_by                TEXT        := COALESCE(json_action_rule_upserted.updated_by, current_user);  -- Default created_by to the current user if not provided
    v_provenance                TEXT        := COALESCE(provenance, 'unknown');  -- Set default provenance
    v_return                    TEXT;
    
BEGIN
	/**
	* The function techbd_udi_ingress.json_action_rule_upserted is designed to either insert a new record or 
	* update an existing record in the json_action_rule table, depending on whether the action_rule_id is provided. 
	* If the action_rule_id is not NULL, the function attempts to update the existing record. If action_rule_id is NULL, 
	* a new record is inserted with a randomly generated UUID as the primary key.
	
	* The function also handles exception logging. In the event of an error during the execution of an insert or 
	* update operation, the exception details are captured, and the issue is logged in a diagnostic table using the * register_issue function.
	*/
	
	-- Check whether the json rule specified in 'json_path' is a valid path or not. If not then, raise exception
	IF techbd_udi_ingress.is_valid_jsonpath(json_action_rule_upserted.json_path) = FALSE THEN 
		RAISE EXCEPTION 'Invalid json_path.';
	END IF;
	
    -- First, attempt to update the record if it exists
	IF(json_action_rule_upserted.action_rule_id IS NOT null) THEN

		UPDATE techbd_udi_ingress.json_action_rule
		SET
			"namespace"         = json_action_rule_upserted.namespace,
			json_path           = json_action_rule_upserted.json_path,
			"action"            = json_action_rule_upserted.action,
			"condition"         = json_action_rule_upserted.condition,
			reject_json         = json_action_rule_upserted.reject_json,
			modify_json         = json_action_rule_upserted.modify_json,
			priority            = json_action_rule_upserted.priority,
			updated_at          = CURRENT_TIMESTAMP,
			updated_by          = v_updated_by,
			last_applied_at     = CURRENT_TIMESTAMP,
			provenance          = json_action_rule_upserted.provenance,
			description 		= json_action_rule_upserted.description
			WHERE techbd_udi_ingress.json_action_rule.action_rule_id = json_action_rule_upserted.action_rule_id
			RETURNING techbd_udi_ingress.json_action_rule.action_rule_id INTO v_return 
		;	

		RETURN v_return;
	END IF;

   	INSERT INTO techbd_udi_ingress.json_action_rule (
        action_rule_id,
        "namespace",
        json_path,
        "action",
        "condition",
        reject_json,
        modify_json,
        priority,
        description,
        updated_at,
        updated_by,
        last_applied_at,
        created_at,
        created_by,
        provenance
    ) VALUES (
        gen_random_uuid()::TEXT,
        json_action_rule_upserted.namespace,
        json_action_rule_upserted.json_path,
        json_action_rule_upserted.action,
        json_action_rule_upserted.condition,
        json_action_rule_upserted.reject_json,
        json_action_rule_upserted.modify_json,
        json_action_rule_upserted.priority,
        json_action_rule_upserted.description,
        CURRENT_TIMESTAMP,
        v_updated_by,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP,
        json_action_rule_upserted.created_by,
        json_action_rule_upserted.provenance
    ) RETURNING json_action_rule.action_rule_id INTO v_return;

    -- Return a success message
    RETURN v_return;
EXCEPTION
   WHEN OTHERS THEN
        -- Capture exception details
        GET STACKED DIAGNOSTICS
            v_error_msg = MESSAGE_TEXT,
            v_sqlstate = RETURNED_SQLSTATE,
            v_pg_detail = PG_EXCEPTION_DETAIL,
            v_pg_hint = PG_EXCEPTION_HINT,
            v_pg_context = PG_EXCEPTION_CONTEXT;
        	v_error_type = 'SQL';

        RAISE NOTICE 'Error occurred: %', SQLERRM; /*Test purpose*/         
       
        -- Log the exception, reusing the previous exception ID if it exists
        PERFORM techbd_udi_ingress.register_issue(
            COALESCE(v_exception_id,NULL),
            'action_rule',
            v_error_msg,
            v_error_type,
            v_sqlstate,
            v_pg_detail,
            v_pg_hint,
            v_pg_context,
            v_created_by,
            v_provenance);

        RETURN NULL;
END;
$function$
;

/*====================================================================================================*/
 
DROP FUNCTION IF EXISTS techbd_udi_ingress.is_valid_jsonpath(TEXT);

CREATE OR REPLACE FUNCTION techbd_udi_ingress.is_valid_jsonpath(json_path TEXT)
RETURNS BOOLEAN AS $$
DECLARE 
	is_exists boolean := false;
BEGIN
    -- Attempt to apply the JSONPath to the dummy JSON
    is_exists := jsonb_path_exists('{}'::jsonb, json_path::jsonpath);
    RETURN TRUE;  -- If no error, the JSONPath is valid
EXCEPTION
    WHEN others THEN
    	RETURN FALSE;  -- If an error occurs, the JSONPath is invalid
END;
$$ LANGUAGE plpgsql;
/*====================================================================================================*/


DROP PROCEDURE IF EXISTS techbd_udi_ingress.insert_fhir_screening_patient(text,text,text,TIMESTAMPTZ,jsonb,text,text,jsonb);
DROP PROCEDURE IF EXISTS techbd_udi_ingress.insert_fhir_screening_patient(text,text,text,TIMESTAMPTZ,jsonb,text,text,jsonb,text);
CREATE OR REPLACE PROCEDURE techbd_udi_ingress.insert_fhir_screening_patient (
    IN hub_interaction_id TEXT,
    IN tenant_id TEXT,
    IN uri TEXT,
    IN created_at TIMESTAMPTZ,
    IN payload JSONB,
    IN created_by TEXT,
    IN provenance TEXT,
    IN elaboration JSONB,
    IN primary_org_id TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_exception_id  TEXT;
    v_error_msg     TEXT;
    v_error_type    TEXT;
    v_sqlstate      TEXT;
    v_pg_detail     TEXT;
    v_pg_hint       TEXT;
    v_pg_context    TEXT;

    v_created_by    TEXT := COALESCE(created_by, current_user);
    v_provenance    TEXT := COALESCE(provenance, 'unknown');
BEGIN
    INSERT INTO techbd_udi_ingress.sat_interaction_fhir_screening_patient (
        sat_interaction_fhir_screening_patient_id,
        hub_interaction_id,
        qe_name,
        patient_mrn,
        patient_id,
        patient_type,
        patient_full_name,
        patient_first_name,
        patient_last_name,
        patient_gender,
        patient_birth_date,
        patient_address,
        patient_city,
        patient_state,
        patient_postal_code,
        patient_language,
        patient_ssn,
        org_id,
        elaboration,
        created_by,
        provenance,
        primary_org_id,
        tenant_id
    )
    WITH patient_resource AS (
        SELECT jsonb_path_query_first(
                   payload,
                   '$.entry[*].resource?(@.resourceType == "Patient")'::jsonpath
               ) AS pr
    ),

    org_ids AS (
        SELECT string_agg(
                   (org #>> '{id}'),
                   ', '
               ) AS organization_ids
        FROM jsonb_path_query(
                 payload,
                 '$.entry[*].resource?(@.resourceType == "Organization")'::jsonpath
             ) AS t(org)
    ),

    patient_parsed AS (
        SELECT
            -- Basic info
            pr->>'id' AS patient_id,
            pr->>'resourceType' AS patient_type,
            pr->>'gender' AS gender,
            pr->>'birthDate' AS birth_date,

            -- Name fields
            jsonb_path_query_first(pr, '$.name[0].text'::jsonpath) #>> '{}' AS full_name,
            jsonb_path_query_first(pr, '$.name[0].family'::jsonpath) #>> '{}' AS last_name,

            (
                SELECT string_agg(g #>> '{}', ' ')
                FROM jsonb_path_query(pr, '$.name[0].given[*]'::jsonpath) AS t(g)
            ) AS first_name,

            -- Address fields
            jsonb_path_query_first(pr, '$.address[0].text'::jsonpath)       #>> '{}' AS address,
            jsonb_path_query_first(pr, '$.address[0].city'::jsonpath)       #>> '{}' AS city,
            jsonb_path_query_first(pr, '$.address[0].state'::jsonpath)      #>> '{}' AS state,
            jsonb_path_query_first(pr, '$.address[0].postalCode'::jsonpath) #>> '{}' AS postal_code,

            -- Language
            jsonb_path_query_first(
                pr,
                '$.communication[0].language.coding[0].code'::jsonpath
            ) #>> '{}' AS language,

            -- Identifiers
            jsonb_path_query_first(
                pr,
                '$.identifier[*]?(@.type.coding[0].code == "MR").value'::jsonpath
            ) #>> '{}' AS medical_record_number,

            jsonb_path_query_first(
                pr,
                '$.identifier[*]?(@.type.coding[0].code == "SS").value'::jsonpath
            ) #>> '{}' AS social_security_number

        FROM patient_resource
    )

    SELECT
        gen_random_uuid()::TEXT,
        hub_interaction_id,
        tenant_id,                   -- qe_name
        medical_record_number,
        patient_id,
        patient_type,
        full_name,
        first_name,
        last_name,
        gender,
        birth_date,
        address,
        city,
        state,
        postal_code,
        language,
        social_security_number,
        organization_ids,
        elaboration,
        v_created_by,
        v_provenance,
        primary_org_id,
        tenant_id
    FROM patient_parsed
    CROSS JOIN org_ids;

EXCEPTION
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS
            v_error_msg = MESSAGE_TEXT,
            v_sqlstate  = RETURNED_SQLSTATE,
            v_pg_detail = PG_EXCEPTION_DETAIL,
            v_pg_hint   = PG_EXCEPTION_HINT,
            v_pg_context = PG_EXCEPTION_CONTEXT;

        v_error_type := 'SQL';

        v_exception_id := techbd_udi_ingress.register_issue(
            NULL,
            uri,
            v_error_msg,
            v_error_type,
            v_sqlstate,
            v_pg_detail,
            v_pg_hint,
            v_pg_context,
            v_created_by,
            v_provenance,
            NULL::jsonb,
            tenant_id,
            hub_interaction_id
        );
END;        
$$;

DROP PROCEDURE IF EXISTS techbd_udi_ingress.insert_fhir_screening_organization(text,text,text,TIMESTAMPTZ,jsonb,text,text,jsonb);
DROP PROCEDURE IF EXISTS techbd_udi_ingress.insert_fhir_screening_organization(text,text,text,TIMESTAMPTZ,jsonb,text,text,jsonb,text);
CREATE OR REPLACE PROCEDURE techbd_udi_ingress.insert_fhir_screening_organization (
    IN hub_interaction_id TEXT,
    IN tenant_id TEXT,
    IN uri TEXT,
    IN created_at TIMESTAMPTZ,
    IN payload JSONB,
    IN created_by TEXT,
    IN provenance TEXT,
    IN elaboration JSONB
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_exception_id  TEXT;
    v_error_msg     TEXT;
    v_error_type    TEXT;
    v_sqlstate      TEXT;
    v_pg_detail     TEXT;
    v_pg_hint       TEXT;
    v_pg_context    TEXT;
    v_created_by    TEXT := COALESCE(created_by, current_user);
    v_provenance    TEXT := COALESCE(provenance, 'unknown');
BEGIN
    INSERT INTO techbd_udi_ingress.sat_interaction_fhir_screening_organization (
        sat_interaction_fhir_screening_organization_id,
        hub_interaction_id,
        qe_name,
        org_id,
        org_type,
        org_name,
        org_active,
        org_address,
        org_city,
        org_state,
        org_postal_code,
        elaboration,
        created_by,
        provenance,
        tenant_id
    )
    WITH organizations AS (
        SELECT
            -- Entire Organization resource JSON
            value AS org
        FROM jsonb_path_query(
                payload,
                '$.entry[*].resource?(@.resourceType == "Organization")'::jsonpath
        ) AS t(value)
    ),
    parsed_org AS (
        SELECT
            org->>'id'                      AS org_id,
            org->>'resourceType'            AS org_type,
            org->>'name'                    AS org_name,
            org->>'active'                  AS org_active,
            jsonb_path_query_first(org, '$.address[0].text'::jsonpath) #>> '{}' AS org_address,
            jsonb_path_query_first(org, '$.address[0].city'::jsonpath) #>> '{}' AS org_city,
            jsonb_path_query_first(org, '$.address[0].state'::jsonpath ) #>> '{}' AS org_state,
            jsonb_path_query_first(org, '$.address[0].postalCode'::jsonpath ) #>> '{}' AS org_postal_code

        FROM organizations
    )
    SELECT
        gen_random_uuid()::text,
        hub_interaction_id,
        tenant_id,
        org_id,
        org_type,
        org_name,
        org_active,
        org_address,
        org_city,
        org_state,
        org_postal_code,
        elaboration,
        v_created_by,
        v_provenance,
        tenant_id
    FROM parsed_org;

EXCEPTION
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS
            v_error_msg = MESSAGE_TEXT,
            v_sqlstate  = RETURNED_SQLSTATE,
            v_pg_detail = PG_EXCEPTION_DETAIL,
            v_pg_hint   = PG_EXCEPTION_HINT,
            v_pg_context= PG_EXCEPTION_CONTEXT;

        v_error_type := 'SQL';

        v_exception_id := techbd_udi_ingress.register_issue(
            NULL,
            uri,
            v_error_msg,
            v_error_type,
            v_sqlstate,
            v_pg_detail,
            v_pg_hint,
            v_pg_context,
            v_created_by,
            v_provenance,
            NULL::jsonb,
            tenant_id,
            hub_interaction_id
        );
END;        
$$;

/*******************************************************************************************************************************
Function for upserting HL7 interaction request data from a JSONB payload.

Parameters:
hub_interaction_id   - Unique identifier of the hub interaction (TEXT)
tenant_id            - Unique identifier of the tenant (TEXT)
uri                  - URI associated with the HL7 request (TEXT)
nature               - Nature of the interaction request (TEXT)
from_state           - The initial state of the interaction (TEXT)
to_state             - The final state of the interaction after processing (TEXT)
payload              - JSONB object containing the HL7 request data (JSONB), default is NULL
state_transition_reason - Reason for transitioning between states (TEXT), default is NULL
created_at           - Timestamp of when the record was created (TIMESTAMPTZ), defaults to CURRENT_TIMESTAMP if not provided
created_by           - Identifier of the user or system creating the record (TEXT), defaults to the current user if not provided
provenance           - Metadata providing the provenance or origin of the interaction (TEXT), defaults to 'unknown'
elaboration          - JSONB object containing additional details about the interaction (JSONB), default is NULL

Returns:
The function returns the generated UUID of the newly inserted record in the 'sat_interaction_hl7_request' table or NULL if the 
interaction is not valid or the payload is null.

Description:
This function inserts HL7 interaction request data into the 'sat_interaction_hl7_request' table. It ensures that the hub interaction 
exists in the 'hub_interaction' table and that the payload is not null. If both conditions are met, a new record is inserted, 
generating a UUID for the 'sat_interaction_hl7_request_id'. The function also handles exceptions, logging errors with the 
'register_issue' procedure if any SQL or runtime errors occur during the insert operation.
*******************************************************************************************************************************/
DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_hl7_request_upserted(text, text, text, text, text, text, jsonb, text, timestamp with time zone, text, text, jsonb);
DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_hl7_request_upserted(text, text, text, text, text, text, jsonb, text, timestamptz, text, text, jsonb, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_hl7_request_upserted(text, text, text, text, text, text, jsonb, text, text, timestamptz, text, text, jsonb, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_hl7_request_upserted(text, text, text, text, text, text, jsonb, text, text, timestamptz, text, text, jsonb, text, text, text, text, text);

CREATE OR REPLACE FUNCTION techbd_udi_ingress.sat_interaction_hl7_request_upserted(
	p_hub_interaction_id text, 
	p_tenant_id text, 
	p_uri text, 
	p_nature text, 
	p_from_state text, 
	p_to_state text, 
	p_payload jsonb DEFAULT NULL::jsonb, 
	p_hl7_payload_text text DEFAULT NULL::text,
	p_state_transition_reason text DEFAULT NULL::text, 
	p_created_at timestamp with time zone DEFAULT NULL::timestamp with time zone, 
	p_created_by text DEFAULT NULL::text, 
	p_provenance text DEFAULT NULL::text, 
	p_elaboration jsonb DEFAULT NULL::jsonb, 
	p_client_ip_address text DEFAULT NULL::text, 
	p_user_agent text DEFAULT NULL::text, 
	p_origin text DEFAULT NULL::text,
	p_techbd_version_number text DEFAULT NULL::text,
	p_file_name text DEFAULT NULL::text)	
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    -- Declare variables to hold error and extracted data
	v_error_msg 					TEXT;
    v_error_type 					TEXT;
    v_sqlstate 						TEXT;
    v_pg_detail 					TEXT;
    v_pg_hint 						TEXT;
    v_pg_context 					TEXT;   
    v_created_at 					TIMESTAMPTZ := COALESCE(p_created_at, CURRENT_TIMESTAMP);  -- Ensure created_at is not null
    v_created_by 					TEXT 		:= COALESCE(p_created_by, current_user);  -- Default created_by to the current user if not provided
    v_provenance 					TEXT 		:= COALESCE(p_provenance, 'unknown');  -- Set default provenance
    v_exception_id 					TEXT		:= gen_random_uuid()::text;
    
    -- Additional variables for extracting and holding data from the payload JSONB
    v_return 						TEXT 	:= NULL;
	v_return_status					BOOLEAN := false;
	v_is_exception					BOOLEAN := false;
	v_elaboration 					JSONB 	:= NULL;
	v_elaboration_steps 			JSONB	:= '[]'::jsonb;
	v_elaboration_steps_text 		TEXT[] 	:= ARRAY[]::text[]; 
    v_elaboration_steps_jsonb 		JSONB 	:= '[]'::jsonb;
	v_validation_error              TEXT;


BEGIN	
	/* This function is designed to insert a new record into the sat_interaction_hl7_request table if it does not already exist. 
	 * It takes in various details about an HL7 interaction (such as hub_interaction_id, tenant_id, uri, payload, and others), 
	 * ensures that certain fields like created_at, created_by, and provenance have defaults, and inserts the data. The function 
	 * generates a unique UUID for the new record's primary key and returns it.
	 * If an error occurs during the insertion, the function captures detailed diagnostic information about the exception and 
	 * logs it using the register_issue function. It returns NULL if no new record is inserted or if an error occurs. 
	 * 'origin' can be 'http', 'sftp' etc.
	 */
	BEGIN
		v_elaboration_steps_text := array_append(v_elaboration_steps_text, '0: In sat_interaction_hl7_request_upserted function');
	
		IF NOT EXISTS(SELECT 1 FROM techbd_udi_ingress.hub_interaction t
	  		WHERE t.hub_interaction_id = p_hub_interaction_id 
	  		AND t."key" = p_uri) THEN
	  		v_return_status := true;
			v_return := NULL;
			v_error_msg := 'hub_interaction_id is not in the hub_interaction table.';	
	  	END IF;
	  
		/*IF p_hl7_payload_text IS NULL and p_payload IS NULL THEN
	  		RETURN NULL;	
	  	END IF;*/
	  
		IF NOT v_return_status THEN
		  	-- The INSERT statement inserts a new record into the sat_interaction_hl7_request table, generating a UUID for the primary key.
		    -- If the operation succeeds, the generated UUID is returned.
		    INSERT INTO techbd_udi_ingress.sat_interaction_hl7_request (
		        sat_interaction_hl7_request_id,
		        hub_interaction_id,
		        tenant_id,
		        tenant_id_lower,
		        uri,
		        nature,
		        payload,
				hl7_payload_text,
		        client_ip_address,
		        user_agent,
		        from_state,
		        to_state,
		        state_transition_reason,
		        created_by,
		        provenance,
		        elaboration,
		        origin,
				techbd_version_number,
				file_name
		    )
		    VALUES (
		        gen_random_uuid()::TEXT,  -- Generate a unique UUID for the primary key
		        p_hub_interaction_id,
		        p_tenant_id,
		        LOWER(p_tenant_id),  -- Store the tenant ID in lowercase for consistency
		        p_uri,
		        p_nature,
		        p_payload,
				p_hl7_payload_text,
		        p_client_ip_address,
		        p_user_agent,
		        p_from_state,
		        p_to_state,
		        p_state_transition_reason,
		        p_created_by,
		        p_provenance,
		        p_elaboration, 
		        COALESCE(p_origin, 'http'),
				p_techbd_version_number, 
				p_file_name
		    ) RETURNING sat_interaction_hl7_request_id INTO v_return;  -- Return the generated UUID

			v_elaboration_steps_text := array_append(v_elaboration_steps_text, '1: Inserted into sat_interaction_hl7_request');

			v_validation_error := (p_payload -> 'result' -> 0-> 'details' ->> 'text')::text;

			IF(p_nature = 'HL7 Validation Result' AND p_to_state = 'VALIDATION_FAILED'
				AND v_validation_error IS NOT NULL AND v_validation_error != '')
    		THEN		
				INSERT INTO techbd_udi_ingress.sat_interaction_hl7_validation_errors(
					sat_hl7_validation_errors_id, 
					hub_interaction_id, 
					tenant_id, 
					uri, 
					error_type, 
					"error",           
					file_name, 
					origin, 
					user_agent, 
					techbd_version_number, 
					created_at, 
					created_by, 
					provenance
				)
				VALUES(
					gen_random_uuid()::TEXT,  -- Generate a unique UUID for the primary key
					p_hub_interaction_id,
					p_tenant_id,
					p_uri,
					'hl7_validation_errors',
					v_validation_error,
					p_file_name,
					COALESCE(p_origin, 'http'),
					p_user_agent,
					p_techbd_version_number,
					v_created_at,
					v_created_by,
					v_provenance);	

				v_elaboration_steps_text := array_append(v_elaboration_steps_text, '2: Inserted into sat_interaction_hl7_validation_errors');			
            END IF;
		END IF;
	EXCEPTION
	   WHEN OTHERS THEN
		    -- Capture exception details
		    GET STACKED DIAGNOSTICS
		        v_error_msg = MESSAGE_TEXT,
		        v_sqlstate = RETURNED_SQLSTATE,
		        v_pg_detail = PG_EXCEPTION_DETAIL,
		        v_pg_hint = PG_EXCEPTION_HINT,
		        v_pg_context = PG_EXCEPTION_CONTEXT;
		        v_error_type = 'SQL';
		
		    v_is_exception := true;				
			v_return_status := true;
			v_return := NULL;
	END;

	-- Save the log details into table
	v_elaboration := jsonb_build_object(
	         						'hub_interaction_id', p_hub_interaction_id,
									'interaction_key', p_uri,
	         						'nature', p_nature,
									'tenant_id', p_tenant_id,
	         						'from_state', p_from_state,
	         						'to_state', p_to_state,
									'techbd_version_number', p_techbd_version_number,
									'file_name', p_file_name,
									'client_ip_address', p_client_ip_address
	         						);
	-- Convert steps array to JSONB and attach
    IF array_length(v_elaboration_steps_text, 1) IS NOT NULL THEN
        v_elaboration_steps_jsonb := to_jsonb(v_elaboration_steps_text);
        v_elaboration := v_elaboration || jsonb_build_object('steps', v_elaboration_steps_jsonb);
    END IF;

    -- Attach error message if present
    IF v_error_msg IS NOT NULL THEN
        v_elaboration := v_elaboration || jsonb_build_object('error_message', v_error_msg);
    END IF;

    -- Register diagnostic log
    CALL techbd_udi_ingress.register_diagnostic_log(v_exception_id, p_uri, v_created_by, v_provenance, 'info'::text, 
       																'sat_interaction_hl7_request_upserted function Logs'::text, v_created_by, 'success'::text, NULL::text, 0, v_elaboration, p_tenant_id, p_hub_interaction_id);

	IF v_is_exception = true THEN    
        -- Log the exception, reusing the previous exception ID if it exists
        v_elaboration := v_elaboration || jsonb_build_array(jsonb_build_object('error_message', v_error_msg));
        v_exception_id := techbd_udi_ingress.register_issue(
            v_exception_id, p_uri, v_error_msg, v_error_type, v_sqlstate, v_pg_detail, v_pg_hint, v_pg_context, v_created_by, v_provenance, v_elaboration, p_tenant_id, p_hub_interaction_id
        );
	END IF;
   	RETURN v_return;
END;
$function$
;

DROP VIEW IF EXISTS techbd_udi_ingress.fhir_tenant_stats_view  CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.fhir_tenant_stats_view  AS  
    SELECT 
        sihr.tenant_id_lower::text AS qe_name,  
        sihr.created_at::date,
        COUNT(CASE 
                  WHEN sihr.nature = 'Original FHIR Payload' THEN 1 
                  ELSE NULL 
              END) AS qe_fhir_submission_count,        
        COUNT(CASE 
                  WHEN sihr.nature = 'Forwarded HTTP Response' THEN 1 
                  ELSE NULL 
              END) AS qe_fhir_pass_count,
        COUNT(CASE 
                  WHEN sihr.nature = 'Forwarded HTTP Response Error' THEN 1 
                  ELSE NULL 
              END) AS qe_fhir_fail_count
    FROM 
        techbd_udi_ingress.hub_interaction hintr
    JOIN 
        techbd_udi_ingress.sat_interaction_fhir_request sihr 
    ON 
        hintr.hub_interaction_id = sihr.hub_interaction_id
    WHERE 
        sihr.uri = ANY (ARRAY['/Bundle'::text, '/Bundle/'::text]) 
        AND sihr.tenant_id_lower::text IN ('healthelink', 'healtheconn', 'healthix', 'grrhio', 'hixny')
    AND sihr.created_at >= (CURRENT_DATE - INTERVAL '1 day')  -- Filter for last 1 day
GROUP BY 
    sihr.tenant_id_lower::text, sihr.created_at::date
ORDER BY 
    sihr.created_at::date DESC;



/****************************************************************************************
 * View for tracking recent FHIR submission statuses by organization, capturing success and failure rates.
 *
 * Purpose:
 * - Provides a summary of FHIR interactions for each organization within the past 7 days.
 * - Distinguishes successful submissions from failed ones to highlight potential issues in data processing.
 *
 * Columns:
 * - **organization_id**: Unique identifier of the organization.
 * - **tenant_id_lower**: Lowercase tenant ID for consistency and grouping.
 * - **qe_total_submissions**: Total count of interactions of type 'RequestResponseEncountered'.
 * - **success_submissions**: Number of successful 'Forwarded HTTP Response' submissions.
 * - **faield_submissions**: Number of failed 'Forwarded HTTP Response Error' submissions.
 * - **recently_created_at**: Timestamp of the most recent interaction for each organization.
 *
 * Filters:
 * - Includes only interactions with specific '/Bundle/' URIs.
 * - Considers data from the last 7 days, excluding interactions from unspecified ('N/A') tenants.
 *
 * Ordering:
 * - Results are ordered by **recently_created_at** in descending order, emphasizing the most recent submissions.
 ****************************************************************************************/


drop view if exists techbd_udi_ingress.fhir_scn_submission cascade;
create or replace view techbd_udi_ingress.fhir_scn_submission as  
 WITH submission_counts AS (
         SELECT sihr.organization_id AS organization_id,
            count(
                CASE
                    WHEN sihr.nature = 'Original FHIR Payload'::text THEN 1
                    ELSE NULL::integer
                END) AS qe_total_submissions,
            count(
                CASE
                    WHEN sihr.nature = 'Forwarded HTTP Response'::text THEN 1
                    ELSE NULL::integer
                END) AS success_submissions,
            count(
                CASE
                    WHEN sihr.nature = 'Forwarded HTTP Response Error'::text THEN 1
                    ELSE NULL::integer
                END) AS faield_submissions,
            max(sihr.created_at) AS recently_created_at
           FROM techbd_udi_ingress.sat_interaction_fhir_request sihr
          WHERE (sihr.uri = ANY (ARRAY['/Bundle/'::text, '/Bundle'::text])) AND sihr.created_at >= (CURRENT_DATE - '7 days'::interval) AND sihr.tenant_id <> 'N/A'::text
          AND sihr.organization_id != ''
         GROUP BY sihr.organization_id
        )
 SELECT organization_id, 
    qe_total_submissions,
    success_submissions,
    faield_submissions,
    recently_created_at
   FROM submission_counts sc
  ORDER BY recently_created_at DESC;  


/****************************************************************************************
 * View for detailed tracking of recent FHIR submissions by organization and tenant.
 *
 * Purpose:
 * - Summarizes FHIR interaction data for each organization and tenant over the last 7 days.
 * - Distinguishes between total, successful, and failed submissions to identify issues in data processing.
 *
 * Columns:
 * - **organization_id**: Unique identifier of the organization.
 * - **qe_name**: Lowercased tenant ID, representing the tenant associated with the organization.
 * - **qe_total_submissions**: Count of all interactions marked as 'RequestResponseEncountered' for the tenant.
 * - **success_submissions**: Count of successfully forwarded submissions ('Forwarded HTTP Response').
 * - **faield_submissions**: Count of failed submissions ('Forwarded HTTP Response Error').
 * - **recently_created_at**: The timestamp of the latest interaction for the tenant within the last 7 days.
 *
 * Filters:
 * - Filters interactions that use specific '/Bundle/' URIs.
 * - Considers records from the past 7 days and excludes tenants marked as 'N/A'.
 *
 * Ordering:
 * - Results are ordered by **recently_created_at** in descending order, showing the most recent activities first.
 ****************************************************************************************/

drop view if exists techbd_udi_ingress.fhir_scn_submission_details cascade;
create or replace view techbd_udi_ingress.fhir_scn_submission_details as  
  with submission_counts as (
select
	sihr.organization_id as organization_id,
	sihr.tenant_id_lower as qe_name,
	count(
                case
                    when sihr.nature = 'Original FHIR Payload'::text then 1
                    else null::integer
                end) as qe_total_submissions,
	count(
                case
                    when sihr.nature = 'Forwarded HTTP Response'::text then 1
                    else null::integer
                end) as success_submissions,
	count(
                case
                    when sihr.nature = 'Forwarded HTTP Response Error'::text then 1
                    else null::integer
                end) as faield_submissions,
	max(sihr.created_at) as recently_created_at
from
	techbd_udi_ingress.sat_interaction_fhir_request sihr
where
	(sihr.uri = any (array['/Bundle/'::text,
	'/Bundle'::text]))
	and sihr.created_at >= (CURRENT_DATE - '7 days'::interval)
	and sihr.tenant_id <> 'N/A'::text
    and sihr.organization_id != ''
group by
	sihr.organization_id,
	sihr.tenant_id_lower
        )
 select
	organization_id,
	qe_name,
	qe_total_submissions,
	success_submissions,
	faield_submissions,
	recently_created_at
from
	submission_counts sc
order by
	recently_created_at desc;
 

/****************************************************************************************
 * View: techbd_udi_ingress.missing_datalake_submission_details
 *
 * Purpose:
 * - Identifies FHIR submissions with interactions of type 'RequestResponseEncountered' 
 *   that are missing subsequent forwarding responses, specifically lacking entries in 
 *   'Forwarded HTTP Response' or 'Forwarded HTTP Response Error'.
 * - Intended to highlight submissions that may not have reached the datalake within 
 *   the last 7 days, potentially indicating processing or forwarding issues.
 *
 * Columns:
 * - **hub_interaction_id**: Unique identifier for each interaction, providing traceability 
 *   for each FHIR submission attempt.
 * - **sat_interaction_http_request_id**: Unique identifier specifically for the HTTP request 
 *   associated with the interaction, aiding in request-specific tracking.
 * - **qe_name**: Tenant name identifier, representing the organization or entity that submitted 
 *   the interaction, enabling tenant-level analysis.
 * - **created_at**: Timestamp of the interaction's creation, indicating when the initial 
 *   submission attempt occurred.
 *
 * Filters:
 * - Filters records to include only those interactions where the `nature` is 
 *   'RequestResponseEncountered'.
 * - Restricts to interactions involving URIs of type `/Bundle/` or `/Bundle` in the 
 *   past 7 days, excluding tenants labeled as 'N/A'.
 * - Uses `NOT EXISTS` to exclude any interaction that has subsequent entries in 
 *   'Forwarded HTTP Response' or 'Forwarded HTTP Response Error', thereby capturing 
 *   only those interactions without a successful or error response.
 *
 * Ordering:
 * - Results are ordered by `created_at` in descending order, prioritizing recent 
 *   interactions for quicker identification and action on current issues.
 ****************************************************************************************/



drop view if exists techbd_udi_ingress.missing_datalake_submission_details cascade;
create or replace view techbd_udi_ingress.missing_datalake_submission_details as
 select
	sihr.hub_interaction_id,
	sihr.sat_interaction_fhir_request_id as sat_interaction_http_request_id,
	sihr.tenant_id_lower as qe_name,
	sihr.created_at
from
	techbd_udi_ingress.sat_interaction_fhir_request sihr
where
	sihr.nature = 'Original FHIR Payload'::text
	and (sihr.uri = any (array['/Bundle/',
	'/Bundle']))
	and sihr.created_at >= (CURRENT_DATE - '7 days'::interval)
	and not exists (
	select
		1
	from
		techbd_udi_ingress.sat_interaction_fhir_request sihr2
	where
		sihr2.hub_interaction_id = sihr.hub_interaction_id
		and sihr2.nature in ('Forwarded HTTP Response', 'Forwarded HTTP Response Error')
    )
order by
	sihr.created_at desc;



/****************************************************************************************
 * This view, `organization_interaction_summary`, provides a consolidated count of FHIR 
 * interactions for each organization, focusing on specific types of interactions:
 * 
 * - **count_screenings**: Aggregates the count of interactions categorized as "Screening." 
 *   This count includes records where `resource_type_set` contains "Consent."
 * 
 * - **count_referrals**: Aggregates the count of interactions categorized as "Referral." 
 *   This count includes records where `resource_type_set` contains either "ServiceRequest" 
 *   or "Task."
 * 
 * - **count_assessments**: Aggregates the count of interactions categorized as "Assessment." 
 *   Similar to screenings, this count includes records where `resource_type_set` contains "Consent."
 * 
 * The view is filtered to include only specific URIs (`/Bundle` or `/Bundle/`) and excludes records 
 * where `organization_id` is empty. Grouped by `organization_id` and `organization_name`, this view 
 * facilitates quick access to interaction summaries by organization, enabling efficient tracking and 
 * analysis of FHIR resources across different organizations.
 ****************************************************************************************/

DROP VIEW IF EXISTS techbd_udi_ingress.organization_interaction_summary CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.organization_interaction_summary AS 
SELECT 
    organization_id,
    organization_name,
    COUNT(CASE 
            WHEN bundle_type LIKE '%Screening%' THEN 1 
         END) AS count_screenings,
    COUNT(CASE 
            WHEN bundle_type LIKE '%Referral%' THEN 1 
         END) AS count_referrals,
    COUNT(CASE 
            WHEN bundle_type LIKE '%Assessment%' THEN 1 
         END) AS count_assessments
FROM techbd_udi_ingress.sat_interaction_fhir_request htt_req
WHERE  
  uri IN ('/Bundle', '/Bundle/')
and organization_id != ''
 and created_at  >= (CURRENT_DATE - '7 days'::interval)
GROUP BY 
    organization_id,
    organization_name;  

DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_csv_request_upserted(text, text, text, text, text, bytea, text, text, text, text, text, text, text, text, text, text, jsonb, text, text, text, timestamptz, text, text, jsonb, text, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_csv_request_upserted(text, text, text, text, text, bytea, text, text, text, text, text, text, text, text, text, text, jsonb, text, text, text, timestamptz, text, text, jsonb, text, text, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_csv_request_upserted(text, text, text, text, text, bytea, text, text, text, text, text, text, text, text, text, text, jsonb, text, text, text, timestamptz, text, text, jsonb, text, text, text, text, text, jsonb);
DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_csv_request_upserted(text, text, text, text, text, bytea, text, text, text, text, text, text, text, text, text, text, jsonb, text, text, text, timestamptz, text, text, jsonb, text, text, text, text, text, jsonb, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_csv_request_upserted(text, text, text, text, text, bytea, text, text, text, text, text, text, text, text, text, text, jsonb, text, text, text, timestamptz, text, text, jsonb, text, text, text, text, text, jsonb, text, jsonb);
DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_csv_request_upserted(text, text, text, text, text, bytea, text, text, text, text, text, text, text, text, text, text, jsonb, text, text, text, timestamptz, text, text, jsonb, text, text, text, text, text, jsonb, text, jsonb, int4, int4, text);

CREATE OR REPLACE FUNCTION techbd_udi_ingress.sat_interaction_csv_request_upserted(interaction_id text, uri text, nature text, tenant_id text DEFAULT NULL::text, csv_zip_file_name text DEFAULT NULL::text, csv_zip_file_content bytea DEFAULT NULL::bytea, group_id text DEFAULT NULL::text, status text DEFAULT NULL::text, csv_screening_observation_data_payload_text text DEFAULT NULL::text, csv_screening_profile_data_payload_text text DEFAULT NULL::text, csv_demographic_data_payload_text text DEFAULT NULL::text, csv_qe_admin_data_payload_text text DEFAULT NULL::text, csv_screening_observation_data_file_name text DEFAULT NULL::text, csv_screening_profile_data_file_name text DEFAULT NULL::text, csv_demographic_data_file_name text DEFAULT NULL::text, csv_qe_admin_data_file_name text DEFAULT NULL::text, validation_result_payload jsonb DEFAULT NULL::jsonb, from_state text DEFAULT NULL::text, to_state text DEFAULT NULL::text, state_transition_reason text DEFAULT NULL::text, created_at timestamp with time zone DEFAULT NULL::timestamp with time zone, created_by text DEFAULT NULL::text, provenance text DEFAULT NULL::text, elaboration jsonb DEFAULT NULL::jsonb, zip_file_hub_interaction_id text DEFAULT NULL::text, client_ip_address text DEFAULT NULL::text, user_agent text DEFAULT NULL::text, origin text DEFAULT NULL::text, sftp_session_id text DEFAULT NULL::text, zip_file_processing_errors jsonb DEFAULT NULL::jsonb, p_techbd_version_number text DEFAULT NULL::text, p_full_operation_outcome jsonb DEFAULT NULL::jsonb, p_total_number_of_files_in_zip_file INTEGER DEFAULT NULL::INTEGER, p_number_of_fhir_bundles_generated_from_zip_file INTEGER DEFAULT NULL::INTEGER, p_data_validation_status TEXT DEFAULT NULL::text)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    -- Declare variables to hold error and extracted data
	v_error_msg 					TEXT;
    v_error_type 					TEXT;
    v_sqlstate 						TEXT;
    v_pg_detail 					TEXT;
    v_pg_hint 						TEXT;
    v_pg_context 					TEXT;   
    v_created_at 					TIMESTAMPTZ := COALESCE(created_at, CURRENT_TIMESTAMP);  -- Ensure created_at is not null
    v_created_by 					TEXT 		:= COALESCE(created_by, current_user);  -- Default created_by to the current user if not provided
    v_provenance 					TEXT 		:= COALESCE(provenance, 'unknown');  -- Set default provenance
    v_exception_id 					TEXT		:= gen_random_uuid()::text;
    
    -- Additional variables for extracting and holding data from the payload JSONB
   	v_return 						TEXT 	:= NULL;
	v_return_status					BOOLEAN := false;
	v_is_exception					BOOLEAN := false;
    v_elaboration 					JSONB 	:= NULL;
	v_elaboration_steps 			JSONB	:= '[]'::jsonb;
	v_elaboration_steps_text 		TEXT[] 	:= ARRAY[]::text[]; 
    v_elaboration_steps_jsonb 		JSONB 	:= '[]'::jsonb;
    v_diagnostic_id 				TEXT  	:= gen_random_uuid()::text;
    --lo_id 							OID;
	v_status						TEXT 	:= sat_interaction_csv_request_upserted.status;
	p_elaboration					JSONB 	:= sat_interaction_csv_request_upserted.elaboration;
	p_validation_result_payload		JSONB 	:= sat_interaction_csv_request_upserted.validation_result_payload;
	p_interaction_id				TEXT 	:= sat_interaction_csv_request_upserted.interaction_id;
	v_denormalize_result 			TEXT 	:= NULL;
BEGIN	
	BEGIN
		
		v_elaboration_steps_text := array_append(v_elaboration_steps_text, '0: In sat_interaction_csv_request_upserted function');
	
		IF NOT EXISTS(SELECT 1 FROM techbd_udi_ingress.hub_interaction t
	  		WHERE t.hub_interaction_id = p_interaction_id 
	  		AND t."key" = sat_interaction_csv_request_upserted.uri) THEN
	  			v_return_status := true;
				v_return := NULL;
				v_error_msg := 'hub_interaction_id is not in the hub_interaction table.';
	  	END IF;
	
		IF NOT v_return_status THEN
		    IF nature = 'Update Zip File Payload' THEN /*Update the full payload*/
		    	UPDATE techbd_udi_ingress.sat_interaction_zip_file_request
		    		SET validation_result_payload = p_validation_result_payload
		    		WHERE hub_interaction_id = p_interaction_id;
		    	v_return := p_interaction_id;
		
				v_elaboration_steps_text := array_append(v_elaboration_steps_text, '1: Updated validation_result_payload');
		
			ELSIF nature = 'Update Zip File Processing Details' THEN /*Update the errors and status after the zip file is processed*/
				IF v_status IS NOT NULL THEN
					UPDATE techbd_udi_ingress.sat_interaction_zip_file_request
					SET status = v_status
					WHERE hub_interaction_id = p_interaction_id;
				END IF;
		
				IF zip_file_processing_errors IS NOT NULL THEN
					UPDATE techbd_udi_ingress.sat_interaction_zip_file_request
					SET general_errors = zip_file_processing_errors
					WHERE hub_interaction_id = p_interaction_id;	
				END IF;
		
				IF p_elaboration IS NOT NULL THEN
					UPDATE techbd_udi_ingress.sat_interaction_zip_file_request
					SET elaboration = p_elaboration
					WHERE hub_interaction_id = p_interaction_id;
				END IF;
		
				IF p_full_operation_outcome IS NOT NULL THEN
					UPDATE techbd_udi_ingress.sat_interaction_zip_file_request
					SET full_operation_outcome = p_full_operation_outcome
					WHERE hub_interaction_id = p_interaction_id;

					--Denormalize the 'processing_errors' errors from full_operation_outcome
					SELECT techbd_udi_ingress.denormalize_validation_errors(
						p_category => 'processing_errors', 
						p_hub_interaction_id => p_interaction_id,
						p_uri => uri
					) INTO v_denormalize_result;
				END IF;
		
				IF p_validation_result_payload IS NOT NULL THEN
					UPDATE techbd_udi_ingress.sat_interaction_zip_file_request
					SET validation_result_payload = p_validation_result_payload
					WHERE hub_interaction_id = p_interaction_id;
				END IF;
		
				IF p_total_number_of_files_in_zip_file IS NOT NULL THEN
					UPDATE techbd_udi_ingress.sat_interaction_zip_file_request
					SET total_number_of_files_in_zip_file = p_total_number_of_files_in_zip_file
					WHERE hub_interaction_id = p_interaction_id;
				END IF;
		
				IF p_number_of_fhir_bundles_generated_from_zip_file IS NOT NULL THEN
					UPDATE techbd_udi_ingress.sat_interaction_zip_file_request
					SET number_of_fhir_bundles_generated_from_zip_file = p_number_of_fhir_bundles_generated_from_zip_file
					WHERE hub_interaction_id = p_interaction_id;
				END IF;
		
				IF p_data_validation_status IS NOT NULL THEN
					UPDATE techbd_udi_ingress.sat_interaction_zip_file_request
					SET data_validation_status = p_data_validation_status
					WHERE hub_interaction_id = p_interaction_id;
				END IF;
		
		    	v_return := p_interaction_id;
				v_elaboration_steps_text := array_append(v_elaboration_steps_text, '1: Updated sat_interaction_zip_file_request');
		    
		    ELSIF nature = 'Original CSV Zip Archive' THEN 
		    	/*lo_id := lo_create(0); -- Create a new large OBJECT
		    	PERFORM lo_open(lo_id, 131072); -- Open the large object in write MODE
		    	PERFORM lo_import('/path/to/example.zip', lo_id); -- Write file contents to the large OBJECT*/
		    
		    	-- The INSERT statement inserts a new record (insert the csv zip file) into the sat_interaction_zip_file_request table, generating a UUID for the primary key.
			    INSERT INTO techbd_udi_ingress.sat_interaction_zip_file_request (
			        sat_interaction_zip_file_request_id, hub_interaction_id, tenant_id, tenant_id_lower, uri, nature,
			        group_id, status, csv_zip_file_name, csv_zip_file_content,
					client_ip_address, user_agent,
					elaboration, created_at, created_by, provenance, 
					validation_result_payload, origin, sftp_session_id, techbd_version_number, full_operation_outcome,
					general_errors,
					total_number_of_files_in_zip_file,
					number_of_fhir_bundles_generated_from_zip_file,
					data_validation_status
			    )
			    VALUES (
			        gen_random_uuid()::TEXT,  -- Generate a unique UUID for the primary key
			        p_interaction_id,
			        tenant_id, 
			        LOWER(tenant_id), 
			        uri, 
			        nature,
			        group_id, 
			        v_status, 
			        csv_zip_file_name,
			        csv_zip_file_content, 
			        --lo_id, -- Insert the reference (OID) into the table
					client_ip_address, user_agent,
					p_elaboration, v_created_at, v_created_by, v_provenance, 
					p_validation_result_payload, 
					sat_interaction_csv_request_upserted.origin,
		            sat_interaction_csv_request_upserted.sftp_session_id,
					p_techbd_version_number,
					p_full_operation_outcome,
					zip_file_processing_errors,
					p_total_number_of_files_in_zip_file,
					p_number_of_fhir_bundles_generated_from_zip_file,
					p_data_validation_status
			    ) RETURNING sat_interaction_zip_file_request_id INTO v_return;  -- Return the generated UUID
			    
			    v_elaboration_steps_text := array_append(v_elaboration_steps_text, '1: Inserted into sat_interaction_zip_file_request');
		    ELSE 
		  	    -- The INSERT statement inserts a new record into the sat_interaction_flat_file_csv_request table, generating a UUID for the primary key.
			    INSERT INTO techbd_udi_ingress.sat_interaction_flat_file_csv_request (
			        sat_interaction_flat_file_csv_request_id, hub_interaction_id, tenant_id, tenant_id_lower, uri, nature,
			        group_id, status, validation_result_payload, 
					screening_observation_data_payload_text, screening_profile_data_payload_text, demographic_data_payload_text, qe_admin_data_payload_text, 
					screening_observation_data_file_name, screening_profile_data_file_name, demographic_data_file_name, qe_admin_data_file_name,
					client_ip_address, user_agent,
					from_state, to_state, state_transition_reason,
					elaboration, created_at, created_by, provenance,
					zip_file_hub_interaction_id, techbd_version_number
			    )
			    VALUES (
			        gen_random_uuid()::TEXT,  -- Generate a unique UUID for the primary key
			        p_interaction_id,
			        tenant_id, 
			        LOWER(tenant_id), 
			        uri, 
			        nature,
			        group_id, 
			        v_status, 
			        p_validation_result_payload,
			        csv_screening_observation_data_payload_text, csv_screening_profile_data_payload_text, csv_demographic_data_payload_text, csv_qe_admin_data_payload_text, 
					csv_screening_observation_data_file_name, csv_screening_profile_data_file_name, csv_demographic_data_file_name, csv_qe_admin_data_file_name,
					client_ip_address, user_agent,
					from_state, 
					to_state, 
					state_transition_reason,
					NULL, v_created_at, v_created_by, v_provenance,
					zip_file_hub_interaction_id, p_techbd_version_number
			    ) RETURNING sat_interaction_flat_file_csv_request_id INTO v_return;  -- Return the generated UUID
			    
			    v_elaboration_steps_text := array_append(v_elaboration_steps_text, '1: Inserted into sat_interaction_flat_file_csv_request');
		
				IF p_validation_result_payload IS NOT NULL THEN
					--Denormalize the 'data_integrity' errors from validation_result_payload
					SELECT techbd_udi_ingress.denormalize_validation_errors(
						p_category => 'data_integrity', 
						p_hub_interaction_id => p_interaction_id
					) INTO v_denormalize_result;
			
					v_elaboration_steps_text := array_append(v_elaboration_steps_text, '2: Denormalized the errors from validation_result_payload');
				END IF;
		
		    END IF;
		    
		    --RETURN v_return;
		END IF;
	EXCEPTION
	   WHEN OTHERS THEN
		    -- Capture exception details
		    GET STACKED DIAGNOSTICS
		        v_error_msg = MESSAGE_TEXT,
		        v_sqlstate = RETURNED_SQLSTATE,
		        v_pg_detail = PG_EXCEPTION_DETAIL,
		        v_pg_hint = PG_EXCEPTION_HINT,
		        v_pg_context = PG_EXCEPTION_CONTEXT;
		        v_error_type = 'SQL';

			v_is_exception := true;				
			v_return_status := true;
			v_return := NULL;
	END;

	-- Save the log details into table
	v_elaboration := jsonb_build_object(
	 						'interaction_id', p_interaction_id,
	 						'interaction_key', uri,
	 						'nature', nature,
	 						'validation_result_payload', p_validation_result_payload,
	 						'status', v_status,
	 						'zip_file_processing_errors', zip_file_processing_errors,
							'p_full_operation_outcome', p_full_operation_outcome,
							'elaboration', p_elaboration,
							'p_total_number_of_files_in_zip_file', p_total_number_of_files_in_zip_file,
							'p_number_of_fhir_bundles_generated_from_zip_file', p_number_of_fhir_bundles_generated_from_zip_file,
							'p_data_validation_status', p_data_validation_status
	 						);
	-- Convert steps array to JSONB and attach
    IF array_length(v_elaboration_steps_text, 1) IS NOT NULL THEN
        v_elaboration_steps_jsonb := to_jsonb(v_elaboration_steps_text);
        v_elaboration := v_elaboration || jsonb_build_object('steps', v_elaboration_steps_jsonb);
    END IF;

    -- Attach error message if present
    IF v_error_msg IS NOT NULL THEN
        v_elaboration := v_elaboration || jsonb_build_object('error_message', v_error_msg);
    END IF;

    -- Register diagnostic log
    CALL techbd_udi_ingress.register_diagnostic_log(v_exception_id, uri, v_created_by, v_provenance, 'info'::text, 
       						'sat_interaction_csv_request_upserted function Logs'::text, v_created_by, 'success'::text, NULL::text, 0, v_elaboration, tenant_id, p_interaction_id);

	IF v_is_exception = true THEN    
        -- Log the exception, reusing the previous exception ID if it exists
        v_elaboration := v_elaboration || jsonb_build_array(jsonb_build_object('error_message', v_error_msg));
        v_exception_id := techbd_udi_ingress.register_issue(
            v_exception_id, uri, v_error_msg, v_error_type, v_sqlstate, v_pg_detail, v_pg_hint, v_pg_context, v_created_by, v_provenance, v_elaboration, tenant_id, p_interaction_id
        );
	END IF;
   	RETURN v_return;
END;
$function$
;


DROP FUNCTION IF EXISTS techbd_udi_ingress.get_fhir_scn_submission(DATE, DATE);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.get_fhir_scn_submission(
    start_date DATE,
    end_date DATE
)
RETURNS TABLE (
    organization_id TEXT,
    qe_total_submissions INTEGER,
    success_submissions INTEGER,
    failed_submissions INTEGER,
    recently_created_at TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    WITH submission_counts AS (
        SELECT 
            sihr.organization_id,
            count(
                CASE 
                    WHEN sihr.nature = 'Original FHIR Payload' THEN 1 
                    ELSE NULL 
                END
            )::INTEGER AS qe_total_submissions,
            count(
                CASE 
                     WHEN sihr.nature = 'Forwarded HTTP Response'
                         OR (sihr.nature = 'Forwarded HTTP Response Error' AND sihr.replay_status = 'True')
                    THEN 1 
                END
            )::INTEGER AS success_submissions,
            count(
                CASE 
                    WHEN  (sihr.nature = 'Forwarded HTTP Response Error' AND sihr.replay_status IS DISTINCT FROM 'True' ) 
                    THEN 1
                END
            )::INTEGER AS failed_submissions,  
            max(sihr.created_at)::TIMESTAMP AS recently_created_at
        FROM techbd_udi_ingress.sat_interaction_fhir_request sihr
        WHERE 
            sihr.uri = ANY (ARRAY['/Bundle/', '/Bundle', '/flatfile/csv/Bundle', '/flatfile/csv/Bundle/', '/ccda/Bundle', '/ccda/Bundle/', '/hl7v2/Bundle', '/hl7v2/Bundle/'])  
            AND sihr.created_at >= TO_TIMESTAMP(start_date::text, 'YYYY-MM-DD') 
            AND sihr.created_at < TO_TIMESTAMP(end_date::text, 'YYYY-MM-DD') + INTERVAL '1 day'
            AND sihr.tenant_id <> 'N/A' 
            AND sihr.organization_id <> ''
        GROUP BY sihr.organization_id
    )
    SELECT 
        submission_counts.organization_id,
        submission_counts.qe_total_submissions,
        submission_counts.success_submissions,
        submission_counts.failed_submissions,
        submission_counts.recently_created_at
    FROM submission_counts 
     WHERE submission_counts.qe_total_submissions <> 0 
    ORDER BY submission_counts.recently_created_at DESC;
END;
$$ LANGUAGE plpgsql;



/*******************************************************************************************
This function returns a summary of FHIR submissions per tenant (`qe_name`) between the 
given start and end dates. It calculates the total submissions, successful Shinny Datalake 
forwards, failed forwards, and the most recent submission timestamp. Only interactions 
with specific '/Bundle'-related URIs are considered.
*******************************************************************************************/


DROP FUNCTION IF EXISTS techbd_udi_ingress.get_fhir_needs_attention(date, date);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.get_fhir_needs_attention(start_date DATE, end_date DATE)
RETURNS TABLE (
    qe_name TEXT,
    qe_total_submissions BIGINT,
    techbd_processed BIGINT,
    shinny_datalake_submissions BIGINT,
    shinny_datalake_submissions_failed BIGINT,
    valid_bundles BIGINT,
    recently_created_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY    

	WITH base_interactions AS (
        SELECT
            hub_interaction_id,
            tenant_id_lower
        FROM techbd_udi_ingress.sat_interaction_fhir_request
        WHERE nature = 'Original FHIR Payload'
        AND created_at >= TO_TIMESTAMP(start_date::text, 'YYYY-MM-DD') 
        AND created_at < TO_TIMESTAMP(end_date::text, 'YYYY-MM-DD') + INTERVAL '1 day'
        AND tenant_id <> 'N/A'        
        AND uri = ANY (ARRAY[
            '/Bundle', '/Bundle/',
            '/flatfile/csv/Bundle', '/flatfile/csv/Bundle/',
            '/ccda/Bundle', '/ccda/Bundle/',
            '/hl7v2/Bundle', '/hl7v2/Bundle/'
        ])
    ),

	-- ONE ROW PER hub_interaction_id (interaction-level facts)
	per_interaction AS (
		SELECT
			bi.hub_interaction_id,
			bi.tenant_id_lower,

			-- all 4 missing → 1 else 0 
			CASE
				WHEN NOT EXISTS (
					SELECT 1
					FROM techbd_udi_ingress.sat_interaction_fhir_request d
					WHERE d.hub_interaction_id = bi.hub_interaction_id
					  AND d.nature IN (
						  'techByDesignDisposition',
						  'Forward HTTP Request',
						  'Forwarded HTTP Response',
						  'Forwarded HTTP Response Error'
					  )
				)
				THEN 1 ELSE 0
			END AS missing_step_flag
		FROM base_interactions bi
	),
	
	-- AGGREGATE interaction-level metrics FIRST 
	interaction_agg AS (
		SELECT
			tenant_id_lower AS qe_name,
			COUNT(*) AS qe_total_submissions,
			SUM(missing_step_flag) AS missing_steps_total
		FROM per_interaction
		GROUP BY tenant_id_lower
	),

	-- ROW-level metrics (safe to aggregate after join)
	row_level_agg AS (
		SELECT
			pi.tenant_id_lower AS qe_name,
			COUNT(*) FILTER (WHERE sihr.to_state = 'COMPLETE') AS shinny_datalake_submissions,
			COUNT(*) FILTER (WHERE sihr.nature = 'Forward HTTP Request') AS valid_bundles_forwarded,
			COUNT(*) FILTER (WHERE sihr.to_state = 'COMPLETE') AS valid_bundles_completed,
			COUNT(*) FILTER (WHERE sihr.nature = 'Forwarded HTTP Response Error' AND sihr.replay_status IS DISTINCT FROM 'True') AS valid_bundles_with_fail_response,
			COUNT(*) FILTER (WHERE sihr.nature = 'techByDesignDisposition' AND sihr.techbd_disposition_action = 'discard') AS invalid_bundles,
			MAX(sihr.created_at) AS recently_created_at
		FROM per_interaction pi
		JOIN techbd_udi_ingress.sat_interaction_fhir_request sihr ON sihr.hub_interaction_id = pi.hub_interaction_id
		GROUP BY pi.tenant_id_lower
	)

	-- FINAL RESULT
	SELECT
		ia.qe_name,
		ia.qe_total_submissions,
		(ia.qe_total_submissions - ia.missing_steps_total) AS techbd_processed,
		rl.shinny_datalake_submissions,
		(rl.valid_bundles_forwarded - rl.shinny_datalake_submissions) AS shinny_datalake_submissions_failed,
		(ia.qe_total_submissions - ia.missing_steps_total - rl.invalid_bundles) AS valid_bundles,
		rl.recently_created_at    
	FROM interaction_agg ia
	JOIN row_level_agg rl
	  ON rl.qe_name = ia.qe_name
	WHERE ia.qe_total_submissions > 0
	ORDER BY rl.recently_created_at DESC;
END;
$$ LANGUAGE plpgsql;



DROP FUNCTION if exists techbd_udi_ingress.get_interaction_http_request();
/*
CREATE OR REPLACE FUNCTION techbd_udi_ingress.get_interaction_http_request()
RETURNS TABLE (
    interaction_id UUID,
    uri TEXT,
    interaction_created_at TIMESTAMPTZ,
    interaction_created_by TEXT,
    interaction_provenance TEXT,
    sat_interaction_http_request_id UUID,
    nature TEXT,
    tenant_id TEXT,
    user_agent TEXT,
    client_ip_address TEXT,
    content_type TEXT,
    payload JSONB,
    elaboration TEXT,
    from_state TEXT,
    to_state TEXT,
    state_transition_reason TEXT,
    request_created_at TIMESTAMPTZ,
    request_created_by TEXT,
    request_provenance TEXT,
    issues_count INTEGER,
    resource_types TEXT,
    start_time TIMESTAMPTZ,
    finish_time TIMESTAMPTZ,
    duration_millisecs NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        combined_query.interaction_id::UUID,
        combined_query.uri::TEXT,
        combined_query.interaction_created_at::TIMESTAMPTZ,
        combined_query.interaction_created_by::TEXT,
        combined_query.interaction_provenance::TEXT,
        combined_query.sat_interaction_http_request_id::UUID,
        combined_query.nature::TEXT,
        combined_query.tenant_id::TEXT,
        combined_query.user_agent::TEXT,
        combined_query.client_ip_address::TEXT,
        combined_query.content_type::TEXT,
        combined_query.payload::JSONB,
        combined_query.elaboration::TEXT,
        combined_query.from_state::TEXT,
        combined_query.to_state::TEXT,
        combined_query.state_transition_reason::TEXT,
        combined_query.request_created_at::TIMESTAMPTZ,
        combined_query.request_created_by::TEXT,
        combined_query.request_provenance::TEXT,
        combined_query.issues_count::INTEGER,
        combined_query.resource_types::TEXT,
        combined_query.start_time::TIMESTAMPTZ,
        combined_query.finish_time::TIMESTAMPTZ,
        combined_query.duration_millisecs::NUMERIC
    FROM (
        SELECT 
            htt_req.hub_interaction_id AS interaction_id,
            htt_req.uri,
            htt_req.created_at AS interaction_created_at,
            htt_req.created_by AS interaction_created_by,
            htt_req.provenance AS interaction_provenance,
            htt_req.sat_interaction_fhir_request_id AS sat_interaction_http_request_id,
            htt_req.nature,
            htt_req.tenant_id,
            htt_req.user_agent,
            htt_req.client_ip_address,
            'application/json'::text AS content_type,
            htt_req.payload,
            htt_req.elaboration,
            htt_req.from_state,
            htt_req.to_state,
            htt_req.state_transition_reason,
            htt_req.created_at AS request_created_at,
            htt_req.created_by AS request_created_by,
            htt_req.provenance AS request_provenance,
            htt_req.issues_count,
            htt_req.resource_type_set AS resource_types,
            htt_req.interaction_start_time AS start_time,
            htt_req.interaction_end_time AS finish_time,
            EXTRACT(epoch FROM htt_req.interaction_end_time::timestamp without time zone - htt_req.interaction_start_time::timestamp without time zone) * 1000::numeric AS duration_millisecs
        FROM techbd_udi_ingress.sat_interaction_fhir_request htt_req
        
        UNION ALL
        
        SELECT 
            user_req.hub_interaction_id AS interaction_id,
            user_req.uri,
            user_req.created_at AS interaction_created_at,
            user_req.created_by AS interaction_created_by,
            user_req.provenance AS interaction_provenance,
            user_req.sat_interaction_user_id AS sat_interaction_http_request_id,
            user_req.nature,
            user_req.tenant_id,
            user_req.user_agent,
            user_req.client_ip_address,
            'application/json'::text AS content_type,
            NULL::jsonb AS payload,
            user_req.elaboration,
            NULL::text AS from_state,
            NULL::text AS to_state,
            NULL::text AS state_transition_reason,
            user_req.created_at AS request_created_at,
            user_req.created_by AS request_created_by,
            user_req.provenance AS request_provenance,
            NULL::integer AS issues_count,
            NULL::text AS resource_types,
            user_req.interaction_start_time AS start_time,
            user_req.interaction_end_time AS finish_time,
            EXTRACT(epoch FROM user_req.interaction_end_time::timestamp without time zone - user_req.interaction_start_time::timestamp without time zone) * 1000::numeric AS duration_millisecs
        FROM techbd_udi_ingress.sat_interaction_user user_req
        WHERE user_req.uri !~~* '%/Bundle%'::text 
    ) combined_query;
END;
$$ LANGUAGE plpgsql;
*/


DROP FUNCTION if exists techbd_udi_ingress.get_fhir_needs_attention_details(text, date, date);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.get_fhir_needs_attention_details(
    tenant_id TEXT,
    start_date DATE,
    end_date DATE
)
RETURNS TABLE (
    hub_interaction_id TEXT,
    sat_interaction_http_request_id TEXT,
    qe_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE, 
    error_message TEXT
) AS $$
BEGIN
	RETURN QUERY EXECUTE format(
        'WITH base_interactions AS (
            SELECT
                hub_interaction_id
            FROM techbd_udi_ingress.sat_interaction_fhir_request
            WHERE nature = ''Original FHIR Payload''
            AND created_at >= TO_TIMESTAMP($1::text, ''YYYY-MM-DD'') 
	        AND created_at < TO_TIMESTAMP($2::text, ''YYYY-MM-DD'') + INTERVAL ''1 day''
            AND tenant_id_lower = $3
            AND uri = ANY (ARRAY[''/Bundle/'', ''/Bundle'', ''/flatfile/csv/Bundle'', ''/flatfile/csv/Bundle/'', ''/ccda/Bundle'', ''/ccda/Bundle/'', ''/hl7v2/Bundle'', ''/hl7v2/Bundle/''])
        )
        -- 1. nature = Forwarded HTTP Response Error and to_state = FAIL ===> once got Forwarded HTTP Response Error but no record with to_state as COMPLETE
        SELECT 
            sihr.hub_interaction_id,
            sihr.sat_interaction_fhir_request_id AS sat_interaction_http_request_id,
            sihr.tenant_id_lower AS qe_name,
            sihr.created_at,
            sihr.error_message
        FROM techbd_udi_ingress.sat_interaction_fhir_request sihr
        JOIN base_interactions bi ON bi.hub_interaction_id = sihr.hub_interaction_id
        WHERE sihr.nature = ''Forwarded HTTP Response Error'' 
		AND NOT EXISTS (
            SELECT 1
            FROM techbd_udi_ingress.sat_interaction_fhir_request r
            WHERE r.hub_interaction_id = sihr.hub_interaction_id
                AND r.to_state = ''COMPLETE''
        )
        
        UNION ALL

        /*2. techByDesignDisposition generated and valid but no response received
         * 	 techByDesignDisposition <> discard AND 
			 NOT EXISTS ((natue = Forwarded HTTP Response or Forwarded HTTP Response Replay) and to_state = COMPLETE) AND 
			 NOT EXISTS (nature = Forwarded HTTP Response Error or Forwarded HTTP Response Replay Error) and to_state = FAIL*/
        SELECT
            ofp.hub_interaction_id,
            ofp.sat_interaction_fhir_request_id AS sat_interaction_http_request_id,
            ofp.tenant_id_lower AS qe_name,
            ofp.created_at,
            ''Forwarded HTTP Response is missing'' AS error_message
        FROM base_interactions bi
        JOIN techbd_udi_ingress.sat_interaction_fhir_request ofp ON ofp.hub_interaction_id = bi.hub_interaction_id
        WHERE ofp.nature = ''Original FHIR Payload''
            -- disposition exists and is valid
            AND EXISTS (
                SELECT 1
                FROM techbd_udi_ingress.sat_interaction_fhir_request d
                WHERE d.hub_interaction_id = bi.hub_interaction_id
                AND d.nature = ''techByDesignDisposition''
                AND d.techbd_disposition_action <> ''discard''
            )
            AND NOT EXISTS (
                SELECT 1
                FROM techbd_udi_ingress.sat_interaction_fhir_request r
                WHERE r.hub_interaction_id = bi.hub_interaction_id
                AND (r.to_state = ''COMPLETE'')
            )
            AND NOT EXISTS (
                SELECT 1
                FROM techbd_udi_ingress.sat_interaction_fhir_request r
                WHERE r.hub_interaction_id = bi.hub_interaction_id
                AND (r.to_state = ''FAIL'')
            )

        UNION ALL

        /* 3. 
         *   NOT EXISTS techByDesignDisposition AND 
			 EXISTS Forward HTTP Request AND 
			 NOT EXISTS ((natue = Forwarded HTTP Response or Forwarded HTTP Response Replay) and to_state = COMPLETE) AND 
			 NOT EXISTS (nature = Forwarded HTTP Response Error or Forwarded HTTP Response Replay Error) and to_state = FAIL
         */
        SELECT
            ofp.hub_interaction_id,
            ofp.sat_interaction_fhir_request_id AS sat_interaction_http_request_id,
            ofp.tenant_id_lower AS qe_name,
            ofp.created_at,
            ''Forwarded HTTP Response is missing'' AS error_message
        FROM base_interactions bi
        JOIN techbd_udi_ingress.sat_interaction_fhir_request ofp  ON ofp.hub_interaction_id = bi.hub_interaction_id
        WHERE ofp.nature = ''Original FHIR Payload''
            AND NOT EXISTS (
                SELECT 1
                FROM techbd_udi_ingress.sat_interaction_fhir_request d
                WHERE d.hub_interaction_id = bi.hub_interaction_id
                AND d.nature = ''techByDesignDisposition''
            )
            AND EXISTS (
                SELECT 1
                FROM techbd_udi_ingress.sat_interaction_fhir_request d
                WHERE d.hub_interaction_id = bi.hub_interaction_id
                AND d.nature = ''Forward HTTP Request''
            )
            AND NOT EXISTS (
                SELECT 1
                FROM techbd_udi_ingress.sat_interaction_fhir_request r
                WHERE r.hub_interaction_id = bi.hub_interaction_id
                AND (r.to_state = ''COMPLETE'')
            )
            AND NOT EXISTS (
                SELECT 1
                FROM techbd_udi_ingress.sat_interaction_fhir_request r
                WHERE r.hub_interaction_id = bi.hub_interaction_id
                AND (r.to_state = ''FAIL'')
            )            
        ORDER BY 4 DESC'
    )
    USING start_date, end_date, tenant_id;
END;
$$ LANGUAGE plpgsql;


DROP FUNCTION if exists techbd_udi_ingress.get_missing_datalake_submission_details(text,date, date);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.get_missing_datalake_submission_details(
    tenant_id_param TEXT,
    start_date_param DATE,
    end_date_param DATE
)
RETURNS TABLE (
    hub_interaction_id text,
    sat_interaction_http_request_id text,
    qe_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    error_message text
) AS $$
BEGIN
    RETURN QUERY
    /*SELECT 
        sihr.hub_interaction_id,
        sihr.sat_interaction_fhir_request_id AS sat_interaction_http_request_id,
        sihr.tenant_id_lower AS qe_name,
        sihr.created_at,
		sihr.error_message
    FROM techbd_udi_ingress.sat_interaction_fhir_request sihr
    WHERE 
        sihr.tenant_id_lower = tenant_id_param
        AND sihr.nature = 'techByDesignDisposition'
        AND sihr.uri = ANY (ARRAY['/Bundle/', '/Bundle','/flatfile/csv/Bundle', '/flatfile/csv/Bundle/', '/ccda/Bundle', '/ccda/Bundle/', '/hl7v2/Bundle', '/hl7v2/Bundle/'])
        AND sihr.created_at >= TO_TIMESTAMP(start_date_param::text, 'YYYY-MM-DD') 
        AND sihr.created_at < TO_TIMESTAMP(end_date_param::text, 'YYYY-MM-DD') + INTERVAL '1 day'
        --AND NOT EXISTS (
        --    SELECT 1
        --    FROM techbd_udi_ingress.sat_interaction_fhir_request sihr2
        --    WHERE sihr2.hub_interaction_id = sihr.hub_interaction_id
        --      AND sihr2.nature = ANY (ARRAY['Forwarded HTTP Response', 'Forwarded HTTP Response Error'])
        --)
		AND techbd_disposition_action = 'discard'
    ORDER BY sihr.created_at DESC;*/

    WITH base_interactions AS (
        SELECT
            sihr_base.hub_interaction_id
        FROM techbd_udi_ingress.sat_interaction_fhir_request AS sihr_base
        WHERE sihr_base.nature = 'Original FHIR Payload'
          AND sihr_base.created_at >= start_date_param
          AND sihr_base.created_at < end_date_param + INTERVAL '1 day'
          AND sihr_base.tenant_id_lower = tenant_id_param
          AND sihr_base.uri = ANY (ARRAY[
              '/Bundle', '/Bundle/',
              '/flatfile/csv/Bundle', '/flatfile/csv/Bundle/',
              '/ccda/Bundle', '/ccda/Bundle/',
              '/hl7v2/Bundle', '/hl7v2/Bundle/'
          ])
    )

    -- 1️ Invalid bundles (discarded)
    SELECT 
        sihr1.hub_interaction_id,
        sihr1.sat_interaction_fhir_request_id,
        sihr1.tenant_id_lower,
        sihr1.created_at,
        sihr1.error_message
    FROM techbd_udi_ingress.sat_interaction_fhir_request AS sihr1
    JOIN base_interactions AS bi
      ON bi.hub_interaction_id = sihr1.hub_interaction_id
    WHERE sihr1.nature = 'techByDesignDisposition'
      AND sihr1.techbd_disposition_action = 'discard'

    /*UNION ALL

    -- 2️ Missing techByDesignDisposition
    SELECT
        ofp.hub_interaction_id,
        ofp.sat_interaction_fhir_request_id,
        ofp.tenant_id_lower,
        ofp.created_at,
        'techByDesignDisposition is missing'
    FROM base_interactions AS bi
    JOIN techbd_udi_ingress.sat_interaction_fhir_request AS ofp
      ON ofp.hub_interaction_id = bi.hub_interaction_id
     AND ofp.nature = 'Original FHIR Payload'
    WHERE NOT EXISTS (
        SELECT 1
        FROM techbd_udi_ingress.sat_interaction_fhir_request AS d
        WHERE d.hub_interaction_id = bi.hub_interaction_id
          AND d.nature = 'techByDesignDisposition'
    )*/

    --ORDER BY 4 DESC;
    ORDER BY sihr1.created_at DESC;
END;
$$ LANGUAGE plpgsql;


/* ----- -- Missing techByDesignDisposition -- ------*/
CREATE OR REPLACE FUNCTION techbd_udi_ingress.get_missing_techByDesignDisposition_details(
    tenant_id_param text,
    start_date_param date,
    end_date_param date
)
RETURNS TABLE (
    hub_interaction_id text,
    sat_interaction_http_request_id text,
    qe_name text,
    created_at timestamptz,
    error_message text
)
LANGUAGE plpgsql
AS $function$
BEGIN
    RETURN QUERY
    WITH base_interactions AS (
        SELECT
            sihr_base.hub_interaction_id
        FROM techbd_udi_ingress.sat_interaction_fhir_request AS sihr_base
        WHERE sihr_base.nature = 'Original FHIR Payload'
          AND sihr_base.created_at >= start_date_param
          AND sihr_base.created_at < end_date_param + INTERVAL '1 day'
          AND sihr_base.tenant_id_lower = tenant_id_param
          AND sihr_base.uri = ANY (ARRAY[
              '/Bundle', '/Bundle/',
              '/flatfile/csv/Bundle', '/flatfile/csv/Bundle/',
              '/ccda/Bundle', '/ccda/Bundle/',
              '/hl7v2/Bundle', '/hl7v2/Bundle/'
          ])
    )
    -- Missing techByDesignDisposition
    SELECT
        ofp.hub_interaction_id,
        ofp.sat_interaction_fhir_request_id,
        ofp.tenant_id_lower,
        ofp.created_at,
        'techByDesignDisposition is missing'
    FROM base_interactions AS bi
    JOIN techbd_udi_ingress.sat_interaction_fhir_request AS ofp
      ON ofp.hub_interaction_id = bi.hub_interaction_id
     AND ofp.nature = 'Original FHIR Payload'
    WHERE NOT EXISTS (
        SELECT 1
        FROM techbd_udi_ingress.sat_interaction_fhir_request AS d
        WHERE d.hub_interaction_id = bi.hub_interaction_id
          AND d.nature IN ('techByDesignDisposition', 'Forward HTTP Request')
    )
    AND NOT EXISTS (
        SELECT 1
        FROM techbd_udi_ingress.sat_interaction_fhir_request AS d
        WHERE d.hub_interaction_id = bi.hub_interaction_id
          AND d.to_state IN ('COMPLETE', 'FAIL')
    )	
    ORDER BY 4 DESC;
END;
$function$;
/* ----- --END of Missing techByDesignDisposition -- ------*/

DROP FUNCTION if exists techbd_udi_ingress.get_fhir_scn_submission_details(text, date, date);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.get_fhir_scn_submission_details(
    organization_id_param TEXT,
    start_date DATE,
    end_date DATE
)
RETURNS TABLE (
    organization_id TEXT,
    qe_name TEXT,
    qe_total_submissions BIGINT,
    success_submissions BIGINT,
    failed_submissions BIGINT,
    recently_created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY EXECUTE format(
        'WITH submission_counts AS (
             SELECT 
                sihr.organization_id,
                sihr.tenant_id_lower AS qe_name,
                count(CASE WHEN sihr.nature = ''Original FHIR Payload'' THEN 1 ELSE NULL END) AS qe_total_submissions, 
	 			count(
	                CASE 
	                     WHEN sihr.nature = ''Forwarded HTTP Response''
	                         OR (sihr.nature = ''Forwarded HTTP Response Error'' AND sihr.replay_status = ''True'')
	                    THEN 1 
					ELSE NULL
	                END
	            ) AS success_submissions,
	            count(
	                CASE 
	                    WHEN  (sihr.nature = ''Forwarded HTTP Response Error'' AND sihr.replay_status IS DISTINCT FROM ''True'' ) 
	                    THEN 1
					ELSE NULL
	                END
	            ) AS failed_submissions, 
                max(sihr.created_at) AS recently_created_at
             FROM 
                techbd_udi_ingress.sat_interaction_fhir_request sihr
             WHERE 
                sihr.uri = ANY (ARRAY[''/Bundle/'', ''/Bundle'',''/flatfile/csv/Bundle'', ''/flatfile/csv/Bundle/'', ''/ccda/Bundle'', ''/ccda/Bundle/'', ''/hl7v2/Bundle'', ''/hl7v2/Bundle/''])
	            AND sihr.created_at >= TO_TIMESTAMP($2::text, ''YYYY-MM-DD'') 
		        AND sihr.created_at < TO_TIMESTAMP($3::text, ''YYYY-MM-DD'') + INTERVAL ''1 day'' -- Parameters shifted by one
                AND sihr.tenant_id <> ''N/A''
                AND sihr.organization_id = $1  -- Organization ID filter
             GROUP BY 
                sihr.organization_id, sihr.tenant_id_lower
        )
        SELECT 
            organization_id,
            qe_name,
            qe_total_submissions,
            success_submissions,
            failed_submissions,
            recently_created_at
        FROM 
            submission_counts sc
        WHERE qe_total_submissions <> 0 
        ORDER BY 
            recently_created_at DESC'
    ) USING organization_id_param, start_date, end_date;  -- Pass all parameters
END;
$$ LANGUAGE plpgsql;

DROP VIEW IF EXISTS techbd_udi_ingress.interaction_csv_http_stat CASCADE;

/*******************************************************************************************
This view summarizes CSV-based HTTP ZIP file requests and their corresponding 
FHIR processing outcomes. It combines metadata from 
sat_interaction_zip_file_request with aggregated success and failure counts 
from sat_interaction_fhir_request.  

Key details included:
- Tenant identifiers, hub interaction ID, request URI, origin, and session info.  
- File statistics: total files in the ZIP and number of FHIR bundles generated.  
- FHIR outcome metrics: total responses, count of successful responses, 
  and count of failed responses.  
- Data quality indicators such as validation status, along with a normalized 
  status field for readability.  
- A helper field (`selected_field_for_download`) fixed to 'full_operation_outcome' 
  for downstream use.  

This view provides a consolidated pipeline-level summary of how each CSV ZIP 
upload was processed through the HTTP-to-FHIR conversion, enabling tracking 
of throughput, success vs. failure, and data validation results.
*******************************************************************************************/


DROP VIEW IF EXISTS techbd_udi_ingress.interaction_csv_http CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_csv_http AS
SELECT 
    sizfr.tenant_id_lower,
    sizfr.tenant_id,
    sizfr.hub_interaction_id,
    sizfr.created_at,
    sizfr.uri,
    sizfr.csv_zip_file_name,
    sizfr.origin,
    sizfr.sftp_session_id,
    INITCAP(REPLACE(LOWER(sizfr.status), '_', ' ')) AS status,
    sizfr.total_number_of_files_in_zip_file,
    sizfr.number_of_fhir_bundles_generated_from_zip_file, 
    sizfr.data_validation_status,
    'full_operation_outcome' AS selected_field_for_download,
    techbd_version_number
FROM
    techbd_udi_ingress.sat_interaction_zip_file_request sizfr ;


/*******************************************************************************************
This view takes data from sat_interaction_fhir_request table to 
provide a consolidated view of HTTP FHIR requests via CSV, including resource types, 
interaction details, request attributes, and validation issues.
******************************************************************************************/
DROP VIEW IF EXISTS techbd_udi_ingress.interaction_csv_http_fhir_request CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_csv_http_fhir_request
AS SELECT DISTINCT ON (bundle_id)
    hub_interaction_id AS interaction_id,
    uri,
    bundle_id,
    created_at AS interaction_created_at, 
    sat_interaction_fhir_request_id AS sat_interaction_http_request_id, 
    tenant_id, 
    created_at AS request_created_at,  
    patient_id,
    patient_mrn,     
    source_hub_interaction_id,
    CASE
        WHEN is_bundle_valid = true THEN 'Valid'
        WHEN is_bundle_valid = false THEN 'Invalid'
        ELSE NULL
    END AS is_bundle_valid
FROM techbd_udi_ingress.sat_interaction_fhir_request htt_req
WHERE source_type = 'CSV'
ORDER BY bundle_id, created_at DESC;


/*******************************************************************************************
  This view provides a consolidated view of failed CSV HTTP requests and associated interactions.
******************************************************************************************/
DROP VIEW IF EXISTS techbd_udi_ingress.interaction_csv_http_request_forward_failure;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_csv_http_request_forward_failure
AS SELECT sat_interaction_fhir_request_id,
    hub_interaction_id AS interaction_id,
    tenant_id,
    uri,
    bundle_id,
    nature,
    created_at AS interaction_created_at,
    created_by AS interaction_created_by,
    provenance AS interaction_provenance,
    client_ip_address,
    user_agent,
    from_state,
    to_state,
    outbound_http_message,
    error_message AS error,
    issues_count,
    resource_type_set AS resource_types,
    patient_id, 
    patient_mrn,
    concat_ws('-'::text, tenant_id, organization_id, patient_mrn) AS source_mrn,    
    source_hub_interaction_id
   FROM techbd_udi_ingress.sat_interaction_fhir_request intr_hreq
WHERE to_state = 'FAIL'::text AND source_type = 'CSV';

/*******************************************************************************************
This view summarizes CSV processing errors where ZIP file uploads could not be 
processed due to the "file_not_processed" category. It queries the 
sat_csv_fhir_processing_errors table, filtering specifically on 
category = 'file_not_processed'.  

For each ZIP file interaction (identified by zip_file_hub_interaction_id), 
it aggregates key metadata such as creation time, tenant, group, URI, error 
types, ZIP file name, origin, and user agent.  

The result provides a consolidated summary of unprocessed file errors, making 
it easier to identify failed uploads and investigate their root causes.
*******************************************************************************************/


DROP VIEW IF EXISTS techbd_udi_ingress.csv_file_not_processed_error_summary cascade;
CREATE OR REPLACE view techbd_udi_ingress.csv_file_not_processed_error_summary
AS 
SELECT
    scfpe.zip_file_hub_interaction_id,
    MIN(scfpe.created_at) AS created_at,
    MIN(scfpe.tenant_id) AS tenant_id,
    MIN(scfpe.group_id) AS group_id,
    MIN(scfpe.uri) AS uri,
    MIN(scfpe.error_type) AS error_types,
    MIN(scfpe.zip_file_name) AS zip_file_name,
    MIN(scfpe.origin) AS origin,
    MIN(scfpe.user_agent) AS user_agent
FROM
    techbd_udi_ingress.sat_csv_fhir_processing_errors scfpe 
where 
	scfpe.category = 'file_not_processed'    
GROUP BY
    scfpe.zip_file_hub_interaction_id ;

/*******************************************************************************************
This view lists detailed CSV processing errors where uploaded ZIP files could 
not be processed (category = 'file_not_processed'). It retrieves records from 
the sat_csv_fhir_processing_errors table without aggregation, providing a 
record-level view of each error.  

The output includes timestamps, tenant and group identifiers, interaction IDs, 
URIs, error type and subtype, descriptive messages, error details, file names, 
ZIP file names, origin, and user agent.  

This detailed view complements the summary view by offering full visibility 
into each unprocessed file error, supporting debugging and root cause analysis.
*******************************************************************************************/


DROP VIEW IF EXISTS techbd_udi_ingress.csv_file_not_processed_error cascade;
CREATE OR REPLACE view techbd_udi_ingress.csv_file_not_processed_error
AS 
SELECT
    scfpe.created_at,
    scfpe.tenant_id,
    scfpe.zip_file_hub_interaction_id,
    scfpe.group_id,
    scfpe.uri,
    scfpe.error_type,
    scfpe.error_subtype,
    scfpe.description,
    scfpe.error,
    scfpe.file_name,
    scfpe.zip_file_name,
    scfpe.origin,
    scfpe.user_agent
FROM
	techbd_udi_ingress.sat_csv_fhir_processing_errors scfpe 
where 
	scfpe.category = 'file_not_processed'       
ORDER BY
    scfpe.created_at DESC;  

/*******************************************************************************************
This view summarizes CSV processing errors related to the "incomplete_groups" 
category. It selects records from the sat_csv_fhir_processing_errors table 
filtered by category = 'incomplete_groups'.  

For each ZIP file interaction (identified by zip_file_hub_interaction_id), 
it aggregates metadata such as tenant ID, creation time, request URI, 
ZIP file name, and user agent.  

The result provides a consolidated summary of errors where uploaded CSV files 
contained incomplete or missing group data, making it easier to track and 
investigate problematic uploads.
*******************************************************************************************/

DROP VIEW IF EXISTS techbd_udi_ingress.csv_incomplete_groups_errors_summary cascade;
CREATE OR REPLACE view techbd_udi_ingress.csv_incomplete_groups_errors_summary
AS 
SELECT
    min(scfpe.tenant_id) as tenant_id,
    scfpe.zip_file_hub_interaction_id,
    min(scfpe.created_at) as created_at,  
    min(scfpe.uri) as uri, 
    min(scfpe.zip_file_name) as csv_zip_file_name, 
    min(scfpe.user_agent) as user_agent
FROM
	techbd_udi_ingress.sat_csv_fhir_processing_errors scfpe 
where 
	scfpe.category = 'incomplete_groups'    
GROUP BY
    scfpe.zip_file_hub_interaction_id; 

/*******************************************************************************************
This view lists detailed CSV processing errors categorized as "incomplete_groups". 
It retrieves record-level data from the sat_csv_fhir_processing_errors table 
filtered by category = 'incomplete_groups'.  

The output includes creation time, tenant ID, request URI, interaction IDs 
(for both ZIP file and flat file), group identifiers, error type, error message, 
and descriptive details.  

Unlike the summary view, this provides a granular view of each incomplete 
group error, helping with debugging and root cause analysis of CSV uploads 
that failed due to incomplete or missing group data.
*******************************************************************************************/



DROP VIEW IF EXISTS techbd_udi_ingress.csv_incomplete_groups_errors cascade;
CREATE OR REPLACE view techbd_udi_ingress.csv_incomplete_groups_errors
AS 
 select
	scfpe.created_at,
	scfpe.tenant_id,
	scfpe.uri,
	scfpe.zip_file_hub_interaction_id,
    scfpe.flat_file_hub_interaction_id as hub_interaction_id,
    scfpe.group_id,
	scfpe.error_type,
	scfpe.error,
	scfpe.description
FROM
	techbd_udi_ingress.sat_csv_fhir_processing_errors scfpe 
where 
	scfpe.category = 'incomplete_groups'   
ORDER BY
    created_at DESC; 	


/*******************************************************************************************
This view summarizes CSV processing errors related to "data_integrity" issues. 
It queries the sat_csv_fhir_processing_errors table filtered by 
category = 'data_integrity'.  

For each ZIP file interaction (identified by zip_file_hub_interaction_id), 
it aggregates key metadata including tenant ID, creation time, request URI, 
ZIP file name, and user agent.  

The result provides a consolidated summary of data integrity errors in CSV uploads, 
making it easier to identify and investigate problematic files that failed 
validation or integrity checks.
*******************************************************************************************/


DROP VIEW IF EXISTS techbd_udi_ingress.csv_data_integrity_error_summary cascade;
CREATE OR REPLACE view techbd_udi_ingress.csv_data_integrity_error_summary 
AS 
SELECT
    min(scfpe.tenant_id) as tenant_id,
    scfpe.zip_file_hub_interaction_id,
    min(scfpe.created_at) as created_at,  
    min(scfpe.uri) as uri, 
    min(scfpe.zip_file_name) as csv_zip_file_name, 
    min(scfpe.user_agent) as user_agent
FROM
	techbd_udi_ingress.sat_csv_fhir_processing_errors scfpe 
where 
	scfpe.category = 'data_integrity'    
GROUP BY
    scfpe.zip_file_hub_interaction_id; 


 
/*****************************************
This query retrieves interaction details from the techbd_udi_ingress.sat_interaction_fhir_request table. It includes interaction date, tenant ID (QE), IT vendor, SCN, type, and the count of interactions.

SCN is determined based on tenant_id and patient_mrn_source_system, defaulting to NULL if patient_mrn_source_system is NULL.
Filters exclude invalid bundles and specific URIs.
Results are grouped by key fields and sorted in ascending order.
*****************************************/
   
 drop view if exists techbd_udi_ingress.hrsn_data_tracker cascade;  
 create or replace view techbd_udi_ingress.hrsn_data_tracker as  
 SELECT  
    htt_req.created_at::date AS interaction_date, 
    htt_req.tenant_id AS qe_name,  
    case
	    WHEN htt_req.patient_mrn_source_system IS NULL THEN NULL
        WHEN tenant_id_lower = 'healtheconn' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'HAHRSN' THEN 'Healthy Alliance'
        WHEN tenant_id_lower = 'hixny' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'HAHRSN' THEN 'Healthy Alliance Foundation Inc.'
        WHEN tenant_id_lower = 'healtheconn' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'HVCCHRSN' THEN 'Hudson Valley Care Coalition, Inc.'
        WHEN tenant_id_lower = 'healtheconn' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'CCHRSN' THEN 'Care Compass'
        WHEN tenant_id_lower = 'healthix' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'HWCLI' THEN 'Health and Welfare Council of LI'
        WHEN tenant_id_lower = 'healthix' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'PHS' THEN 'Public Health Solutions'
        WHEN tenant_id_lower = 'healthix' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'SIPPS' THEN 'Staten Island PPS'
        WHEN tenant_id_lower = 'bronxrhio' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'SOMOSFH' THEN 'Somos Healthcare Providers, Inc.'
        WHEN tenant_id_lower = 'grrhio' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'FLIPAFH' THEN 'Finger Lakes IPA Inc.'
        WHEN tenant_id_lower = 'grrhio' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'FLIPATN' THEN 'Finger Lakes IPA Inc. (Monroe County Only)'
        WHEN tenant_id_lower = 'healthelink' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'WNYICCFH' THEN 'Western New York Integrated Care Collaborative, Inc.'
        ELSE SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5)
    END AS scn, 
    -- Determine IT Vendor based on SCN and QE
    case
        WHEN tenant_id_lower = 'healtheconn' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'HAHRSN' THEN 'Unite Us'
        WHEN tenant_id_lower = 'hixny' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'HAHRSN' THEN 'UniteUs'
        WHEN tenant_id_lower = 'healtheconn' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'HVCCHRSN' THEN 'Unite Us'
        WHEN tenant_id_lower = 'healtheconn' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'CCHRSN' THEN 'Unite Us'
        WHEN tenant_id_lower = 'healthix' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'HWCLI' THEN 'UniteUs'
        WHEN tenant_id_lower = 'healthix' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'PHS' THEN 'UniteUs'
        WHEN tenant_id_lower = 'healthix' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'SIPPS' THEN 'Channels360'
        WHEN tenant_id_lower = 'bronxrhio' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'SOMOSFH' THEN 'FindHelp'
        WHEN tenant_id_lower = 'grrhio' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'FLIPAFH' THEN 'FindHelp'
        WHEN tenant_id_lower = 'grrhio' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'FLIPATN' THEN 'TogetherNow'
        WHEN tenant_id_lower = 'healthelink' AND SPLIT_PART(htt_req.patient_mrn_source_system, '/', 5) = 'WNYICCFH' THEN 'FindHelp'
        ELSE ''
    END AS it_vendor, 
    htt_req.bundle_type AS type,
    COUNT(*) AS count
FROM techbd_udi_ingress.sat_interaction_fhir_request htt_req
WHERE 
    htt_req.uri =  ANY (ARRAY['/Bundle/', '/Bundle', '/flatfile/csv/Bundle', '/flatfile/csv/Bundle/', '/ccda/Bundle', '/ccda/Bundle/', '/hl7v2/Bundle', '/hl7v2/Bundle/']) 
    AND htt_req.is_bundle_valid = true  
    AND htt_req.nature = 'Forwarded HTTP Response'
GROUP BY 
    interaction_date, qe_name, it_vendor, scn, type 
ORDER BY 
    interaction_date DESC;

/*******************************************************************************************
This view takes data from sat_interaction_fhir_request table to 
provide a consolidated view of CCDA FHIR requests via HTTPS, including resource types, 
interaction details, request attributes, and validation issues.
******************************************************************************************/

DROP VIEW IF EXISTS techbd_udi_ingress.interaction_csv_ccda_fhir_request CASCADE;

/*******************************************************************************************
This view retrieves the most recent CCDA interaction per `hub_interaction_id` from the 
`sat_interaction_fhir_request` table. It includes metadata and payload details, along with 
a derived FHIR conversion status indicating whether a corresponding FHIR payload exists. 
Only the latest record per interaction is retained, and interactions are filtered by 
`source_type = 'CCDA'`.
*******************************************************************************************/


DROP VIEW IF EXISTS techbd_udi_ingress.interaction_ccda_request CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_ccda_request
AS 
WITH fhir_summary AS (
    SELECT
        hub_interaction_id,
        CASE
            WHEN COUNT(hub_interaction_id) > 0 THEN 'True'::text
            ELSE 'False'::text
        END AS fhir_conversion_status
    FROM techbd_udi_ingress.sat_interaction_fhir_request
    WHERE 
    nature = 'Original FHIR Payload'::text 
    AND source_type = 'CCDA'::text
    GROUP BY hub_interaction_id
),
ranked_sicr AS (
    SELECT
    sicr.sat_interaction_fhir_request_id,
    sicr.hub_interaction_id,
    sicr.tenant_id,
    sicr.tenant_id_lower,
    sicr.uri,
    sicr.nature, 
    sicr.client_ip_address,
    sicr.user_agent,
    sicr.from_state,
    sicr.to_state, 
    sicr.created_at,
	sicr.ig_version,
	sicr.techbd_version_number,
        ROW_NUMBER() OVER (PARTITION BY hub_interaction_id ORDER BY created_at DESC) AS rn
    FROM techbd_udi_ingress.sat_interaction_fhir_request sicr
    WHERE  source_type = 'CCDA'::text
)
SELECT
    sicr.sat_interaction_fhir_request_id AS sat_interaction_ccda_request_id,
    sicr.hub_interaction_id,
    sicr.tenant_id,
    sicr.tenant_id_lower,
    sicr.uri,
    sicr.nature,
    fhir_summary.fhir_conversion_status,
    sicr.client_ip_address,
    sicr.user_agent,
    sicr.from_state,
    sicr.to_state,
    'CCDA' AS origin,
    sicr.created_at,
	sicr.ig_version,
	sicr.techbd_version_number
FROM ranked_sicr sicr
JOIN fhir_summary ON sicr.hub_interaction_id = fhir_summary.hub_interaction_id
WHERE sicr.rn = 1 ;


/*******************************************************************************************
This view retrieves the most recent HL7V2 interaction per `hub_interaction_id` from the 
`sat_interaction_fhir_request` table. It includes metadata and payload details, along with 
a derived FHIR conversion status indicating whether a corresponding FHIR payload exists. 
Only the latest record per interaction is retained, and interactions are filtered by 
`source_type = 'HL7V2'`.
*******************************************************************************************/


DROP VIEW IF EXISTS techbd_udi_ingress.interaction_hl7v2_request CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_hl7v2_request
AS 
SELECT
    sicr.sat_interaction_fhir_request_id AS sat_interaction_fhir_request_id,
    sicr.hub_interaction_id,
    sicr.tenant_id,
    sicr.tenant_id_lower,
    sicr.uri,
    sicr.nature,
    'true' AS fhir_conversion_status,
    sicr.client_ip_address,
    sicr.user_agent,
    sicr.from_state,
    sicr.to_state,
    sicr.state_transition_reason,
    'HL7V2' AS origin,
    sicr.created_at,
    sicr.provenance,
	sicr.ig_version,
	sicr.techbd_version_number
FROM techbd_udi_ingress.sat_interaction_fhir_request sicr  
WHERE  
  source_type = 'HL7V2'::text ;

    

/*******************************************************************************************
This view selects the most recent CSV-based FHIR interaction per tenant from the 
`sat_interaction_fhir_request` table. It filters records where the source type is 'CSV' 
and returns the latest interaction details along with the total count of such interactions 
per tenant. Only the most recent interaction per tenant is retained.
*******************************************************************************************/

 DROP VIEW IF EXISTS techbd_udi_ingress.interaction_recent_csv_https CASCADE;
 CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_recent_csv_https AS
	SELECT DISTINCT ON (sihr.tenant_id_lower)
		sihr.sat_interaction_fhir_request_id AS sat_interaction_http_request_id,
		sihr.hub_interaction_id AS interaction_id,
		sihr.tenant_id,
		sihr.created_at AS interaction_created_at,
		sihr.tenant_id_lower,
		COUNT(*) OVER (PARTITION BY sihr.tenant_id_lower) AS interaction_count
	FROM techbd_udi_ingress.sat_interaction_fhir_request sihr
	WHERE sihr.source_type = 'CSV'
	ORDER BY sihr.tenant_id_lower, sihr.created_at DESC;  


/*****************************************
This view consolidates diagnostic data from the table 'sat_interaction_fhir_session_diagnostic'. 
It transforms and organizes key fields, including 'tenant_id', 'severity', 'message', 'ig_version', 
and 'validation_engine', along with converting the 'encountered_at' timestamp into a date format. 

The view aggregates data to calculate the count of issues ('issue_count') for each combination of 
'tenant_id', 'severity', 'message', 'ig_version', and 'validation_engine'. It provides an ordered 
output by the most recent encountered dates ('encountered_date') and tenant IDs for streamlined 
analysis or reporting.

If an existing view 'fhir_session_diagnostics' already exists, it is dropped before creating 
this new view definition to ensure consistency.
******************************************************************************************/

drop view if exists techbd_udi_ingress.fhir_session_diagnostics cascade;
create or replace view techbd_udi_ingress.fhir_session_diagnostics     
as
	select
	created_at::date as encountered_date,
	tenant_id,
	lower(severity) as severity,
	message as message,
	ig_version,
	MAX(validation_engine)  as validation_engine,
	COUNT(*) as issue_count
from
	techbd_udi_ingress.sat_interaction_fhir_session_diagnostic
	where lower(severity) in ('warning','error','fatal')
	and message != ''	
 	AND created_at >= (CURRENT_DATE - INTERVAL '7 days')

group by
	created_at::date,
	tenant_id,
	lower(severity),
	message,
	ig_version
	--validation_engine
order by
	created_at::date desc ;


/*****************************************
This view, 'fhir_session_diagnostics_details', extracts detailed diagnostic information 
from the 'sat_interaction_fhir_session_diagnostic' table. It includes fields such as 
'tenant_id', 'uri', 'bundle_id', 'session_id', 'severity', 'message', 'line', 'column', 
'diagnostics', and 'elaboration'. 

The 'encountered_at' timestamp is converted into a date format ('encountered_date') 
to simplify analysis. This view is intended to provide a comprehensive, detailed view 
of diagnostics for further investigation or reporting.

If an existing view 'fhir_session_diagnostics_details' already exists, it is dropped 
before creating the new view to ensure the latest definition is applied.
******************************************************************************************/

drop view if exists techbd_udi_ingress.fhir_session_diagnostics_details cascade;
create or replace view techbd_udi_ingress.fhir_session_diagnostics_details     
as
select
	tenant_id,
	uri,
	bundle_id,
	session_id,
	LOWER(severity) AS severity,
	message,
	line,
	"column",
	ig_version,
	validation_engine,
	diagnostics,
	to_char(created_at, 'MM-DD-YYYY') AS encountered_date,
	elaboration
from
	techbd_udi_ingress.sat_interaction_fhir_session_diagnostic intr_diagno
WHERE LOWER(severity) IN ('warning', 'error', 'fatal')
AND created_at >= (CURRENT_DATE - INTERVAL '7 days')
	AND message <> '';


/******************************************************************************************
The view 'ref_code_lookup_system_view' provides a simplified lookup of all 'system_value' entries associated with each code_type.
It aggregates data from the ref_code_lookup table by grouping on code_type, and generates a comma-separated string of system_values, ordered alphabetically.
******************************************************************************************/
DROP VIEW IF EXISTS techbd_udi_ingress.ref_code_lookup_system_view CASCADE;
CREATE VIEW techbd_udi_ingress.ref_code_lookup_system_view AS
SELECT 
    code_type,
    STRING_AGG(DISTINCT system_value, ', ' ORDER BY system_value) AS system_values
FROM techbd_udi_ingress.ref_code_lookup
GROUP BY code_type
ORDER BY code_type;

/******************************************************************************************
The function 'ref_code_lookup_upserted', Inserts a new entry into the `ref_code_lookup` table with the given parameters such as code type, code,
display string, system value, description, IG version, and provenance. If a conflict occurs, the insertion is skipped without raising an error.
******************************************************************************************/
DROP FUNCTION IF EXISTS techbd_udi_ingress.ref_code_lookup_upserted(text, text, text, text, text, text, text);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.ref_code_lookup_upserted(code_type text, code text, display_string text, system_value text, description text, ig_version text, provenance text DEFAULT 'TechBD'::text)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    -- Declare variables to hold error and extracted data
    v_error_msg			TEXT;
    v_error_type		TEXT;
    v_sqlstate			TEXT;
    v_pg_detail			TEXT;
    v_pg_hint			TEXT;
    v_pg_context		TEXT;
    v_exception_id		TEXT;
    v_return			TEXT;
BEGIN
    INSERT INTO techbd_udi_ingress.ref_code_lookup
		(ref_code_lookup_id, code_type, code, display_string, system_value, description, ig_version, created_at, created_by, provenance)
	VALUES ( 
		gen_random_uuid()::TEXT,
		ref_code_lookup_upserted.code_type, 
		ref_code_lookup_upserted.code, 
		ref_code_lookup_upserted.display_string, 
		ref_code_lookup_upserted.system_value, 
		ref_code_lookup_upserted.description, 
		ref_code_lookup_upserted.ig_version, 
		CURRENT_TIMESTAMP, CURRENT_USER,
		COALESCE(ref_code_lookup_upserted.provenance, 'TechBd'))
    ON CONFLICT DO NOTHING
	RETURNING ref_code_lookup.ref_code_lookup_id INTO v_return;
   		
    RETURN v_return;
EXCEPTION
   WHEN OTHERS THEN
        -- Capture exception details
        GET STACKED DIAGNOSTICS
            v_error_msg = MESSAGE_TEXT,
            v_sqlstate = RETURNED_SQLSTATE,
            v_pg_detail = PG_EXCEPTION_DETAIL,
            v_pg_hint = PG_EXCEPTION_HINT,
            v_pg_context = PG_EXCEPTION_CONTEXT;
        	v_error_type = 'SQL';

        RAISE NOTICE 'Error occurred: %', SQLERRM; /*Test purpose*/         
       
        -- Log the exception, reusing the previous exception ID if it exists
        PERFORM techbd_udi_ingress.register_issue(
            COALESCE(v_exception_id,NULL),
            'action_rule',
            v_error_msg,
            v_error_type,
            v_sqlstate,
            v_pg_detail,
            v_pg_hint,
            v_pg_context,
            CURRENT_USER,
            'TechBd');

        RETURN NULL;
END;
$function$
;

DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_ccda_request_upserted(text, text, text, text, text, text, jsonb, text, timestamptz, text, text, jsonb, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_ccda_request_upserted(text, text, text, text, text, text, jsonb, text, text, timestamptz, text, text, jsonb, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_ccda_request_upserted(text, text, text, text, text, text, jsonb, text, text, timestamptz, text, text, jsonb, text, text, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_interaction_ccda_request_upserted(text, text, text, text, text, text, jsonb, text, text, timestamptz, text, text, jsonb, text, text, text, text, text, text);

CREATE OR REPLACE FUNCTION techbd_udi_ingress.sat_interaction_ccda_request_upserted(hub_interaction_id text, tenant_id text, uri text, nature text, from_state text, to_state text, payload jsonb DEFAULT NULL::jsonb, ccda_payload_text text DEFAULT NULL::text, state_transition_reason text DEFAULT NULL::text, created_at timestamp with time zone DEFAULT NULL::timestamp with time zone, created_by text DEFAULT NULL::text, provenance text DEFAULT NULL::text, elaboration jsonb DEFAULT NULL::jsonb, client_ip_address text DEFAULT NULL::text, user_agent text DEFAULT NULL::text, origin text DEFAULT 'http'::text, p_techbd_version_number text DEFAULT NULL::text, p_file_name text DEFAULT NULL::text, p_ccda_authoring_device text DEFAULT NULL::text)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    -- Declare variables to hold error and extracted data
	v_error_msg 					TEXT;
    v_error_type 					TEXT;
    v_sqlstate 						TEXT;
    v_pg_detail 					TEXT;
    v_pg_hint 						TEXT;
    v_pg_context 					TEXT;   
    v_created_at 					TIMESTAMPTZ := COALESCE(created_at, CURRENT_TIMESTAMP);  -- Ensure created_at is not null
    v_created_by 					TEXT 		:= COALESCE(created_by, current_user);  -- Default created_by to the current user if not provided
    v_provenance 					TEXT 		:= COALESCE(provenance, 'CCDA');  -- Set default provenance
    v_exception_id 					TEXT		:= gen_random_uuid()::text;
    
    -- Additional variables for extracting and holding data from the payload JSONB
    v_return 						TEXT 	:= NULL;
	v_return_status					BOOLEAN := false;
	v_is_exception					BOOLEAN := false;
	v_elaboration 					JSONB 	:= NULL;
	v_elaboration_steps 			JSONB	:= '[]'::jsonb;
	v_validation_error 				TEXT 	:= NULL;
	v_steps_text      				TEXT[] 	:= ARRAY[]::text[];

BEGIN
	v_steps_text := array_append(v_steps_text, '0: In sat_interaction_ccda_request_upserted function');

	IF NOT EXISTS(SELECT 1 FROM techbd_udi_ingress.hub_interaction t
  		WHERE t.hub_interaction_id = sat_interaction_ccda_request_upserted.hub_interaction_id 
  		AND t."key" = sat_interaction_ccda_request_upserted.uri) THEN
  		v_return_status := true;
		v_return := NULL;
		v_error_msg := 'hub_interaction_id is not in the hub_interaction table.';
  	END IF;
  
	IF NOT v_return_status THEN
		BEGIN
		  	-- The INSERT statement inserts a new record into the sat_interaction_ccda_request table, generating a UUID for the primary key.
		    -- If the operation succeeds, the generated UUID is returned.
		    INSERT INTO techbd_udi_ingress.sat_interaction_ccda_request (
		        sat_interaction_ccda_request_id,
		        hub_interaction_id,
		        tenant_id,
		        tenant_id_lower,
		        uri,
		        nature,
		        payload,
				ccda_payload_text,
		        from_state,
		        to_state,
		        state_transition_reason,
				created_at,
		        created_by,
		        provenance,
		        elaboration,
		        client_ip_address, 
		        user_agent,
		        origin,
				techbd_version_number,
				file_name,
				ccda_authoring_device
		    )
		    VALUES (
		        gen_random_uuid()::TEXT,  -- Generate a unique UUID for the primary key
		        hub_interaction_id,
		        tenant_id,
		        LOWER(tenant_id),  -- Store the tenant ID in lowercase for consistency
		        uri,
		        nature,
		        payload,
				ccda_payload_text,
		        from_state,
		        to_state,
		        state_transition_reason,
				v_created_at,
		        v_created_by,
		        v_provenance,
		        elaboration,
		        client_ip_address, 
		        user_agent,
		        COALESCE(origin, 'http'),
				p_techbd_version_number,
				p_file_name,
				p_ccda_authoring_device
		    ) RETURNING sat_interaction_ccda_request_id INTO v_return;  -- Return the generated UUID
	
			v_steps_text := array_append(v_steps_text, '1: Inserted into sat_interaction_ccda_request');

			IF (nature = 'CCDA Validation Result') THEN
				UPDATE techbd_udi_ingress.sat_interaction_ccda_request AS t
				SET    ccda_authoring_device = p_ccda_authoring_device 
				WHERE  t.hub_interaction_id = sat_interaction_ccda_request_upserted.hub_interaction_id AND t.nature = 'Original CCDA Payload';
			END IF;

			v_validation_error := (payload -> 'OperationOutcome' -> 'validationResults' -> 0-> 'operationOutcome' -> 'issue' -> 0 -> 'details' ->> 'text')::text;
			IF (nature = 'CCDA Validation Result' AND to_state = 'VALIDATION_FAILED' AND 
				v_validation_error IS NOT NULL AND v_validation_error != '')
			THEN
				INSERT INTO techbd_udi_ingress.sat_interaction_ccda_validation_errors(
			          sat_ccda_validation_errors_id, 
			          hub_interaction_id, 
			          tenant_id, 
			          uri, 
			          error_type, 
			          "error", 
			          file_name, 
			          origin, 
			          user_agent, 
			          techbd_version_number, 
			          created_at, 
			          created_by, 
			          provenance)
			      VALUES(
			          gen_random_uuid()::TEXT,  -- Generate a unique UUID for the primary key
			          hub_interaction_id,
			          tenant_id,
			          uri,
			          'ccda_validation_errors',
			          v_validation_error,
			          p_file_name,
			          COALESCE(origin, 'http'),
			          user_agent,
			          p_techbd_version_number,
			          v_created_at,
			          v_created_by,
			          v_provenance);
		
				v_steps_text := array_append(v_steps_text, '2: Inserted into sat_interaction_ccda_validation_errors');
		    END IF;
		EXCEPTION
		   WHEN OTHERS THEN
			    -- Capture exception details
			    GET STACKED DIAGNOSTICS
			        v_error_msg = MESSAGE_TEXT,
			        v_sqlstate = RETURNED_SQLSTATE,
			        v_pg_detail = PG_EXCEPTION_DETAIL,
			        v_pg_hint = PG_EXCEPTION_HINT,
			        v_pg_context = PG_EXCEPTION_CONTEXT;
			        v_error_type = 'SQL';
	
				v_is_exception := true;				
				v_return_status := true;
				v_return := NULL;
		END;
 	END IF;
	-- Save the log details into table
	v_elaboration := jsonb_build_object(
	         						'hub_interaction_id', hub_interaction_id,
									'interaction_key', uri,
	         						'nature', nature,
									'tenant_id', tenant_id,
	         						'from_state', from_state,
	         						'to_state', to_state,
									'techbd_version_number', p_techbd_version_number,
									'file_name', p_file_name,
									'ccda_authoring_device', p_ccda_authoring_device,
									'client_ip_address', client_ip_address,
									'sat_id', v_return
	         						);

	-- convert steps to JSONB and attach
    IF array_length(v_steps_text, 1) IS NOT NULL THEN
        v_elaboration_steps := to_jsonb(v_steps_text);
        v_elaboration := v_elaboration || jsonb_build_object('steps', v_elaboration_steps);
    END IF;

    -- attach error message if present
    IF v_error_msg IS NOT NULL THEN
        v_elaboration := v_elaboration || jsonb_build_object('error_message', v_error_msg);
    END IF;

    CALL techbd_udi_ingress.register_diagnostic_log(
			v_exception_id, uri, v_created_by, v_provenance, 'info'::text, 'sat_interaction_ccda_request_upserted function Logs'::text, 
			v_created_by, 'success'::text, NULL::text, 0, v_elaboration, tenant_id, hub_interaction_id
		);

	IF v_is_exception = true THEN    
        v_exception_id := techbd_udi_ingress.register_issue(
            v_exception_id, uri, v_error_msg, v_error_type, v_sqlstate, v_pg_detail, v_pg_hint, v_pg_context, v_created_by, v_provenance, v_elaboration, tenant_id, hub_interaction_id
        );
	END IF;
   	RETURN v_return;
END;
$function$
;


/*******************************************************************************************
This view selects the most recent CCDA-based FHIR interaction per tenant from the 
`sat_interaction_fhir_request` table. It filters records where the source type is 'CCDA' 
and returns the latest interaction details along with the total count of such interactions 
per tenant. Only the most recent interaction per tenant is retained.
*******************************************************************************************/

 DROP VIEW IF EXISTS techbd_udi_ingress.interaction_recent_ccda_https CASCADE;
 CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_recent_ccda_https AS
 SELECT DISTINCT ON (sihr.tenant_id_lower)
    sihr.sat_interaction_fhir_request_id AS sat_interaction_http_request_id,
    sihr.hub_interaction_id AS interaction_id,
    sihr.tenant_id,
    sihr.created_at AS interaction_created_at,
    sihr.tenant_id_lower,
    COUNT(*) OVER (PARTITION BY sihr.tenant_id_lower) AS interaction_count
FROM techbd_udi_ingress.sat_interaction_fhir_request sihr
WHERE sihr.source_type = 'CCDA'
ORDER BY sihr.tenant_id_lower, sihr.created_at DESC;


/*******************************************************************************************
This view takes data from sat_interaction_flat_file_csv_request table to 
provide a consolidated view of CSV file interactions via HTTPS, including 
interaction details, tenant information, and associated file payloads.
*******************************************************************************************/
DROP VIEW IF EXISTS techbd_udi_ingress.interaction_csv_http_stat_file_data CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_csv_http_stat_file_data AS
SELECT
    zip_file_hub_interaction_id,
    nature,
    tenant_id,
    tenant_id_lower,
    hub_interaction_id,
    demographic_data_payload_text,
    qe_admin_data_payload_text,
    demographic_data_file_name,
    qe_admin_data_file_name,
    screening_observation_data_payload_text,
    screening_profile_data_payload_text,
    screening_observation_data_file_name,
    screening_profile_data_file_name
FROM
    techbd_udi_ingress.sat_interaction_flat_file_csv_request
WHERE 
    demographic_data_file_name != '' OR
    qe_admin_data_file_name != '' OR
    screening_observation_data_file_name != '' OR
    screening_profile_data_file_name != '';

DROP FUNCTION IF EXISTS techbd_udi_ingress.get_validation_response(TEXT, TEXT);

CREATE OR REPLACE FUNCTION techbd_udi_ingress.get_validation_response(interaction_type TEXT, interaction_id TEXT) 
RETURNS JSONB
LANGUAGE plpgsql
AS $function$
DECLARE
	v_payload			JSONB := NULL;
	v_zip_generalErrors	TEXT  := NULL;
    v_error_msg 		TEXT;
    v_error_type 		TEXT;
    v_sqlstate 			TEXT;
    v_pg_detail 		TEXT;
    v_pg_hint 			TEXT;
    v_pg_context 		TEXT;
BEGIN
	
	CASE interaction_type
		WHEN 'CSV' THEN
			IF NOT EXISTS (SELECT 1
					FROM techbd_udi_ingress.sat_interaction_flat_file_csv_request 
					WHERE hub_interaction_id = interaction_id) THEN 
				RETURN jsonb_build_object('error', interaction_id::TEXT || ' is not a valid ' || interaction_type::TEXT || ' interaction id.');
			ELSE
				RETURN CASE WHEN nature = 'Converted to FHIR' AND to_state = 'CONVERTED_TO_FHIR' THEN 
								jsonb_build_object('payload', validation_result_payload, 'message', 'Converted to FHIR')
							ELSE validation_result_payload 
						END
					FROM techbd_udi_ingress.sat_interaction_flat_file_csv_request 
					WHERE nature IN ('Converted to FHIR', 'CSV Validation Result') 
					  AND hub_interaction_id = interaction_id 
					  AND validation_result_payload IS NOT NULL 
					  AND validation_result_payload != '{}'
					ORDER BY --nature = 'CSV Validation Result' 
						CASE 
						    WHEN nature = 'Converted to FHIR' AND to_state = 'FHIR_CONVERTION_FAILED' THEN 1
						    WHEN nature = 'CSV Validation Result' THEN 2
						    ELSE 3  -- Other cases (like "Converted to FHIR" with "CONVERTED_TO_FHIR")
						END
					LIMIT 1;
			END IF;
		WHEN 'FHIR' THEN
			IF NOT EXISTS (SELECT 1
					FROM techbd_udi_ingress.sat_interaction_fhir_request 
					WHERE hub_interaction_id = interaction_id) THEN 
				RETURN jsonb_build_object('error', interaction_id::TEXT || ' is not a valid ' || interaction_type::TEXT || ' interaction id.');
			ELSE
				RETURN payload 
					FROM techbd_udi_ingress.sat_interaction_fhir_request 
					WHERE nature ='techByDesignDisposition' 
					  AND hub_interaction_id = interaction_id 
					  AND payload IS NOT NULL AND payload != '{}';
			END IF;
		WHEN 'ZIP' THEN
			IF NOT EXISTS (SELECT 1
					FROM techbd_udi_ingress.sat_interaction_zip_file_request 
					WHERE hub_interaction_id = interaction_id) THEN 
				RETURN jsonb_build_object('error', interaction_id::TEXT || ' is not a valid ' || interaction_type::TEXT || ' interaction id.');
			END IF;
			WITH 
				flat_file AS (
					SELECT 
				        hub_interaction_id AS flat_file_hub_interaction_id, 
				        /*jsonb_strip_nulls(jsonb_build_array(
				            demographic_data_file_name, NULL,
				            qe_admin_data_file_name, NULL,
				            screening_observation_data_file_name, 
				            screening_profile_data_file_name
				        )) AS validatedCsvFiles, */
						(
					        SELECT jsonb_agg(file_name)
					        FROM (
					            SELECT demographic_data_file_name AS file_name
					            UNION ALL
					            SELECT qe_admin_data_file_name
					            UNION ALL
					            SELECT screening_observation_data_file_name
					            UNION ALL
					            SELECT screening_profile_data_file_name
					        ) AS subquery
					        WHERE file_name IS NOT NULL
					    ) AS validatedCsvFiles,
				        to_state, nature,
				        format('/flatfile/csv/Bundle/status?interactionType=CSV&interactionId=%s', hub_interaction_id) AS csvStatusEndpoint
				    FROM techbd_udi_ingress.sat_interaction_flat_file_csv_request 
				    WHERE zip_file_hub_interaction_id = interaction_id
				),
				failed_validation AS (
				    SELECT flat_file_hub_interaction_id, validatedCsvFiles, csvStatusEndpoint
				    FROM flat_file
				    WHERE to_state = 'VALIDATION_FAILED'
				),
				successful_validation AS (
				    SELECT flat_file_hub_interaction_id, validatedCsvFiles, csvStatusEndpoint
				    FROM flat_file
				    WHERE to_state = 'VALIDATION_SUCCESS'
				),
				zip_file_details AS (
					SELECT general_errors, status
					FROM techbd_udi_ingress.sat_interaction_zip_file_request 
					WHERE hub_interaction_id = interaction_id LIMIT 1
				),
				failed_fhir AS (
				    SELECT flat_file_hub_interaction_id, validatedCsvFiles, csvStatusEndpoint
				    FROM flat_file
				    WHERE nature = 'Converted to FHIR' AND to_state = 'FHIR_CONVERSION_FAILED'
				),
				successful_fhir AS (
				    SELECT DISTINCT 
				        fhir.hub_interaction_id AS fhir_hub_interaction_id, 
				        ff.flat_file_hub_interaction_id,
				        ff.validatedCsvFiles, 
				        format('/flatfile/csv/Bundle/status?interactionType=FHIR&interactionId=%s', fhir.hub_interaction_id) AS fhirStatusEndpoint
				    FROM techbd_udi_ingress.sat_interaction_fhir_request fhir
				    JOIN flat_file ff ON fhir.group_hub_interaction_id = ff.flat_file_hub_interaction_id
				    WHERE ff.nature = 'Converted to FHIR' AND ff.to_state = 'CONVERTED_TO_FHIR' AND fhir.nature = 'Original FHIR Payload'
				    AND UPPER(fhir.source_type) = 'CSV' AND fhir.source_hub_interaction_id = interaction_id
				)
				SELECT jsonb_build_object( 
					'zipFileInteractionId', interaction_id,
					'status', (SELECT status FROM zip_file_details),
				    'csvValidationResults', jsonb_build_object(
				        'failedCsvInteractions', (SELECT jsonb_agg(jsonb_build_object(
							'message', 'Csv Validation Failed.Check the status endpoint for the details of failure',
				            'csvInteractionId', flat_file_hub_interaction_id,
				            'validatedCsvFiles', validatedCsvFiles,
				            'statusEndpoint', csvStatusEndpoint
				        )) FROM failed_validation),
				        'successfulCsvInteractions', (SELECT jsonb_agg(jsonb_build_object(
				            'message', 'Csv Validation is Successful',
				            'csvInteractionId', flat_file_hub_interaction_id,
				            'validatedCsvFiles', validatedCsvFiles,
				            'statusEndpoint', csvStatusEndpoint,
							'fhirProcessingResults', jsonb_build_object(
						        'failedFhirConversions', (SELECT jsonb_agg(jsonb_build_object(
						        	'message', 'Could not convert to FHIR.Check the status endpoint for the details of failure'
						        )) FROM failed_fhir),
						        'convertedToFhir', (SELECT jsonb_agg(jsonb_build_object(
						        	'fhirInteractionId', fhir_hub_interaction_id,
						            'statusEndpoint', fhirStatusEndpoint
						        )) FROM successful_fhir)
						    )
				        )) FROM successful_validation)
				    ),
				    /*'fhirProcessingResults', jsonb_build_object(
				        'failedFhirConversions', (SELECT jsonb_agg(jsonb_build_object(
				        	'message', 'Could not convert to FHIR.Check the status endpoint for the details of failure',
				            'csvInteractionId', flat_file_hub_interaction_id,
				            'validatedCsvFiles', validatedCsvFiles,
				            'statusEndpoint', csvStatusEndpoint
				        )) FROM failed_fhir),
				        'convertedToFhir', (SELECT jsonb_agg(jsonb_build_object(
				        	'fhirInteractionId', fhir_hub_interaction_id,
				            'csvInteractionId', flat_file_hub_interaction_id,
				            'validatedCsvFiles', validatedCsvFiles,
				            'statusEndpoint', fhirStatusEndpoint
				        )) FROM successful_fhir)
				    ),*/
				    'generalErrors', (SELECT general_errors FROM zip_file_details) 
				) INTO v_payload;
			RETURN v_payload;
		ELSE 
			RETURN NULL;
	END CASE;
EXCEPTION
   WHEN OTHERS THEN
	    -- Capture exception details
	    GET STACKED DIAGNOSTICS
	        v_error_msg  = MESSAGE_TEXT,
	        v_sqlstate   = RETURNED_SQLSTATE,
	        v_pg_detail  = PG_EXCEPTION_DETAIL,
	        v_pg_hint 	 = PG_EXCEPTION_HINT,
	        v_pg_context = PG_EXCEPTION_CONTEXT;
	        v_error_type = 'SQL';
	
	    RAISE NOTICE 'Error occurred: %', SQLERRM; 
		RETURN NULL;
END;
$function$;    


/*
 * Function: sat_diagnostic_dataledger_api_upserted
 * Schema: techbd_udi_ingress
 * Purpose: This function upserts records into the `sat_diagnostic_dataledger_api` table 
 *          based on the provided `hub_interaction_id`. It first checks if the interaction 
 *          exists in `hub_interaction` and then either inserts a new record or updates an 
 *          existing one. The function also logs exceptions using `register_issue`.
 * 
 * Parameters:
 *   - hub_interaction_id: Unique identifier for the interaction.
 *   - dataledger_url: URL of the DataLedger API.
 *   - source_hub_interaction_id: (Optional) Source interaction ID.
 *   - group_hub_interaction_id: (Optional) Group interaction ID.
 *   - received_payload: (Optional) JSON payload received.
 *   - received_status: (Optional) Status of the received payload.
 *   - received_reason: (Optional) Reason for received status.
 *   - dataledger_received_status_code: (Optional) Status code from DataLedger.
 *   - dataledger_received_response: (Optional) Response from DataLedger.
 *   - sent_payload: (Optional) JSON payload sent.
 *   - sent_status: (Optional) Status of the sent payload.
 *   - sent_reason: (Optional) Reason for sent status.
 *   - dataledger_sent_response: (Optional) Response received from DataLedger after sending.
 *   - dataledger_sent_status_code: (Optional) Status code of the sent response.
 *   - source: (Optional) Source system of the data.
 *   - additional_details: (Optional) Additional metadata.
 *   - created_at: (Optional) Timestamp of record creation, defaults to CURRENT_TIMESTAMP.
 *   - created_by: (Optional) User who created the record, defaults to current_user.
 *   - provenance: (Optional) Data provenance identifier.
 * 
 * Returns:
 *   - The hub_interaction_id if the upsert is successful, otherwise NULL.
 */

DROP FUNCTION IF EXISTS techbd_udi_ingress.sat_diagnostic_dataledger_api_upserted(text, text, text, text, jsonb, text, text, text, jsonb, jsonb, text, text, jsonb, text, text, jsonb, timestamptz, text, text);

CREATE OR REPLACE FUNCTION techbd_udi_ingress.sat_diagnostic_dataledger_api_upserted(
	hub_interaction_id 					text, 
	dataledger_url 						text, 
	source_hub_interaction_id 			text 	DEFAULT NULL::text, 
	group_hub_interaction_id 			text 	DEFAULT NULL::text, 
	received_payload 					jsonb 	DEFAULT NULL::jsonb, 
	received_status 					text 	DEFAULT NULL::text, 
	received_reason 					text 	DEFAULT NULL::text, 
	dataledger_received_status_code 	text 	DEFAULT NULL::text, 
	dataledger_received_response 		jsonb 	DEFAULT NULL::jsonb,
	sent_payload 						jsonb 	DEFAULT NULL::jsonb,
	sent_status 						text 	DEFAULT NULL::text,
	sent_reason 						text 	DEFAULT NULL::text,
	dataledger_sent_response 			jsonb 	DEFAULT NULL::jsonb,
	dataledger_sent_status_code 		text 	DEFAULT NULL::text,
	source 								text 	DEFAULT NULL::text,
	additional_details 					jsonb 	DEFAULT NULL::jsonb, 
	created_at 							timestamp with time zone DEFAULT NULL::timestamp with time zone, 
	created_by 							text 	DEFAULT NULL::text, 
	provenance 							text 	DEFAULT NULL::text
)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    -- Declare variables to hold error and extracted data
	v_error_msg 		TEXT;
    v_error_type 		TEXT;
    v_sqlstate 			TEXT;
    v_pg_detail 		TEXT;
    v_pg_hint 			TEXT;
    v_pg_context 		TEXT; 
    v_created_at 		TIMESTAMPTZ := COALESCE(created_at, CURRENT_TIMESTAMP);  -- Ensure created_at is not null
    v_created_by 		TEXT 		:= COALESCE(created_by, current_user);  -- Default created_by to the current user if not provided
    v_provenance 		TEXT 		:= COALESCE(provenance, 'unknown');  -- Set default provenance
    v_exception_id 		TEXT;    
    v_return 			TEXT 		:= NULL;
	v_diagnostics_key	TEXT 		:= 'dataledger_api_diagnostics_info';
	v_diagnostics_id	TEXT 		:= NULL;

	v_dataledger_url 					TEXT 		:= NULL;
	v_source_hub_interaction_id 		TEXT 		:= NULL;
	v_group_hub_interaction_id 			TEXT 		:= NULL;
	v_received_payload 					jsonb		:= NULL;
	v_received_status 					TEXT 		:= NULL;
	v_received_reason 					TEXT 		:= NULL;
	v_dataledger_received_status_code 	TEXT 		:= NULL;
	v_dataledger_received_response 		jsonb		:= NULL;
	v_sent_payload 						jsonb		:= NULL;
	v_sent_status 						TEXT 		:= NULL;
	v_sent_reason 						TEXT 		:= NULL;
	v_dataledger_sent_response 			jsonb		:= NULL;
	v_dataledger_sent_status_code 		TEXT 		:= NULL;
	v_source 							TEXT 		:= NULL;
	v_additional_details				jsonb		:= NULL;
BEGIN	
	IF NOT EXISTS(SELECT 1 FROM techbd_udi_ingress.hub_interaction t
  		WHERE t.hub_interaction_id = sat_diagnostic_dataledger_api_upserted.hub_interaction_id) THEN
  		RETURN NULL;
  	END IF;
 
	IF NOT EXISTS(SELECT 1 FROM techbd_udi_ingress.sat_diagnostic_dataledger_api t
  		WHERE t.hub_interaction_id = sat_diagnostic_dataledger_api_upserted.hub_interaction_id) THEN

		-- Insert into hub_diagnostics table
	    INSERT INTO techbd_udi_ingress.hub_diagnostic (
	        hub_diagnostic_id,
	        "key",
	        created_by,
	        provenance
	    ) VALUES (
	        gen_random_uuid()::TEXT,  -- Generate a unique UUID for the primary key
	        v_diagnostics_key,
	        v_created_by,
	        v_provenance
	    )
		ON CONFLICT (hub_diagnostic_id) DO NOTHING
		RETURNING hub_diagnostic_id INTO v_diagnostics_id;

	  	INSERT INTO techbd_udi_ingress.sat_diagnostic_dataledger_api (
	        sat_diagnostic_dataledger_api_id,
			hub_diagnostic_id,
			hub_interaction_id,
			dataledger_url,	
			source_hub_interaction_id,
			group_hub_interaction_id,
			received_payload,	
			received_status,
			received_reason,
			dataledger_received_status_code,
			dataledger_received_response,	
			sent_payload,
			sent_status,
			sent_reason,
			dataledger_sent_response,
			dataledger_sent_status_code,
			source,
			additional_details,
			created_at,
			created_by,
			provenance
	    )
	    VALUES (
	        gen_random_uuid()::TEXT,  -- Generate a unique UUID for the primary key
			v_diagnostics_id,
	        sat_diagnostic_dataledger_api_upserted.hub_interaction_id,
			sat_diagnostic_dataledger_api_upserted.dataledger_url, 
			sat_diagnostic_dataledger_api_upserted.source_hub_interaction_id,
			sat_diagnostic_dataledger_api_upserted.group_hub_interaction_id,
			sat_diagnostic_dataledger_api_upserted.received_payload,
			sat_diagnostic_dataledger_api_upserted.received_status,
			sat_diagnostic_dataledger_api_upserted.received_reason,
			sat_diagnostic_dataledger_api_upserted.dataledger_received_status_code, 
			sat_diagnostic_dataledger_api_upserted.dataledger_received_response,
			sat_diagnostic_dataledger_api_upserted.sent_payload,
			sat_diagnostic_dataledger_api_upserted.sent_status,
			sat_diagnostic_dataledger_api_upserted.sent_reason,
			sat_diagnostic_dataledger_api_upserted.dataledger_sent_response,
			sat_diagnostic_dataledger_api_upserted.dataledger_sent_status_code,
			sat_diagnostic_dataledger_api_upserted.source,
			sat_diagnostic_dataledger_api_upserted.additional_details,
			v_created_at,
			v_created_by,
			v_provenance
	    ) RETURNING sat_diagnostic_dataledger_api_id INTO v_return;
		  v_return := sat_diagnostic_dataledger_api_upserted.hub_interaction_id;

	ELSE
		SELECT dl.dataledger_url,	
			dl.source_hub_interaction_id,
			dl.group_hub_interaction_id,
			dl.received_payload,	
			dl.received_status,
			dl.received_reason,
			dl.dataledger_received_status_code,
			dl.dataledger_received_response,	
			dl.sent_payload,
			dl.sent_status,
			dl.sent_reason,
			dl.dataledger_sent_response,
			dl.dataledger_sent_status_code,
			dl.source,
			dl.additional_details
		INTO v_dataledger_url,
			v_source_hub_interaction_id,
			v_group_hub_interaction_id,
			v_received_payload,
			v_received_status,
			v_received_reason,
			v_dataledger_received_status_code,
			v_dataledger_received_response,
			v_sent_payload,
			v_sent_status,
			v_sent_reason,
			v_dataledger_sent_response,
			v_dataledger_sent_status_code,
			v_source,
			v_additional_details
		FROM techbd_udi_ingress.sat_diagnostic_dataledger_api dl
		WHERE dl.hub_interaction_id = sat_diagnostic_dataledger_api_upserted.hub_interaction_id; 

		UPDATE techbd_udi_ingress.sat_diagnostic_dataledger_api d
		SET 
		    dataledger_url 					= COALESCE(sat_diagnostic_dataledger_api_upserted.dataledger_url, v_dataledger_url),
		    source_hub_interaction_id 		= COALESCE(sat_diagnostic_dataledger_api_upserted.source_hub_interaction_id, v_source_hub_interaction_id),
		    group_hub_interaction_id 		= COALESCE(sat_diagnostic_dataledger_api_upserted.group_hub_interaction_id, v_group_hub_interaction_id),
		    received_payload 				= COALESCE(sat_diagnostic_dataledger_api_upserted.received_payload, v_received_payload),
		    received_status 				= COALESCE(sat_diagnostic_dataledger_api_upserted.received_status, v_received_status),
		    received_reason 				= COALESCE(sat_diagnostic_dataledger_api_upserted.received_reason, v_received_reason),
		    dataledger_received_status_code = COALESCE(sat_diagnostic_dataledger_api_upserted.dataledger_received_status_code, v_dataledger_received_status_code),
		    dataledger_received_response 	= COALESCE(sat_diagnostic_dataledger_api_upserted.dataledger_received_response, v_dataledger_received_response),
		    sent_payload 					= COALESCE(sat_diagnostic_dataledger_api_upserted.sent_payload, v_sent_payload),
		    sent_status 					= COALESCE(sat_diagnostic_dataledger_api_upserted.sent_status, v_sent_status),
		    sent_reason 					= COALESCE(sat_diagnostic_dataledger_api_upserted.sent_reason, v_sent_reason),
		    dataledger_sent_response 		= COALESCE(sat_diagnostic_dataledger_api_upserted.dataledger_sent_response, v_dataledger_sent_response),
		    dataledger_sent_status_code 	= COALESCE(sat_diagnostic_dataledger_api_upserted.dataledger_sent_status_code, v_dataledger_sent_status_code),
		    source 							= COALESCE(sat_diagnostic_dataledger_api_upserted.source, v_source),
		    additional_details 				= COALESCE(sat_diagnostic_dataledger_api_upserted.additional_details, v_additional_details),
		    created_at 						= v_created_at,
		    created_by 						= v_created_by,
		    provenance 						= v_provenance
		WHERE
		    d.hub_interaction_id = sat_diagnostic_dataledger_api_upserted.hub_interaction_id; -- Unique identifier to locate the row

		v_return := sat_diagnostic_dataledger_api_upserted.hub_interaction_id;
	END IF;

    RETURN v_return;
EXCEPTION
   WHEN OTHERS THEN
	    -- Capture exception details
	    GET STACKED DIAGNOSTICS
	        v_error_msg = MESSAGE_TEXT,
	        v_sqlstate = RETURNED_SQLSTATE,
	        v_pg_detail = PG_EXCEPTION_DETAIL,
	        v_pg_hint = PG_EXCEPTION_HINT,
	        v_pg_context = PG_EXCEPTION_CONTEXT;
	        v_error_type = 'SQL';
	
	    RAISE NOTICE 'Error occurred: %', SQLERRM; /*Test purpose*/         
	       
	    -- Log the exception, reusing the previous exception ID if it exists
		v_exception_id := techbd_udi_ingress.register_issue(
        COALESCE(v_exception_id,NULL),
		sat_diagnostic_dataledger_api_upserted.dataledger_url,
		v_error_msg,
		v_error_type,
		v_sqlstate,
		v_pg_detail,
		v_pg_hint,
		v_pg_context,
		v_created_by,
		v_provenance, null::jsonb, null::text, sat_diagnostic_dataledger_api_upserted.hub_interaction_id);
		RETURN NULL;
END;
$function$
;

/*******************************************************************************************
This view, `ref_code_lookup_code_view`, provides grouped and structured reference code data 
from the `ref_code_lookup` table for use in UIs or APIs.

It performs the following:
- Aggregates all reference codes (`code`, `display_string`) grouped by `code_type` 
  into JSON arrays using `json_agg`.
- Adds special groups:
  - `ombRaceCategory`: includes only race codes where `description` is 'ombCategory'.
  - `ombEthnicityCategory`: includes only ethnicity codes with the same description.

Each row in the view corresponds to a `code_type` (or one of the special categories), and the 
`codes` column holds an array of JSON objects with `code` and `display` keys, sorted by code.
*******************************************************************************************/


DROP VIEW IF EXISTS techbd_udi_ingress.ref_code_lookup_code_view CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.ref_code_lookup_code_view
AS
SELECT code_type,
    json_agg(json_build_object('code', code, 
    						   'display', display_string,
    						   'system', system_value
    						  ) ORDER BY code) AS codes
   FROM techbd_udi_ingress.ref_code_lookup
  GROUP BY code_type
UNION ALL
 SELECT 'ombRaceCategory'::text AS code_type,
    json_agg(json_build_object('code', code, 
    						   'display', display_string,
    						   'system', system_value
    						  ) ORDER BY code) AS codes
   FROM techbd_udi_ingress.ref_code_lookup
  WHERE description = 'ombCategory'::text AND code_type = 'race'::text
UNION ALL
 SELECT 'ombEthnicityCategory'::text AS code_type,
    json_agg(json_build_object('code', code, 
    						   'display', display_string,
    						   'system', system_value
    						  ) ORDER BY code) AS codes
   FROM techbd_udi_ingress.ref_code_lookup
  WHERE description = 'ombCategory'::text AND code_type = 'ethnicity'::text;


/*******************************************************************************************
register_interaction_nexus_ingestion -  function used in t he nexus integration to register an HTTP interaction request in the 
    hub_nexus_interaction and sat_nexus_interaction_ingestion tables, storing metadata, payload, and diagnostics.
    It ensures error logging, traceability, and prevents duplicate hub entries while returning a JSONB response with interaction details.
*******************************************************************************************/
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_nexus_ingestion(text, text, text, bytea, jsonb, text, text, text, text, text, text, text, text, jsonb, jsonb, jsonb, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_nexus_ingestion(text, text, text, bytea, jsonb, text, text, text, text, text, text, text, text, jsonb, jsonb, jsonb, text, timestamptz, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_nexus_ingestion(text, text, text, bytea, jsonb, text, text, text, text, text, text, text, text, jsonb, jsonb, jsonb, text, timestamptz, text, text);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.register_interaction_nexus_ingestion(
    p_hub_nexus_interaction_id text,
    p_request_uri text,
    p_request_url text,
    p_payload bytea,
    p_nature jsonb,
    p_content_type text DEFAULT NULL,
    p_payload_size text DEFAULT NULL,
    p_original_file_name text DEFAULT NULL,
    p_from_state text DEFAULT NULL,
    p_to_state text DEFAULT NULL,
    p_user_agent text DEFAULT NULL,
    p_user_id text DEFAULT NULL,
    p_client_ip_address text DEFAULT NULL,
    p_additional_details jsonb DEFAULT NULL,
    p_general_errors jsonb DEFAULT NULL,
    p_elaboration jsonb DEFAULT NULL,
    p_created_by text DEFAULT CURRENT_USER,
    p_created_at timestamp with time zone DEFAULT NULL::timestamp with time zone, 
    p_provenance text DEFAULT NULL,
	p_techbd_version_number text DEFAULT NULL::text
)
RETURNS jsonb
LANGUAGE plpgsql
AS $$
DECLARE
    v_elaboration 		jsonb;
    v_error_msg 		text;
    v_sqlstate 			text;
    v_pg_detail 		text;
    v_pg_hint 			text;
    v_pg_context 		text;
    v_error_type 		text;
    v_sat_nexus_id 		text 	:= gen_random_uuid()::text;
	v_exception_id 		text 	:= gen_random_uuid()::text;
	v_return 			JSONB 	:= NULL;
	v_return_status		BOOLEAN := false;
	v_is_exception		BOOLEAN := false;
	v_nature_denorm 	text 	:= trim(p_nature->>'nature');
	v_tenant_id_denorm 	text 	:= p_nature->>'tenant_id';
	v_created_at 		TIMESTAMPTZ := COALESCE(p_created_at, CURRENT_TIMESTAMP);
	v_user_id 			TEXT  		:= COALESCE(p_user_id, CURRENT_USER);
	v_elaboration_steps JSONB		:= '[]'::jsonb;
	v_elaboration_steps_text TEXT[] := ARRAY[]::text[]; 
    v_elaboration_steps_jsonb JSONB := '[]'::jsonb;
	v_provenance		TEXT 		:= 'Nexus';
BEGIN
    BEGIN
		/*
		  This function registers an HTTP interaction request in the hub_nexus_interaction and sat_nexus_interaction_ingestion tables, storing metadata, payload, and diagnostics.
		  It ensures error logging, traceability, and prevents duplicate hub entries while returning a JSONB response with interaction details.
		*/		
		
		v_elaboration_steps_text := array_append(v_elaboration_steps_text, '0: In register_interaction_nexus_ingestion function');

        -- Insert into hub_nexus_interaction if not exists
        IF NOT EXISTS (
            SELECT 1 FROM techbd_udi_ingress.hub_nexus_interaction
            WHERE hub_nexus_interaction_id = p_hub_nexus_interaction_id
        ) THEN
            BEGIN
                INSERT INTO techbd_udi_ingress.hub_nexus_interaction (
                    hub_nexus_interaction_id, "key", created_at, created_by, provenance
                ) VALUES (
                    p_hub_nexus_interaction_id, p_request_uri, v_created_at, p_created_by, p_provenance
                );

				v_elaboration_steps_text := array_append(v_elaboration_steps_text, '1: Inserted into hub_nexus_interaction');
		
            EXCEPTION WHEN OTHERS THEN
                GET STACKED DIAGNOSTICS
                    v_error_msg = MESSAGE_TEXT,
                    v_sqlstate = RETURNED_SQLSTATE,
                    v_pg_detail = PG_EXCEPTION_DETAIL,
                    v_pg_hint = PG_EXCEPTION_HINT,
                    v_pg_context = PG_EXCEPTION_CONTEXT;
                v_error_type := 'SQL';

                v_is_exception := true;				
				v_return_status := true;
				v_return := jsonb_build_object('error', v_error_msg, 
								  	 'hub_nexus_interaction_id', p_hub_nexus_interaction_id);
            END;
        END IF;

		IF NOT v_return_status THEN
	        -- Insert into sat_nexus_interaction_ingestion
	        INSERT INTO techbd_udi_ingress.sat_nexus_interaction_ingestion (
	            sat_nexus_interaction_ingestion_id,
	            hub_nexus_interaction_id,
	            tenant_id,
	            request_uri,
	            request_url,
	            payload,
	            nature,
	            content_type,
	            payload_hash,
	            payload_size,
	            original_file_name,
	            from_state,
	            to_state,
	            user_agent,
	            client_ip_address,
	            additional_details,
	            general_errors,
	            elaboration,
	            created_at,
	            created_by,
	            provenance,
				techbd_version_number
	        ) VALUES (
	            v_sat_nexus_id,
	            p_hub_nexus_interaction_id,
	            v_tenant_id_denorm,
	            p_request_uri,
	            p_request_url,
	            p_payload,
	            v_nature_denorm,
	            p_content_type,
	            md5(p_payload),
	            p_payload_size,
	            p_original_file_name,
	            p_from_state,
	            p_to_state,
	            p_user_agent,
	            p_client_ip_address,
	            p_additional_details,
	            p_general_errors,
	            p_elaboration,
	            v_created_at,
	            p_created_by,
	            p_provenance,
				p_techbd_version_number
	        );
	
	        v_elaboration_steps_text := array_append(v_elaboration_steps_text, '2: Inserted into sat_nexus_interaction_ingestion');
	
	        v_return := jsonb_build_object('hub_nexus_interaction_id', p_hub_nexus_interaction_id);
		END IF;
    EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS
            v_error_msg = MESSAGE_TEXT,
            v_sqlstate = RETURNED_SQLSTATE,
            v_pg_detail = PG_EXCEPTION_DETAIL,
            v_pg_hint = PG_EXCEPTION_HINT,
            v_pg_context = PG_EXCEPTION_CONTEXT;
        v_error_type := 'SQL';

        v_is_exception := true;		         	
		v_return := jsonb_build_object('error', v_error_msg, 
								  	 'hub_nexus_interaction_id', p_hub_nexus_interaction_id);
    END;

	-- Save the log details into table
	v_elaboration := jsonb_build_object(
							'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
							'interaction_key', p_request_uri,
							'nature', v_nature_denorm,
							'tenant_id', v_tenant_id_denorm,
							'from_state', p_from_state,
							'to_state', p_to_state,
							'techbd_version_number', p_techbd_version_number
							);
	-- Convert steps array to JSONB and attach
    IF array_length(v_elaboration_steps_text, 1) IS NOT NULL THEN
        v_elaboration_steps_jsonb := to_jsonb(v_elaboration_steps_text);
        v_elaboration := v_elaboration || jsonb_build_object('steps', v_elaboration_steps_jsonb);
    END IF;

    -- Attach error message if present
    IF v_error_msg IS NOT NULL THEN
        v_elaboration := v_elaboration || jsonb_build_object('error_message', v_error_msg);
    END IF;

    -- Register diagnostic log
    CALL techbd_udi_ingress.register_diagnostic_log(v_exception_id, p_request_uri, v_user_id, v_provenance, 'info'::text, 
       																'register_interaction_nexus_ingestion Logs'::text, v_user_id, 'success'::text, NULL::text, 0, v_elaboration, v_tenant_id_denorm, p_hub_nexus_interaction_id);

	IF v_is_exception = true THEN    
        -- Log the exception, reusing the previous exception ID if it exists
        v_elaboration := v_elaboration || jsonb_build_array(jsonb_build_object('error_message', v_error_msg));
        v_exception_id := techbd_udi_ingress.register_issue(
            v_exception_id, p_request_uri, v_error_msg, v_error_type, v_sqlstate, v_pg_detail, v_pg_hint, v_pg_context, v_user_id, v_provenance, v_elaboration, v_tenant_id_denorm, p_hub_nexus_interaction_id
        );
	END IF;
   	RETURN v_return;
END;
$$;

/*******************************************************************************************
register_interaction_fhir_request -  This function handles the registration of FHIR interaction requests into various hub and satellite tables within 
    the techbd_udi_ingress schema. Handles optional linkage to a nexus interaction, logs FHIR request details, and saves user information if provided. 	
    It first attempts to insert a record into the hub_interaction table, handling unique constraint violations based on the hub_upsert_behavior flag.
    It then logs request details into the sat_interaction_http_request table, including payload, state transitions, and user information.
    Based on the interaction key and request nature, it conditionally calls upsert functions for user, HTTP, or FHIR data. In case of an error, the 
    function logs the exception using the register_issue function.
    Returns a JSONB object representing either the inserted data or an error.
    - origin: http/sftp
    - source_type: fhir
*******************************************************************************************/
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_fhir_request(text, text, jsonb, jsonb, text, text, text, jsonb, text, text, text, text, text, text, text, text, text, text, bool, text, text, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_fhir_request(text, text, jsonb, jsonb, text, text, text, jsonb, text, text, text, text, text, text, text, text, text, timestamptz, text, bool, text, text, text, text, text, json);
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_fhir_request(text, text, jsonb, jsonb, text, text, text, jsonb, text, text, text, text, text, text, text, text, text, timestamptz, text, bool, text, text, text, text, text, jsonb);
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_fhir_request(text, text, jsonb, jsonb, text, text, text, jsonb, text, text, text, text, text, text, text, text, text, timestamptz, text, bool, text, text, text, text, text, jsonb, text);

CREATE OR REPLACE FUNCTION techbd_udi_ingress.register_interaction_fhir_request(
	p_interaction_id text, 
	p_interaction_key text, 
	p_nature jsonb, 
	p_payload jsonb, 
	p_hub_nexus_interaction_id text DEFAULT NULL::text, 
	p_payload_text text DEFAULT NULL::text, 
	p_rule_namespace text DEFAULT NULL::text, 
	p_elaboration jsonb DEFAULT NULL::jsonb, 
	p_content_type text DEFAULT NULL::text, 
	p_from_state text DEFAULT NULL::text, 
	p_to_state text DEFAULT NULL::text, 
	p_state_transition_reason text DEFAULT NULL::text, 
	p_user_id text DEFAULT NULL::text, 
	p_user_name text DEFAULT NULL::text, 
	p_user_session text DEFAULT NULL::text, 
	p_user_role text DEFAULT NULL::text, 
	p_created_by text DEFAULT CURRENT_USER, 
	p_created_at timestamp with time zone DEFAULT NULL::timestamp with time zone, 
	p_provenance text DEFAULT NULL::text, 
	p_hub_upsert_behavior boolean DEFAULT true, 
	p_source_hub_interaction_id text DEFAULT NULL::text, 
	p_origin text DEFAULT NULL::text, 
	p_source_type text DEFAULT NULL::text, 
	p_group_hub_interaction_id text DEFAULT NULL::text,
	p_request_source text DEFAULT NULL::text,
	p_additional_details jsonb DEFAULT NULL::jsonb,
	p_techbd_version_number text DEFAULT NULL::text)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_error_msg 		TEXT;
    v_error_type 		TEXT;
    v_sqlstate 			TEXT;
    v_pg_detail 		TEXT;
    v_pg_hint 			TEXT;
    v_pg_context 		TEXT;
    v_created_at 		TIMESTAMPTZ := COALESCE(p_created_at, CURRENT_TIMESTAMP);
	v_created_by 		TEXT 		:= COALESCE(p_created_by, CURRENT_USER);
	v_provenance 		TEXT 		:= COALESCE(p_provenance, 'FHIR');
    v_exception_id 		TEXT		:= gen_random_uuid()::text;
   	v_nature_denorm 	TEXT		:= trim(p_nature->>'nature');
   	v_tenant_id_denorm 	TEXT		:= p_nature->>'tenant_id';   
	
   	v_from_state				TEXT;
   	v_to_state					TEXT;
   	v_state_transition_reason 	TEXT;
   	v_nature 					JSONB;   
   	v_return 					JSONB 	:= NULL;
	v_return_status				BOOLEAN := false;
	v_is_exception				BOOLEAN := false;
   	v_disposition_json			JSONB 	:= NULL;
   	v_payload					JSONB 	:= NULL;
    v_elaboration 				JSONB 	:= NULL;
	v_elaboration_steps_text 	TEXT[]  := ARRAY[]::text[]; 
	v_elaboration_steps_jsonb 	JSONB 	:= '[]'::jsonb;
	v_user_id 					TEXT  	:= COALESCE(p_user_id, CURRENT_USER);	
	v_source_hub_interaction_id TEXT  	:= CASE 
								            WHEN p_interaction_key IN ('/ccda/Bundle', '/ccda/Bundle/', '/hl7v2/Bundle', '/hl7v2/Bundle/') THEN p_interaction_id
								            ELSE p_source_hub_interaction_id
								           END;
BEGIN 
	/*
	 This function handles the registration of FHIR interaction requests into various hub and satellite tables within the techbd_udi_ingress schema.
	 Handles optional linkage to a nexus interaction, logs FHIR request details, and saves user information if provided. 	
	 It first attempts to insert a record into the hub_interaction table, handling unique constraint violations based on the hub_upsert_behavior flag.
	 It then logs request details into the sat_interaction_http_request table, including payload, state transitions, and user information.
	 Based on the interaction key and request nature, it conditionally calls upsert functions for user, HTTP, or FHIR data. In case of an error, the 
	 function logs the exception using the register_issue function.
	 Returns a JSONB object representing either the inserted data or an error.
	 - origin: http/sftp
	 - source_type: fhir/hl7/csv/ccda
	*/

	v_elaboration_steps_text := array_append(v_elaboration_steps_text, '0: In register_interaction_fhir_request function');
    
	IF v_nature_denorm NOT IN(
			'Original FHIR Payload', 
			'techByDesignDisposition', 
			'Forward HTTP Request', 
			'Forwarded HTTP Response', 
			'Forwarded HTTP Response Error', 
			'Forwarded HTTP Request Replay',
			'Forwarded HTTP Response Replay',
			'Forwarded HTTP Response Replay Error'
	) THEN
		v_return_status := true;
		v_return := jsonb_build_object('error', 'Not a valid FHIR nature',
								  'payload', p_payload,
								  'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
								  'interaction_id', p_interaction_id);
	END IF;
		
	-- Check hub_nexus_interaction exists
    IF NOT v_return_status AND p_hub_nexus_interaction_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM techbd_udi_ingress.hub_nexus_interaction
        WHERE hub_nexus_interaction_id = p_hub_nexus_interaction_id
    ) THEN
        v_return_status := true;
		v_return := jsonb_build_object('error', 'p_hub_nexus_interaction_id does not exists in the hub_nexus_interaction table', 
								  'payload', p_payload,
								  'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
								  'interaction_id', p_interaction_id);
    END IF;

	IF NOT v_return_status THEN
	    -- Attempt to insert into hub_interaction
	        INSERT INTO techbd_udi_ingress.hub_interaction (hub_interaction_id, key, created_at, created_by, provenance)
	        VALUES (p_interaction_id, p_interaction_key, v_created_at, v_created_by, v_provenance)
			ON CONFLICT (hub_interaction_id) DO NOTHING;

	        v_elaboration_steps_text := array_append(v_elaboration_steps_text, '1: Inserted into hub_interaction');  
	
		-- Insert into link table
		IF NOT v_return_status AND p_hub_nexus_interaction_id IS NOT NULL then
			INSERT INTO techbd_udi_ingress.link_nexus_interaction (
				hub_nexus_interaction_id, hub_interaction_id, created_by, created_at, provenance
			) VALUES (
				p_hub_nexus_interaction_id, p_interaction_id, v_created_by, v_created_at, v_provenance
			) ON CONFLICT DO NOTHING;

			v_elaboration_steps_text := array_append(v_elaboration_steps_text, '2: Inserted into link_nexus_interaction');
		END IF;
		
		IF NOT v_return_status THEN
		    -- Attempt to insert into sat_interaction_http_request
		    BEGIN  
			    -- The insertion to sat_interaction_http_request table needs only once. Need to be corrected later.
		        INSERT INTO techbd_udi_ingress.sat_interaction_http_request (
		            sat_interaction_http_request_id, hub_interaction_id, nature, content_type, payload, payload_text, from_state, 
		            to_state, state_transition_reason, elaboration, created_at, created_by, provenance, nature_denorm, tenant_id_denorm, request_source, techbd_version_number, tenant_id
		        )
		        VALUES (
		            gen_random_uuid()::text, p_interaction_id, p_nature, p_content_type, p_payload, p_payload_text, p_from_state, p_to_state, 
		            p_state_transition_reason, p_elaboration, v_created_at, v_created_by, v_provenance, v_nature_denorm, v_tenant_id_denorm, p_request_source, p_techbd_version_number, v_tenant_id_denorm
		        );

		        v_elaboration_steps_text := array_append(v_elaboration_steps_text, '3: Inserted into sat_interaction_http_request');

		       	--1) Call the function with the defined parameters to upsert user info
		       	IF(v_nature_denorm = 'Original FHIR Payload') THEN
		
			    	PERFORM techbd_udi_ingress.sat_interaction_user_upserted(
				        hub_interaction_id 			=> p_interaction_id,
				        hub_interaction_key			=> p_interaction_key, 
			            nature						=> v_nature_denorm,
				        payload						=> p_payload, 
				        user_id						=> p_user_id, 
				        user_name					=> p_user_name, 
				        user_session				=> p_user_session, 
				        user_role					=> p_user_role,
				        tenant_id					=> v_tenant_id_denorm,
				        elaboration					=> p_elaboration,
				        created_at 					=> v_created_at,
				        created_by 					=> v_created_by,
				        provenance 					=> v_provenance,
						p_additional_details		=> COALESCE(p_additional_details, '{}'::jsonb) || jsonb_build_object('version', p_techbd_version_number) /*Add the version number to the additional_details json*/
			       	); 
					
			      	v_elaboration_steps_text := array_append(v_elaboration_steps_text, '4: Called sat_interaction_user_upserted');			        	
			    END IF;
			   
				v_payload := p_payload;
				IF (v_nature_denorm = 'techByDesignDisposition') THEN
						v_disposition_json := techbd_udi_ingress.process_json_action_rules(
							p_payload,
							p_rule_namespace,
							p_interaction_key
						);
					
						v_elaboration_steps_text := array_append(v_elaboration_steps_text, '5: Get the techByDesignDisposition json after process_json_action_rules');
						v_payload = v_disposition_json;
				END IF;
		
			    --2.1) Call the function with the defined parameters to upsert interaction FHIR request info.
			   		PERFORM techbd_udi_ingress.sat_interaction_fhir_request_upserted(
			   			p_hub_interaction_id 			=> p_interaction_id,
				        p_tenant_id 					=> v_tenant_id_denorm,
				        p_uri 							=> p_interaction_key,
				        p_nature 						=> v_nature_denorm,
				        p_from_state 					=> p_from_state,
				        p_to_state 						=> p_to_state,
				        p_payload						=> v_payload,
				        p_state_transition_reason		=> p_state_transition_reason,
				        p_created_at 					=> v_created_at,
				        p_created_by 					=> v_created_by,
				        p_provenance 					=> v_provenance,
				        p_elaboration					=> p_elaboration,	     
				      	p_source_type					=> p_source_type, 
				      	p_source_hub_interaction_id		=> v_source_hub_interaction_id,
				      	p_group_hub_interaction_id		=> p_group_hub_interaction_id,
						p_additional_details			=> p_additional_details,
						p_techbd_version_number			=> p_techbd_version_number
			       	);
			       	v_elaboration_steps_text := array_append(v_elaboration_steps_text, '6: Inserted into sat_interaction_fhir_request');														
			    
				-- Update dashboard_widget_metadata
		       	IF (v_nature_denorm = 'Original FHIR Payload') THEN
					PERFORM techbd_udi_ingress.upsert_dashboard_widget_metadata(
					    CASE 
				            WHEN p_interaction_key IN ('/Bundle', '/Bundle/', '/Bundle/$validate', '/Bundle/$validate/') THEN 'FHIR'
				            WHEN p_interaction_key IN ('/flatfile/csv/Bundle', '/flatfile/csv/Bundle/') THEN 'CSV'
				            WHEN p_interaction_key IN ('/ccda/Bundle', '/ccda/Bundle/') THEN 'CCDA'
				            WHEN p_interaction_key IN ('/hl7v2/Bundle', '/hl7v2/Bundle/') THEN 'HL7V2'
				            ELSE NULL
				        END,
					    v_tenant_id_denorm,
						v_created_at, 
						v_created_by);
				END IF;
		
				v_return := jsonb_build_object('payload', CASE WHEN v_nature_denorm = 'techByDesignDisposition' THEN v_payload ELSE NULL END,
											 'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
											 'interaction_id', p_interaction_id);
			
				v_elaboration_steps_text := array_append(v_elaboration_steps_text, '7: Get the return json');															
			    
			EXCEPTION
		        WHEN OTHERS THEN
		            -- Capture exception details
		            GET STACKED DIAGNOSTICS
		                v_error_msg = MESSAGE_TEXT,
		                v_sqlstate = RETURNED_SQLSTATE,
		                v_pg_detail = PG_EXCEPTION_DETAIL,
		                v_pg_hint = PG_EXCEPTION_HINT,
		                v_pg_context = PG_EXCEPTION_CONTEXT;
		                v_error_type = 'SQL';
		
		         	v_is_exception := true;		         	
					v_return := jsonb_build_object('error', v_error_msg, 
										  	 'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
											 'interaction_id', p_interaction_id);
		    END;
		END IF;
	END IF;

 	-- Save the log details into table
	v_elaboration := jsonb_build_object(
		         						'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
		         						'interaction_id', p_interaction_id,
		         						'interaction_key', p_interaction_key,
		         						'nature', v_nature_denorm,
										'tenant_id', v_tenant_id_denorm,
		         						'from_state', p_from_state,
		         						'to_state', p_to_state,
		         						'disposition_json', v_disposition_json::TEXT,
										'techbd_version_number', p_techbd_version_number
		         						);

	-- Convert steps array to JSONB and attach
    IF array_length(v_elaboration_steps_text, 1) IS NOT NULL THEN
        v_elaboration_steps_jsonb := to_jsonb(v_elaboration_steps_text);
        v_elaboration := v_elaboration || jsonb_build_object('steps', v_elaboration_steps_jsonb);
    END IF;

    -- Attach error message if present
    IF v_error_msg IS NOT NULL THEN
        v_elaboration := v_elaboration || jsonb_build_object('error_message', v_error_msg);
    END IF;

    -- Register diagnostic log
    CALL techbd_udi_ingress.register_diagnostic_log(
        v_exception_id,
        p_interaction_key,
        v_created_by,
        v_provenance,
        'info'::text,
        'register_interaction_fhir_request Logs'::text,
        COALESCE(p_user_id, v_created_by),
        (CASE WHEN v_is_exception THEN 'failure' ELSE 'success' END),
        NULL::text,
        0,
        v_elaboration,
        v_tenant_id_denorm,
        p_interaction_id
    );

    -- If exception occurred, register issue
    IF v_is_exception THEN
		v_exception_id := techbd_udi_ingress.register_issue(
            v_exception_id, p_interaction_key, v_error_msg, v_error_type, v_sqlstate, v_pg_detail, v_pg_hint, v_pg_context, v_created_by, v_provenance, v_elaboration, v_tenant_id_denorm, p_interaction_id
        );
    END IF;
   	RETURN v_return;
END;
$function$
;

/*******************************************************************************************
register_interaction_ccda_request -  This function handles the registration of CCDA interaction requests into various hub and satellite tables within 
    the techbd_udi_ingress schema. Handles optional linkage to a nexus interaction, logs FHIR request details, and saves user information if provided. 	
    It first attempts to insert a record into the hub_interaction table, handling unique constraint violations based on the hub_upsert_behavior flag.
    It then logs request details into the sat_interaction_http_request table, including payload, state transitions, and user information.
    Based on the interaction key and request nature, it conditionally calls upsert functions for user, HTTP, or FHIR data. In case of an error, the 
    function logs the exception using the register_issue function.
    Returns a JSONB object representing either the inserted data or an error.
    - origin: http/sftp
    - source_type: ccda
*******************************************************************************************/
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_ccda_request(text, text, jsonb, text, text, text, jsonb, text, text, jsonb, text, text, text, text, text, text, text, text, bool, text, text, text, text, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_ccda_request(text, text, jsonb, text, text, text, jsonb, text, text, jsonb, text, text, text, text, text, text, text, timestamptz, text, bool, text, text, text, text, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_ccda_request(text, text, jsonb, text, text, text, jsonb, text, text, jsonb, text, text, text, text, text, text, text, timestamptz, text, bool, text, text, text, text, text, text, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_ccda_request(text, text, jsonb, text, text, text, jsonb, text, text, jsonb, text, text, text, text, text, text, text, timestamptz, text, bool, text, text, text, text, text, text, text, text, text, text);

CREATE OR REPLACE FUNCTION techbd_udi_ingress.register_interaction_ccda_request(
	p_interaction_id text, 
	p_interaction_key text, 
	p_nature jsonb,
	p_from_state text, 
	p_to_state text, 
	p_hub_nexus_interaction_id text DEFAULT NULL::text,
	p_payload jsonb DEFAULT NULL::jsonb, 
	p_payload_text text DEFAULT NULL::text, 
	p_rule_namespace text DEFAULT NULL::text, 
	p_elaboration jsonb DEFAULT NULL::jsonb, 
	p_content_type text DEFAULT NULL::text, 
	p_state_transition_reason text DEFAULT NULL::text, 
	p_user_id text DEFAULT NULL::text, 
	p_user_name text DEFAULT NULL::text, 
	p_user_session text DEFAULT NULL::text, 
	p_user_role text DEFAULT NULL::text, 
	p_created_by text DEFAULT CURRENT_USER, 
	p_created_at timestamp with time zone DEFAULT NULL::timestamp with time zone,
	p_provenance text DEFAULT NULL::text, 
	p_hub_upsert_behavior boolean DEFAULT true, 
	p_source_hub_interaction_id text DEFAULT NULL::text, 
	p_client_ip_address text DEFAULT NULL::text, 
	p_user_agent text DEFAULT NULL::text, 
	p_origin text DEFAULT NULL::text, 
	p_source_type text DEFAULT NULL::text, 
	p_group_hub_interaction_id text DEFAULT NULL::text,
	p_request_source text DEFAULT NULL::text,
	p_techbd_version_number text DEFAULT NULL::text,
	p_file_name text DEFAULT NULL::text,
    p_ccda_authoring_device text DEFAULT NULL::text)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_error_msg       TEXT;
    v_error_type      TEXT;
    v_sqlstate        TEXT;
    v_pg_detail       TEXT;
    v_pg_hint         TEXT;
    v_pg_context      TEXT;
	v_created_at      TIMESTAMPTZ 	:= COALESCE(p_created_at, CURRENT_TIMESTAMP);
    v_created_by      TEXT        	:= COALESCE(p_created_by, CURRENT_USER);
    v_provenance      TEXT        	:= COALESCE(p_provenance, 'CCDA');
    v_exception_id    TEXT        	:= gen_random_uuid()::text;
    v_nature_denorm   TEXT 		  	:= trim(COALESCE(p_nature->>'nature', ''));
    v_tenant_id_denorm TEXT 	  	:= p_nature->>'tenant_id';
    v_ccda_source     TEXT 		  	:= p_ccda_authoring_device;

    v_return          JSONB 	  	:= NULL;
    v_return_status   BOOLEAN 	  	:= false;
    v_is_exception    BOOLEAN 	  	:= false;

    v_elaboration     JSONB 	  	:= NULL;
    v_elaboration_steps_text TEXT[] := ARRAY[]::text[]; 
    v_elaboration_steps_jsonb JSONB := '[]'::jsonb;

    v_additional_details JSONB := jsonb_build_object(
        'request', jsonb_build_object(
            'User-Agent', p_user_agent,
            'remoteAddr', p_client_ip_address,
            'X-Observability-Metric-Interaction-Start-Time', v_created_at,
            'X-Observability-Metric-Interaction-Finish-Time', v_created_at
        ),
        'version', p_techbd_version_number
    );

BEGIN
    /*
	 This function handles the registration of CCDA interaction requests into various hub and satellite tables within the techbd_udi_ingress schema.
	 Handles optional linkage to a nexus interaction, logs FHIR request details, and saves user information if provided. 	
	 It first attempts to insert a record into the hub_interaction table, handling unique constraint violations based on the hub_upsert_behavior flag.
	 It then logs request details into the sat_interaction_http_request table, including payload, state transitions, and user information.
	 Based on the interaction key and request nature, it conditionally calls upsert functions for user, HTTP, or FHIR data. In case of an error, the 
	 function logs the exception using the register_issue function.
	 Returns a JSONB object representing either the inserted data or an error.
	 - origin: http/sftp
	 - source_type: fhir/hl7/csv/ccda
	*/

    v_elaboration_steps_text := array_append(v_elaboration_steps_text, '0: In register_interaction_ccda_request function');
    IF v_nature_denorm NOT IN ('Original CCDA Payload', 'CCDA Validation Result', 'Converted to FHIR') THEN
        v_return_status := true;
        v_return := jsonb_build_object(
            'error', 'Not a valid CCDA nature',
            'payload', p_payload,
            'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
            'interaction_id', p_interaction_id
        );
    END IF;

    IF NOT v_return_status
       AND p_interaction_key NOT IN ('/ccda/Bundle', '/ccda/Bundle/', '/ccda/Bundle/$validate', '/ccda/Bundle/$validate/')
    THEN
        v_return_status := true;
        v_return := jsonb_build_object(
            'error', 'Not a valid CCDA interaction_key',
            'payload', p_payload,
            'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
            'interaction_id', p_interaction_id
        );
    END IF;

    -- Check hub_nexus_interaction exists when provided
    IF NOT v_return_status
       AND p_hub_nexus_interaction_id IS NOT NULL
       AND NOT EXISTS (
           SELECT 1 FROM techbd_udi_ingress.hub_nexus_interaction
           WHERE hub_nexus_interaction_id = p_hub_nexus_interaction_id
       )
    THEN
        v_return_status := true;
        v_return := jsonb_build_object(
            'error', 'hub_nexus_interaction_id does not exist in the hub_nexus_interaction table',
            'payload', NULL,
            'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
            'interaction_id', p_interaction_id
        );
    END IF;

    IF NOT v_return_status THEN
        -- Insert hub_interaction
        BEGIN
            INSERT INTO techbd_udi_ingress.hub_interaction (hub_interaction_id, key, created_at, created_by, provenance)
            VALUES (p_interaction_id, p_interaction_key, v_created_at, v_created_by, v_provenance);

            v_elaboration_steps_text := array_append(v_elaboration_steps_text, '1: Inserted into hub_interaction');
        EXCEPTION
            WHEN unique_violation THEN
                IF NOT p_hub_upsert_behavior THEN
                    GET STACKED DIAGNOSTICS
                        v_error_msg = MESSAGE_TEXT,
                        v_sqlstate = RETURNED_SQLSTATE,
                        v_pg_detail = PG_EXCEPTION_DETAIL,
                        v_pg_hint = PG_EXCEPTION_HINT,
                        v_pg_context = PG_EXCEPTION_CONTEXT;
                    v_error_type := 'SQL';
                    v_is_exception := true;
                ELSE
                    -- allowed to upsert (unique_violation ignored)
                    v_elaboration_steps_text := array_append(v_elaboration_steps_text, '1: hub_interaction already exists (unique_violation ignored)');
                END IF;
            WHEN OTHERS THEN
                GET STACKED DIAGNOSTICS
                    v_error_msg = MESSAGE_TEXT,
                    v_sqlstate = RETURNED_SQLSTATE,
                    v_pg_detail = PG_EXCEPTION_DETAIL,
                    v_pg_hint = PG_EXCEPTION_HINT,
                    v_pg_context = PG_EXCEPTION_CONTEXT;
                v_error_type := 'SQL';
                v_is_exception := true;
                v_return_status := true;
                v_return := jsonb_build_object(
                    'error', 'Insert into hub_interaction failed',
                    'payload', NULL,
                    'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
                    'interaction_id', p_interaction_id,
                    'file_name', p_file_name
                );
        END;
    END IF;

    -- Link nexus interaction if provided
    IF NOT v_return_status AND p_hub_nexus_interaction_id IS NOT NULL THEN
        INSERT INTO techbd_udi_ingress.link_nexus_interaction (
            hub_nexus_interaction_id, hub_interaction_id, created_by, created_at, provenance
        ) VALUES (
            p_hub_nexus_interaction_id, p_interaction_id, v_created_by, v_created_at, v_provenance
        ) ON CONFLICT DO NOTHING;

        v_elaboration_steps_text := array_append(v_elaboration_steps_text, '2: Inserted into link_nexus_interaction (or existed)');
    END IF;

    -- Insert sat_interaction_http_request and call downstream upserts
    IF NOT v_return_status THEN
        BEGIN
            INSERT INTO techbd_udi_ingress.sat_interaction_http_request (
                sat_interaction_http_request_id,
                hub_interaction_id,
                nature,
                content_type,
                payload,
                payload_text,
                from_state,
                to_state,
                state_transition_reason,
                elaboration,
                created_at,
                created_by,
                provenance,
                nature_denorm,
                tenant_id_denorm,
                request_source,
                techbd_version_number,
                tenant_id
            )
            VALUES (
                gen_random_uuid()::text,
                p_interaction_id,
                p_nature,
                p_content_type,
                p_payload,
                p_payload_text,
                p_from_state,
                p_to_state,
                p_state_transition_reason,
                p_elaboration,
                v_created_at,
                v_created_by,
                v_provenance,
                v_nature_denorm,
                v_tenant_id_denorm,
                p_request_source,
                p_techbd_version_number,
                v_tenant_id_denorm
            );

            v_elaboration_steps_text := array_append(v_elaboration_steps_text, '3: Inserted into sat_interaction_http_request');

            -- Upsert user info if Original CCDA Payload
            IF v_nature_denorm = 'Original CCDA Payload' THEN
                PERFORM techbd_udi_ingress.sat_interaction_user_upserted(
                    hub_interaction_id      => p_interaction_id,
                    hub_interaction_key     => p_interaction_key,
                    nature                  => v_nature_denorm,
                    payload                 => p_payload,
                    user_id                 => p_user_id,
                    user_name               => p_user_name,
                    user_session            => p_user_session,
                    user_role               => p_user_role,
                    tenant_id               => v_tenant_id_denorm,
                    elaboration             => p_elaboration,
                    created_at              => v_created_at,
                    created_by              => v_created_by,
                    provenance              => v_provenance,
                    p_additional_details    => v_additional_details
                );

                v_elaboration_steps_text := array_append(v_elaboration_steps_text, '4: Called sat_interaction_user_upserted');
            END IF;

            -- Upsert ccda request info
            IF v_nature_denorm IN ('Original CCDA Payload', 'Converted to FHIR', 'CCDA Validation Result') THEN
                PERFORM techbd_udi_ingress.sat_interaction_ccda_request_upserted(
                    hub_interaction_id      => p_interaction_id,
                    tenant_id               => v_tenant_id_denorm,
                    uri                     => p_interaction_key,
                    nature                  => v_nature_denorm,
                    from_state              => p_from_state,
                    to_state                => p_to_state,
                    payload                 => p_payload,
                    ccda_payload_text       => p_payload_text,
                    state_transition_reason => p_state_transition_reason,
                    created_at              => v_created_at,
                    created_by              => v_created_by,
                    provenance              => v_provenance,
                    elaboration             => p_elaboration,
                    client_ip_address       => p_client_ip_address,
                    user_agent              => p_user_agent,
                    origin                  => p_origin,
                    p_techbd_version_number => p_techbd_version_number,
                    p_file_name             => p_file_name,
					p_ccda_authoring_device	=> v_ccda_source
                );

                v_return := jsonb_build_object(
                    'payload', p_payload,
                    'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
                    'interaction_id', p_interaction_id
                );

                v_elaboration_steps_text := array_append(v_elaboration_steps_text, '5: Called sat_interaction_ccda_request_upserted');
            ELSE
                v_return := jsonb_build_object(
                    'error', 'Not a valid CCDA nature',
                    'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
                    'interaction_id', p_interaction_id
                );
            END IF;

        EXCEPTION
            WHEN OTHERS THEN
                GET STACKED DIAGNOSTICS
                    v_error_msg = MESSAGE_TEXT,
                    v_sqlstate = RETURNED_SQLSTATE,
                    v_pg_detail = PG_EXCEPTION_DETAIL,
                    v_pg_hint = PG_EXCEPTION_HINT,
                    v_pg_context = PG_EXCEPTION_CONTEXT;
                v_error_type := 'SQL';

                v_is_exception := true;
                v_return := jsonb_build_object(
                    'error', v_error_msg,
                    'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
                    'interaction_id', p_interaction_id
                );
        END;
    END IF;

    -- Build elaboration object
    v_elaboration := jsonb_build_object(
        'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
        'interaction_id', p_interaction_id,
        'interaction_key', p_interaction_key,
        'nature', v_nature_denorm,
        'tenant_id', v_tenant_id_denorm,
        'from_state', p_from_state,
        'to_state', p_to_state,
        'techbd_version_number', p_techbd_version_number,
        'file_name', p_file_name,
		'ccda_authoring_device', v_ccda_source,
        'additional_details', v_additional_details,
        'return_payload', v_return
    );

    -- Convert steps array to JSONB and attach
    IF array_length(v_elaboration_steps_text, 1) IS NOT NULL THEN
        v_elaboration_steps_jsonb := to_jsonb(v_elaboration_steps_text);
        v_elaboration := v_elaboration || jsonb_build_object('steps', v_elaboration_steps_jsonb);
    END IF;

    -- Attach error message if present
    IF v_error_msg IS NOT NULL THEN
        v_elaboration := v_elaboration || jsonb_build_object('error_message', v_error_msg);
    END IF;

    -- Register diagnostic log
    CALL techbd_udi_ingress.register_diagnostic_log(
        v_exception_id,
        p_interaction_key,
        v_created_by,
        v_provenance,
        'info'::text,
        'register_interaction_ccda_request Logs'::text,
        COALESCE(p_user_id, v_created_by),
        (CASE WHEN v_is_exception THEN 'failure' ELSE 'success' END),
        NULL::text,
        0,
        v_elaboration,
        v_tenant_id_denorm,
        p_interaction_id
    );

    -- If exception occurred, register issue
    IF v_is_exception THEN
        v_exception_id := techbd_udi_ingress.register_issue(
            v_exception_id,
            p_interaction_key,
            v_error_msg,
            v_error_type,
            v_sqlstate,
            v_pg_detail,
            v_pg_hint,
            v_pg_context,
            v_created_by,
            v_provenance,
            v_elaboration,
            v_tenant_id_denorm,
            p_interaction_id
        );
    END IF;

    RETURN v_return;
END;
$function$
;

/*******************************************************************************************
register_interaction_csv_request -  This function handles the registration of CSV interaction requests into various hub and satellite tables within 
    the techbd_udi_ingress schema. Handles optional linkage to a nexus interaction, logs FHIR request details, and saves user information if provided. 	
    It first attempts to insert a record into the hub_interaction table, handling unique constraint violations based on the hub_upsert_behavior flag.
    It then logs request details into the sat_interaction_http_request table, including payload, state transitions, and user information.
    Based on the interaction key and request nature, it conditionally calls upsert functions for user, HTTP, or FHIR data. In case of an error, the 
    function logs the exception using the register_issue function.
    Returns a JSONB object representing either the inserted data or an error.
    - origin: http/sftp
    - source_type: csv
*******************************************************************************************/
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_csv_request(text, text, text, jsonb, text, text, jsonb, jsonb, text, text, text, text, text, text, text, text, text, text, bool, text, bytea, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_csv_request(text, text, text, jsonb, text, text, jsonb, jsonb, text, text, text, text, text, text, text, text, text, timestamptz, text, bool, text, bytea, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_csv_request(text, text, text, jsonb, text, text, jsonb, jsonb, text, text, text, text, text, text, text, text, text, timestamptz, text, bool, text, bytea, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_csv_request(text, text, text, jsonb, text, text, jsonb, jsonb, text, text, text, text, text, text, text, text, text, timestamptz, text, bool, text, bytea, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, jsonb, text, jsonb);
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_csv_request(text, text, text, jsonb, text, text, jsonb, jsonb, text, text, text, text, text, text, text, text, text, timestamptz, text, bool, text, bytea, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, text, jsonb, text, jsonb, int4, int4, text);

CREATE OR REPLACE FUNCTION techbd_udi_ingress.register_interaction_csv_request(
	p_interaction_id text, 
	p_interaction_key text, 
	p_hub_nexus_interaction_id text,
	p_payload jsonb DEFAULT NULL::jsonb, 
	p_payload_text text DEFAULT NULL::text, 
	p_rule_namespace text DEFAULT NULL::text, 
	p_elaboration jsonb DEFAULT NULL::jsonb, 
	p_nature jsonb DEFAULT NULL::jsonb, 
	p_content_type text DEFAULT NULL::text, 
	p_from_state text DEFAULT NULL::text, 
	p_to_state text DEFAULT NULL::text, 
	p_state_transition_reason text DEFAULT NULL::text, 
	p_user_id text DEFAULT NULL::text, 
	p_user_name text DEFAULT NULL::text, 
	p_user_session text DEFAULT NULL::text, 
	p_user_role text DEFAULT NULL::text, 
	p_created_by text DEFAULT CURRENT_USER, 
	p_created_at timestamp with time zone DEFAULT NULL::timestamp with time zone,
	p_provenance text DEFAULT NULL::text, 
	p_hub_upsert_behavior boolean DEFAULT true, 
	p_csv_zip_file_name text DEFAULT NULL::text, 
	p_csv_zip_file_content bytea DEFAULT NULL::bytea, 
	p_csv_group_id text DEFAULT NULL::text, 
	p_csv_status text DEFAULT NULL::text, 
	p_csv_screening_observation_data_payload_text text DEFAULT NULL::text, 
	p_csv_screening_profile_data_payload_text text DEFAULT NULL::text, 
	p_csv_demographic_data_payload_text text DEFAULT NULL::text, 
	p_csv_qe_admin_data_payload_text text DEFAULT NULL::text, 
	p_csv_screening_observation_data_file_name text DEFAULT NULL::text, 
	p_csv_screening_profile_data_file_name text DEFAULT NULL::text, 
	p_csv_demographic_data_file_name text DEFAULT NULL::text, 
	p_csv_qe_admin_data_file_name text DEFAULT NULL::text, 
	p_source_hub_interaction_id text DEFAULT NULL::text, 
	p_client_ip_address text DEFAULT NULL::text, 
	p_user_agent text DEFAULT NULL::text, 
	p_origin text DEFAULT NULL::text, 
	p_source_type text DEFAULT NULL::text, 
	p_group_hub_interaction_id text DEFAULT NULL::text, 
	p_sftp_session_id text DEFAULT NULL::text,
	p_request_source text DEFAULT NULL::text,
	p_zip_file_processing_errors jsonb DEFAULT NULL::jsonb,
	p_techbd_version_number text DEFAULT NULL::text, 
	p_full_operation_outcome jsonb DEFAULT NULL::jsonb,
    p_total_number_of_files_in_zip_file INTEGER DEFAULT 0, 
    p_number_of_fhir_bundles_generated_from_zip_file INTEGER DEFAULT 0, 
    p_data_validation_status TEXT DEFAULT NULL::text)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_error_msg 		TEXT;
    v_error_type 		TEXT;
    v_sqlstate 			TEXT;
    v_pg_detail 		TEXT;
    v_pg_hint 			TEXT;
    v_pg_context 		TEXT;
    v_created_at 		TIMESTAMPTZ := COALESCE(p_created_at, CURRENT_TIMESTAMP);
	v_created_by 		TEXT 		:= COALESCE(p_created_by, CURRENT_USER);  -- Default created_by to the current user if not provided
    v_provenance 		TEXT 		:= COALESCE(p_provenance, 'CSV');
    v_exception_id 		TEXT		:= gen_random_uuid()::text;
   	v_nature_denorm 	TEXT		:= trim(p_nature->>'nature');
   	v_tenant_id_denorm 	TEXT		:= p_nature->>'tenant_id';
   
   	v_from_state				TEXT;
   	v_to_state					TEXT;
   	v_state_transition_reason 	TEXT;
   	v_nature 					JSONB;   
   	v_return 					JSONB 	:= NULL;
	v_return_status				BOOLEAN := false;
	v_is_exception				BOOLEAN := false;
   	v_disposition_json			JSONB 	:= NULL;
   	v_payload					JSONB 	:= NULL;
    v_elaboration 				JSONB 	:= NULL;
	v_elaboration_steps_text TEXT[] 	:= ARRAY[]::text[]; 
	v_elaboration_steps_jsonb JSONB 	:= '[]'::jsonb;
	v_elaboration_steps 		JSONB	:= '[]'::jsonb;
    v_user_id 					TEXT  	:= COALESCE(p_user_id, v_created_by);	
	v_additional_details		JSONB 	:= jsonb_build_object(
										    'request', jsonb_build_object(
										      'User-Agent', p_user_agent,
										      'remoteAddr', p_client_ip_address,
										      'X-Observability-Metric-Interaction-Start-Time', v_created_at,
										      'X-Observability-Metric-Interaction-Finish-Time', v_created_at
										    ),
											'version', p_techbd_version_number
										  );
   
BEGIN 
	/*
	 This function handles the registration of CSV interaction requests into various hub and satellite tables within the techbd_udi_ingress schema.
	 Handles optional linkage to a nexus interaction, logs FHIR request details, and saves user information if provided. 	
	 It first attempts to insert a record into the hub_interaction table, handling unique constraint violations based on the hub_upsert_behavior flag.
	 It then logs request details into the sat_interaction_http_request table, including payload, state transitions, and user information.
	 Based on the interaction key and request nature, it conditionally calls upsert functions for user, HTTP, or FHIR data. In case of an error, the 
	 function logs the exception using the register_issue function.
	 Returns a JSONB object representing either the inserted data or an error.
	 - origin: http/sftp
	 - source_type: fhir/hl7/csv/ccda
	*/

	v_elaboration_steps_text := array_append(v_elaboration_steps_text, '0: In register_interaction_csv_request function');

	IF v_nature_denorm NOT IN('Original CSV Zip Archive', 'Original Flat File CSV', 'CSV Validation Result', 'Converted to FHIR') THEN
		v_return_status := true;
		v_return := jsonb_build_object('error', 'Not a valid CSV nature',
								  'payload', p_payload,
								  'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
								  'interaction_id', p_interaction_id);
	END IF;
	IF NOT v_return_status AND p_interaction_key NOT IN('/flatfile/csv/Bundle', '/flatfile/csv/Bundle/', '/flatfile/csv/Bundle/$validate', '/flatfile/csv/Bundle/$validate/') THEN
		v_return_status := true;
		v_return := jsonb_build_object('error', 'Not a valid CSV interaction_key',
								  'payload', p_payload,
								  'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
								  'interaction_id', p_interaction_id);
	END IF;
	
    -- Check hub_nexus_interaction exists
    IF NOT v_return_status AND p_hub_nexus_interaction_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM techbd_udi_ingress.hub_nexus_interaction
        WHERE hub_nexus_interaction_id = p_hub_nexus_interaction_id
    ) THEN
        v_return_status := true;
		v_return := jsonb_build_object('error', 'p_hub_nexus_interaction_id does not exists in the hub_nexus_interaction table', 
								  'payload', p_payload,
								  'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
								  'interaction_id', p_interaction_id);
    END IF;

	IF NOT v_return_status THEN
	    -- Attempt to insert into hub_interaction
		BEGIN	   	    
	        INSERT INTO techbd_udi_ingress.hub_interaction (hub_interaction_id, key, created_at, created_by, provenance)
	        VALUES (p_interaction_id, p_interaction_key, v_created_at, p_created_by, v_provenance);
	
			v_elaboration_steps_text := array_append(v_elaboration_steps_text, '1: Inserted into hub_interaction');
	    EXCEPTION
	        WHEN unique_violation THEN
	            IF NOT p_hub_upsert_behavior THEN
	                -- Capture exception details
	                GET STACKED DIAGNOSTICS
	                    v_error_msg = MESSAGE_TEXT,
	                    v_sqlstate = RETURNED_SQLSTATE,
	                    v_pg_detail = PG_EXCEPTION_DETAIL,
	                    v_pg_hint = PG_EXCEPTION_HINT,
	                    v_pg_context = PG_EXCEPTION_CONTEXT;
	                    v_error_type = 'SQL';
	
	                v_is_exception := true;	
	            END IF; 
	       WHEN OTHERS THEN
	        	GET STACKED DIAGNOSTICS
	                v_error_msg = MESSAGE_TEXT,
	                v_sqlstate = RETURNED_SQLSTATE,
	                v_pg_detail = PG_EXCEPTION_DETAIL,
	                v_pg_hint = PG_EXCEPTION_HINT,
	                v_pg_context = PG_EXCEPTION_CONTEXT;
	                v_error_type = 'SQL';
	
	         	v_is_exception := true;				
				v_return_status := true;
				v_return := jsonb_build_object('error', v_error_msg, 
									  	 'payload', p_payload,
										 'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
										 'interaction_id', p_interaction_id);
	    END;
	
		-- Insert into link table
		IF NOT v_return_status AND p_hub_nexus_interaction_id IS NOT NULL THEN
			INSERT INTO techbd_udi_ingress.link_nexus_interaction (
				hub_nexus_interaction_id, hub_interaction_id, created_by, created_at, provenance
			) VALUES (
				p_hub_nexus_interaction_id, p_interaction_id, p_created_by, v_created_at, v_provenance
			) ON CONFLICT DO NOTHING;

			v_elaboration_steps_text := array_append(v_elaboration_steps_text, '2: Inserted into link_nexus_interaction');
		END IF;
		
		IF NOT v_return_status THEN
		    -- Attempt to insert into sat_interaction_http_request
		    BEGIN
			    -- The insertion to sat_interaction_http_request table needs only once. Need to be corrected later.
		        INSERT INTO techbd_udi_ingress.sat_interaction_http_request (
		            sat_interaction_http_request_id, hub_interaction_id, nature, content_type, payload, payload_text, from_state, 
		            to_state, state_transition_reason, elaboration, created_at, created_by, provenance, nature_denorm, tenant_id_denorm, request_source, techbd_version_number, tenant_id
		        )
		        VALUES (
		            gen_random_uuid()::text, p_interaction_id, p_nature, p_content_type, p_payload, p_payload_text, p_from_state, p_to_state, 
		            p_state_transition_reason, p_elaboration, v_created_at, p_created_by, v_provenance, v_nature_denorm, v_tenant_id_denorm, p_request_source, p_techbd_version_number, v_tenant_id_denorm
		        );

		        v_elaboration_steps_text := array_append(v_elaboration_steps_text, '3: Inserted into sat_interaction_http_request');

		       	--1) Call the function with the defined parameters to upsert user info
		       	IF(v_nature_denorm = 'Original CSV Zip Archive') THEN     		       	
			    	PERFORM techbd_udi_ingress.sat_interaction_user_upserted(
				        hub_interaction_id 			=> p_interaction_id,
				        hub_interaction_key			=> p_interaction_key, 
			            nature						=> v_nature_denorm,
				        payload						=> p_payload, 
				        user_id						=> p_user_id, 
				        user_name					=> p_user_name, 
				        user_session				=> p_user_session, 
				        user_role					=> p_user_role,
				        tenant_id					=> v_tenant_id_denorm,
				        elaboration					=> p_elaboration,
				        created_at 					=> v_created_at,
				        created_by 					=> p_created_by,
				        provenance 					=> v_provenance,
						p_additional_details		=> v_additional_details
			       	); 

			      	v_elaboration_steps_text := array_append(v_elaboration_steps_text, '4: Inserted into user info');
			    END IF;
			   
			    IF v_nature_denorm IN ('Original CSV Zip Archive', 'Original Flat File CSV', 'CSV Validation Result', 'Converted to FHIR') THEN
					PERFORM techbd_udi_ingress.sat_interaction_csv_request_upserted(
				        interaction_id 				=> p_interaction_id,
				        uri 						=> p_interaction_key,
				        nature 						=> v_nature_denorm,
				        tenant_id 					=> v_tenant_id_denorm,
				        csv_zip_file_name 			=> p_csv_zip_file_name,
				        csv_zip_file_content 		=> p_csv_zip_file_content,
				        group_id 					=> p_csv_group_id,
				        status						=> p_csv_status,
				        csv_screening_observation_data_payload_text => p_csv_screening_observation_data_payload_text, 
				        csv_screening_profile_data_payload_text		=> p_csv_screening_profile_data_payload_text, 
				        csv_demographic_data_payload_text 			=> p_csv_demographic_data_payload_text, 
				        csv_qe_admin_data_payload_text 				=> p_csv_qe_admin_data_payload_text,
						csv_screening_observation_data_file_name 	=> p_csv_screening_observation_data_file_name, 
				        csv_screening_profile_data_file_name 		=> p_csv_screening_profile_data_file_name, 
				        csv_demographic_data_file_name 				=> p_csv_demographic_data_file_name, 
				        csv_qe_admin_data_file_name 				=> p_csv_qe_admin_data_file_name,
				        validation_result_payload 					=> p_payload,		        
				        from_state 					=> p_from_state,
				        to_state 					=> p_to_state,
				        state_transition_reason 	=> p_state_transition_reason,
				        created_at 					=> v_created_at,
				        created_by 					=> p_created_by,
				        provenance 					=> v_provenance,
				        elaboration 				=> p_elaboration,
				        zip_file_hub_interaction_id => p_source_hub_interaction_id,
				        client_ip_address 			=> p_client_ip_address, 
				        user_agent 					=> p_user_agent,
				        origin						=> p_origin,
				        sftp_session_id				=> p_sftp_session_id,
						zip_file_processing_errors	=> p_zip_file_processing_errors,
						p_full_operation_outcome	=> p_full_operation_outcome,
						p_techbd_version_number		=> p_techbd_version_number,
						p_total_number_of_files_in_zip_file 			  => p_total_number_of_files_in_zip_file,
						p_number_of_fhir_bundles_generated_from_zip_file  => p_number_of_fhir_bundles_generated_from_zip_file,
						p_data_validation_status						  => p_data_validation_status
			       	);
		
					v_return := jsonb_build_object('payload', p_payload,
											 'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
											 'interaction_id', p_interaction_id);
					v_elaboration_steps_text := array_append(v_elaboration_steps_text, '5: Inserted into ' || CASE WHEN v_nature_denorm = 'Original CSV Zip Archive' THEN 'sat_interaction_zip_file_request' ELSE 'sat_interaction_flat_file_csv_request' END);
				ELSE
					v_return := jsonb_build_object('error','Not a CSV Interaction Key',
											 'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
											 'interaction_id', p_interaction_id);		
		       	END IF;	   
			
		    EXCEPTION
		        WHEN OTHERS THEN
		            -- Capture exception details
		            GET STACKED DIAGNOSTICS
		                v_error_msg = MESSAGE_TEXT,
		                v_sqlstate = RETURNED_SQLSTATE,
		                v_pg_detail = PG_EXCEPTION_DETAIL,
		                v_pg_hint = PG_EXCEPTION_HINT,
		                v_pg_context = PG_EXCEPTION_CONTEXT;
		                v_error_type = 'SQL';
		
		         	v_is_exception := true;		         	
					v_return := jsonb_build_object('error', v_error_msg, 
										  	 'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
											 'interaction_id', p_interaction_id);
		    END;
		END IF;
	END IF;

   	-- Save the log details into table
	v_elaboration := jsonb_build_object(
						'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
						'interaction_id', p_interaction_id,
						'interaction_key', p_interaction_key,
						'nature', p_nature,
						'from_state', p_from_state,
						'to_state', p_to_state,
						'general_errors', p_zip_file_processing_errors,
						'full_operation_outcome', p_full_operation_outcome,
						'techbd_version_number', p_techbd_version_number,
						'total_number_of_files_in_zip_file', p_total_number_of_files_in_zip_file,
						'number_of_fhir_bundles_generated_from_zip_file', p_number_of_fhir_bundles_generated_from_zip_file,
						'data_validation_status', p_data_validation_status
					  );
	-- Convert steps array to JSONB and attach
    IF array_length(v_elaboration_steps_text, 1) IS NOT NULL THEN
        v_elaboration_steps_jsonb := to_jsonb(v_elaboration_steps_text);
        v_elaboration := v_elaboration || jsonb_build_object('steps', v_elaboration_steps_jsonb);
    END IF;

    -- Attach error message if present
    IF v_error_msg IS NOT NULL THEN
        v_elaboration := v_elaboration || jsonb_build_object('error_message', v_error_msg);
    END IF;

    -- Register diagnostic log
    CALL techbd_udi_ingress.register_diagnostic_log(v_exception_id, p_interaction_key, v_created_by, v_provenance, 'info'::text, 
       													'register_interaction_csv_request Logs'::text, v_user_id, 'success'::text, NULL::text, 0, v_elaboration, v_tenant_id_denorm, p_interaction_id);

	IF v_is_exception = true THEN    
        -- Log the exception, reusing the previous exception ID if it exists
        v_elaboration := v_elaboration || jsonb_build_array(jsonb_build_object('error_message', v_error_msg));
        v_exception_id := techbd_udi_ingress.register_issue(
            v_exception_id, p_interaction_key, v_error_msg, v_error_type, v_sqlstate, v_pg_detail, v_pg_hint, v_pg_context, v_created_by, v_provenance, v_elaboration, v_tenant_id_denorm, p_interaction_id
        );
	END IF;
   	RETURN v_return;
END;
$function$
;

/*******************************************************************************************
register_user_interaction -  This function is designed to handle the registration and upserting of HTTP interaction requests into user anf http interaction tables  
    in the techbd_udi_ingress schema. It first attempts to insert a record into the hub_interaction table and handles unique constraint violations 
    based on the hub_upsert_behavior flag. It then inserts data into the sat_interaction_http_request table, capturing details such as 
    payload, state transitions, and user information. In case of any error, it logs detailed exception information using the 
    register_issue function. The function returns a JSONB object, representing either the inserted data or a processed result.
    - origin: http/sftp
    - source_type: fhir/hl7/csv/ccda
*******************************************************************************************/
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_user_interaction(text, text, jsonb, jsonb, text, text, text, jsonb, text, text, text, text, text, text, text, text, text, text, bool, text, text, text, text, text, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_user_interaction(text, text, jsonb, jsonb, text, text, text, jsonb, text, text, text, text, text, text, text, text, text, timestamptz, text, bool, text, text, text, text, text, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_user_interaction(text, text, jsonb, jsonb, text, text, text, jsonb, text, text, text, text, text, text, text, text, text, timestamptz, text, bool, text, text, text, text, text, text, text, text, timestamptz, timestamptz);
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_user_interaction(text, text, jsonb, jsonb, text, text, text, jsonb, text, text, text, text, text, text, text, text, text, timestamptz, text, bool, text, text, text, text, text, text, text, text, timestamptz, timestamptz, text);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.register_user_interaction(
	p_interaction_id text, 
	p_interaction_key text, 
	p_nature jsonb DEFAULT NULL::jsonb, 
	p_payload jsonb DEFAULT NULL::jsonb, 
	p_payload_text text DEFAULT NULL::text, 
	p_hub_nexus_interaction_id text DEFAULT NULL::text, 
	p_rule_namespace text DEFAULT NULL::text, 
	p_elaboration jsonb DEFAULT NULL::jsonb, 
	p_content_type text DEFAULT NULL::text, 
	p_from_state text DEFAULT NULL::text, 
	p_to_state text DEFAULT NULL::text, 
	p_state_transition_reason text DEFAULT NULL::text, 
	p_user_id text DEFAULT NULL::text, 
	p_user_name text DEFAULT NULL::text, 
	p_user_session text DEFAULT NULL::text, 
	p_user_role text DEFAULT NULL::text, 
	p_created_by text DEFAULT NULL::text, 
    p_created_at timestamp with time zone DEFAULT NULL::timestamp with time zone, 
	p_provenance text DEFAULT NULL::text, 
	p_hub_upsert_behavior boolean DEFAULT true, 
	p_source_hub_interaction_id text DEFAULT NULL::text, 
	p_client_ip_address text DEFAULT NULL::text, 
	p_user_agent text DEFAULT NULL::text, 
	p_origin text DEFAULT NULL::text, 
	p_source_type text DEFAULT NULL::text, 
	p_group_hub_interaction_id text DEFAULT NULL::text, 
	p_sftp_session_id text DEFAULT NULL::text,
	p_request_source text DEFAULT NULL::text,
	p_intr_start_time timestamp with time zone DEFAULT NULL::timestamp with time zone,
   	p_intr_finish_time timestamp with time zone DEFAULT NULL::timestamp with time zone,
	p_techbd_version_number text DEFAULT NULL::text)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_error_msg 		TEXT;
    v_error_type 		TEXT;
    v_sqlstate 			TEXT;
    v_pg_detail 		TEXT;
    v_pg_hint 			TEXT;
    v_pg_context 		TEXT;
    v_created_at 		TIMESTAMPTZ := COALESCE(p_created_at, CURRENT_TIMESTAMP);  -- Ensure created_at is not null
    v_created_by 		TEXT 		:= COALESCE(p_created_by, CURRENT_USER);
    v_provenance 		TEXT 		:= COALESCE(p_provenance, 'USER');
    v_exception_id 		TEXT		:= gen_random_uuid()::text;
   	v_nature_denorm 	TEXT		:= trim(p_nature->>'nature');
   	v_tenant_id_denorm 	TEXT		:= p_nature->>'tenant_id';  
   
   	v_from_state				TEXT;
   	v_to_state					TEXT;
   	v_state_transition_reason 	TEXT;
   	v_nature 					JSONB;   
   	v_return 					JSONB 	:= NULL;
	v_return_status				BOOLEAN := false;
	v_is_exception				BOOLEAN := false;
   	v_disposition_json			JSONB 	:= NULL;
   	v_payload					JSONB 	:= NULL;
    v_elaboration 				JSONB 	:= NULL;
	v_elaboration_steps 		JSONB 	:= '[]'::jsonb;
	v_elaboration_steps_text 	TEXT[]	:= ARRAY[]::text[]; 
    v_elaboration_steps_jsonb 	JSONB 	:= '[]'::jsonb;
    v_user_id 					TEXT  	:= COALESCE(p_user_id, v_created_by);	
	v_additional_details		JSONB 	:= jsonb_build_object(
										    'request', jsonb_build_object(
										        'User-Agent', p_user_agent,
										        'remoteAddr', p_client_ip_address,
										        'X-Observability-Metric-Interaction-Start-Time', p_intr_start_time,
										        'X-Observability-Metric-Interaction-Finish-Time', p_intr_finish_time
										    ),
											'version', p_techbd_version_number
										);
   
BEGIN 
	/* This function is designed to handle the registration and upserting of HTTP interaction requests into user anf http interaction tables in the 
     * techbd_udi_ingress schema. It first attempts to insert a record into the hub_interaction table and handles unique constraint violations 
     * based on the hub_upsert_behavior flag. It then inserts data into the sat_interaction_http_request table, capturing details such as 
     * payload, state transitions, and user information. In case of any error, it logs detailed exception information using the 
     * register_issue function. The function returns a JSONB object, representing either the inserted data or a processed result.
     * origin : http/sftp
     * source_type : fhir/hl7/csv/ccda
     */
	
	v_elaboration_steps_text := array_append(v_elaboration_steps_text, '0: In register_user_interaction function');  
	
	-- Check hub_nexus_interaction exists
    IF p_hub_nexus_interaction_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM techbd_udi_ingress.hub_nexus_interaction
        WHERE hub_nexus_interaction_id = p_hub_nexus_interaction_id
    ) THEN
        v_return_status := true;
		v_return := jsonb_build_object('error', 'p_hub_nexus_interaction_id does not exists in the hub_nexus_interaction table', 
								  'payload', p_payload,
								  'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
								  'interaction_id', p_interaction_id);
    END IF;

	IF NOT v_return_status THEN
	    -- Attempt to insert into hub_interaction
		BEGIN	   	    
	        INSERT INTO techbd_udi_ingress.hub_interaction (hub_interaction_id, key, created_at, created_by, provenance)
	        VALUES (p_interaction_id, p_interaction_key, v_created_at, v_created_by, v_provenance);
	        
			v_elaboration_steps_text := array_append(v_elaboration_steps_text, '1: Inserted into hub_interaction'); 
	    EXCEPTION
	        WHEN unique_violation THEN
	            IF NOT p_hub_upsert_behavior THEN
	                -- Capture exception details
	                GET STACKED DIAGNOSTICS
	                    v_error_msg = MESSAGE_TEXT,
	                    v_sqlstate = RETURNED_SQLSTATE,
	                    v_pg_detail = PG_EXCEPTION_DETAIL,
	                    v_pg_hint = PG_EXCEPTION_HINT,
	                    v_pg_context = PG_EXCEPTION_CONTEXT;
	                    v_error_type = 'SQL';
	
	                v_is_exception := true;
	            END IF; 
	       WHEN OTHERS THEN
	        	GET STACKED DIAGNOSTICS
	                v_error_msg = MESSAGE_TEXT,
	                v_sqlstate = RETURNED_SQLSTATE,
	                v_pg_detail = PG_EXCEPTION_DETAIL,
	                v_pg_hint = PG_EXCEPTION_HINT,
	                v_pg_context = PG_EXCEPTION_CONTEXT;
	                v_error_type = 'SQL';
	
	         	v_is_exception := true;				
				v_return_status := true;
				v_return := jsonb_build_object('error', v_error_msg, 
									  	 'payload', p_payload,
										 'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
										 'interaction_id', p_interaction_id);
	    END;
	
		-- Insert into link table
		IF NOT v_return_status AND p_hub_nexus_interaction_id IS NOT NULL then
			INSERT INTO techbd_udi_ingress.link_nexus_interaction (
				hub_nexus_interaction_id, hub_interaction_id, created_by, created_at, provenance
			) VALUES (
				p_hub_nexus_interaction_id, p_interaction_id, v_created_by, v_created_at, v_provenance
			) ON CONFLICT DO NOTHING;

			v_elaboration_steps_text := array_append(v_elaboration_steps_text, '2: Inserted into link_nexus_interaction');
		END IF;

		IF NOT v_return_status THEN
		    -- Attempt to insert into sat_interaction_http_request
		    BEGIN		  
			    -- The insertion to sat_interaction_http_request table needs only once. Need to be corrected later.
		        INSERT INTO techbd_udi_ingress.sat_interaction_http_request (
		            sat_interaction_http_request_id, hub_interaction_id, nature, content_type, payload, payload_text, from_state, 
		            to_state, state_transition_reason, elaboration, created_at, created_by, provenance, nature_denorm, tenant_id_denorm, request_source, techbd_version_number, tenant_id
		        )
		        VALUES (
		            gen_random_uuid()::text, p_interaction_id, p_nature, p_content_type, p_payload, p_payload_text, p_from_state, p_to_state, 
		            p_state_transition_reason, p_elaboration, v_created_at, v_created_by, v_provenance, v_nature_denorm, v_tenant_id_denorm, p_request_source, p_techbd_version_number, v_tenant_id_denorm
		        );

		        v_elaboration_steps_text := array_append(v_elaboration_steps_text, '3: Inserted into sat_interaction_http_request');
		
		       	--1) Call the function with the defined parameters to upsert user info
		       	IF(v_nature_denorm = 'org.techbd.service.http.Interactions$RequestResponseEncountered' AND trim(p_interaction_key) NOT IN ('/Hl7/v2', '/Hl7/v2/', '/flatfile/csv/Bundle', '/flatfile/csv/Bundle/', '/flatfile/csv/Bundle/$validate', '/flatfile/csv/Bundle/$validate/', '/ccda/Bundle', '/ccda/Bundle/', '/ccda/Bundle/$validate', '/ccda/Bundle/$validate/', '/hl7v2/Bundle', '/hl7v2/Bundle/','/hl7v2/Bundle/$validate', '/hl7v2/Bundle/$validate/')) THEN     		       	
					PERFORM techbd_udi_ingress.sat_interaction_user_upserted(
				        hub_interaction_id 			=> p_interaction_id,
				        hub_interaction_key			=> p_interaction_key, 
			            nature						=> v_nature_denorm,
				        payload						=> p_payload, 
				        user_id						=> p_user_id, 
				        user_name					=> p_user_name, 
				        user_session				=> p_user_session, 
				        user_role					=> p_user_role,
				        tenant_id					=> v_tenant_id_denorm,
				        elaboration					=> p_elaboration,
				        created_at 					=> v_created_at,
				        created_by 					=> v_created_by,
				        provenance 					=> v_provenance,
						p_additional_details		=> v_additional_details
			       	); 
			      	v_elaboration_steps_text := array_append(v_elaboration_steps_text, '4: Inserted into user info');
					
					v_return := jsonb_build_object('hub_nexus_interaction_id', p_hub_nexus_interaction_id,
											 'interaction_id', p_interaction_id);
			    END IF;
		    EXCEPTION
		        WHEN OTHERS THEN
		            -- Capture exception details
		            GET STACKED DIAGNOSTICS
		                v_error_msg = MESSAGE_TEXT,
		                v_sqlstate = RETURNED_SQLSTATE,
		                v_pg_detail = PG_EXCEPTION_DETAIL,
		                v_pg_hint = PG_EXCEPTION_HINT,
		                v_pg_context = PG_EXCEPTION_CONTEXT;
		                v_error_type = 'SQL';
		
		         	v_is_exception := true;		         	
					v_return := jsonb_build_object('error', v_error_msg, 
										  	 'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
											 'interaction_id', p_interaction_id);
		    END;
		END IF;
	END IF;

   	-- Save the log details into table
	v_elaboration := jsonb_build_object(
							'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
							'interaction_id', p_interaction_id,
							'interaction_key', p_interaction_key,
							'nature', p_nature,
							'from_state', p_from_state,
							'to_state', p_to_state,
							'techbd_version_number', p_techbd_version_number
							);
	-- Convert steps array to JSONB and attach
    IF array_length(v_elaboration_steps_text, 1) IS NOT NULL THEN
        v_elaboration_steps_jsonb := to_jsonb(v_elaboration_steps_text);
        v_elaboration := v_elaboration || jsonb_build_object('steps', v_elaboration_steps_jsonb);
    END IF;

    -- Attach error message if present
    IF v_error_msg IS NOT NULL THEN
        v_elaboration := v_elaboration || jsonb_build_object('error_message', v_error_msg);
    END IF;

    -- Register diagnostic log
    CALL techbd_udi_ingress.register_diagnostic_log(v_exception_id, p_interaction_key, v_created_by, v_provenance, 'info'::text, 
       																'register_user_interaction Logs'::text, v_user_id, 'success'::text, NULL::text, 0, v_elaboration, v_tenant_id_denorm, p_interaction_id);

	IF v_is_exception = true THEN    
        -- Log the exception, reusing the previous exception ID if it exists
        v_elaboration := v_elaboration || jsonb_build_array(jsonb_build_object('error_message', v_error_msg));
        v_exception_id := techbd_udi_ingress.register_issue(
            v_exception_id, p_interaction_key, v_error_msg, v_error_type, v_sqlstate, v_pg_detail, v_pg_hint, v_pg_context, v_created_by, v_provenance, v_elaboration, v_tenant_id_denorm, p_interaction_id
        );
	END IF;
   	RETURN v_return;
END;
$function$
;

DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_hl7_request(text, text, jsonb, text, text, text, jsonb, text, text, jsonb, text, text, text, text, text, text, text, timestamptz, text, bool, text, text, text, text, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_hl7_request(text, text, jsonb, text, text, text, jsonb, text, text, jsonb, text, text, text, text, text, text, text, timestamptz, text, bool, text, text, text, text, text, text, text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.register_interaction_hl7_request(text, text, jsonb, text, text, text, jsonb, text, text, jsonb, text, text, text, text, text, text, text, timestamptz, text, bool, text, text, text, text, text, text, text, text, text);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.register_interaction_hl7_request(p_interaction_id text, p_interaction_key text, p_nature jsonb, p_from_state text, p_to_state text, p_hub_nexus_interaction_id text DEFAULT NULL::text, p_payload jsonb DEFAULT NULL::jsonb, p_payload_text text DEFAULT NULL::text, p_rule_namespace text DEFAULT NULL::text, p_elaboration jsonb DEFAULT NULL::jsonb, p_content_type text DEFAULT NULL::text, p_state_transition_reason text DEFAULT NULL::text, p_user_id text DEFAULT NULL::text, p_user_name text DEFAULT NULL::text, p_user_session text DEFAULT NULL::text, p_user_role text DEFAULT NULL::text, p_created_by text DEFAULT CURRENT_USER, p_created_at timestamp with time zone DEFAULT NULL::timestamp with time zone, p_provenance text DEFAULT NULL::text, p_hub_upsert_behavior boolean DEFAULT true, p_source_hub_interaction_id text DEFAULT NULL::text, p_client_ip_address text DEFAULT NULL::text, p_user_agent text DEFAULT NULL::text, p_origin text DEFAULT NULL::text, p_source_type text DEFAULT NULL::text, p_group_hub_interaction_id text DEFAULT NULL::text, p_request_source text DEFAULT NULL::text, p_techbd_version_number text DEFAULT NULL::text, p_file_name text DEFAULT NULL::text)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_error_msg 		TEXT;
    v_error_type 		TEXT;
    v_sqlstate 			TEXT;
    v_pg_detail 		TEXT;
    v_pg_hint 			TEXT;
    v_pg_context 		TEXT;
    v_created_at 		TIMESTAMPTZ := COALESCE(p_created_at, CURRENT_TIMESTAMP);
    v_created_by 		TEXT 		:= COALESCE(p_created_by, CURRENT_USER);  -- Default created_by to the current user if not provided
    v_provenance 		TEXT 		:= COALESCE(p_provenance, 'HL7');
    v_exception_id 		TEXT		:= gen_random_uuid()::text;
   	v_nature_denorm 	TEXT		:= trim(p_nature->>'nature');
   	v_tenant_id_denorm 	TEXT		:= p_nature->>'tenant_id';
   
   	v_from_state				TEXT;
   	v_to_state					TEXT;
   	v_state_transition_reason 	TEXT;
   	v_nature 					JSONB;
   	v_return 					JSONB 	:= NULL;
	v_return_status				BOOLEAN := false;
	v_is_exception				BOOLEAN := false;
   	v_disposition_json			JSONB 	:= NULL;
   	v_payload					JSONB 	:= NULL;
    v_elaboration 				JSONB 	:= NULL;
	v_elaboration_steps 		JSONB 	:= '[]'::jsonb;
	v_elaboration_steps_text 	TEXT[]  := ARRAY[]::text[]; 
    v_elaboration_steps_jsonb 	JSONB   := '[]'::jsonb;
    v_user_id 					TEXT  	:= COALESCE(p_user_id, v_created_by);	
	v_additional_details		JSONB 	:= jsonb_build_object(
										    'request', jsonb_build_object(
										        'User-Agent', p_user_agent,
										        'remoteAddr', p_client_ip_address,
										        'X-Observability-Metric-Interaction-Start-Time', v_created_at,
										        'X-Observability-Metric-Interaction-Finish-Time', v_created_at
										    ),
											'version', p_techbd_version_number
										);
   
BEGIN 
	/*
	 This function handles the registration of HL7 interaction requests into various hub and satellite tables within the techbd_udi_ingress schema.
	 Handles optional linkage to a nexus interaction, logs FHIR request details, and saves user information if provided. 	
	 It first attempts to insert a record into the hub_interaction table, handling unique constraint violations based on the hub_upsert_behavior flag.
	 It then logs request details into the sat_interaction_http_request table, including payload, state transitions, and user information.
	 Based on the interaction key and request nature, it conditionally calls upsert functions for user, HTTP, or FHIR data. In case of an error, the 
	 function logs the exception using the register_issue function.
	 Returns a JSONB object representing either the inserted data or an error.
	 - origin: http/sftp
	 - source_type: fhir/hl7/csv/ccda
	*/
	
	v_elaboration_steps_text := array_append(v_elaboration_steps_text, '0: In register_interaction_hl7_request function');

	IF v_nature_denorm NOT IN('Original HL7 Payload', 'HL7 Validation Result', 'Converted to FHIR') THEN
		v_return_status := true;
		v_return := jsonb_build_object('error', 'Not a valid HL7 nature',
								  'payload', p_payload,
								  'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
								  'interaction_id', p_interaction_id);
	END IF;
	IF NOT v_return_status AND p_interaction_key NOT IN('/hl7v2/Bundle', '/hl7v2/Bundle/', '/hl7v2/Bundle/$validate', '/hl7v2/Bundle/$validate/') THEN
		v_return_status := true;
		v_return := jsonb_build_object('error', 'Not a valid HL7 interaction_key',
								  'payload', p_payload,
								  'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
								  'interaction_id', p_interaction_id);
	END IF;

    -- Check hub_nexus_interaction exists
    IF NOT v_return_status AND p_hub_nexus_interaction_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM techbd_udi_ingress.hub_nexus_interaction
        WHERE hub_nexus_interaction_id = p_hub_nexus_interaction_id
    ) THEN
        v_return_status := true;
		v_return := jsonb_build_object('error', 'p_hub_nexus_interaction_id does not exists in the hub_nexus_interaction table', 
								  'payload', null,
								  'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
								  'interaction_id', p_interaction_id);
    END IF;

	IF NOT v_return_status THEN
	    -- Attempt to insert into hub_interaction
		BEGIN	   	    
	        INSERT INTO techbd_udi_ingress.hub_interaction (hub_interaction_id, key, created_at, created_by, provenance)
	        VALUES (p_interaction_id, p_interaction_key, v_created_at, v_created_by, v_provenance);

	        v_elaboration_steps_text := array_append(v_elaboration_steps_text, '1: Inserted into hub_interaction');
	    EXCEPTION
	        WHEN unique_violation THEN
	            IF NOT p_hub_upsert_behavior THEN
	                -- Capture exception details
	                GET STACKED DIAGNOSTICS
	                    v_error_msg = MESSAGE_TEXT,
	                    v_sqlstate = RETURNED_SQLSTATE,
	                    v_pg_detail = PG_EXCEPTION_DETAIL,
	                    v_pg_hint = PG_EXCEPTION_HINT,
	                    v_pg_context = PG_EXCEPTION_CONTEXT;
	                    v_error_type = 'SQL';
	
	                v_is_exception := true;
	            END IF; 
	       WHEN OTHERS THEN
	        	GET STACKED DIAGNOSTICS
	                v_error_msg = MESSAGE_TEXT,
	                v_sqlstate = RETURNED_SQLSTATE,
	                v_pg_detail = PG_EXCEPTION_DETAIL,
	                v_pg_hint = PG_EXCEPTION_HINT,
	                v_pg_context = PG_EXCEPTION_CONTEXT;
	                v_error_type = 'SQL';
	
	         	v_is_exception := true;				
				v_return_status := true;
				v_return := jsonb_build_object('error', 'Insert into hub_interaction failed', 
									  	 'payload', null,
										 'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
										 'interaction_id', p_interaction_id);
	    END;
	
		-- Insert into link table
		IF NOT v_return_status AND p_hub_nexus_interaction_id IS NOT NULL THEN
			INSERT INTO techbd_udi_ingress.link_nexus_interaction (
				hub_nexus_interaction_id, hub_interaction_id, created_by, created_at, provenance
			) VALUES (
				p_hub_nexus_interaction_id, p_interaction_id, v_created_by, v_created_at, v_provenance
			) ON CONFLICT DO NOTHING;

			v_elaboration_steps_text := array_append(v_elaboration_steps_text, '2: Inserted into link_nexus_interaction');
		END IF;
		
		IF NOT v_return_status THEN
		    -- Attempt to insert into sat_interaction_http_request
		    BEGIN
			    -- The insertion to sat_interaction_http_request table needs only once. Need to be corrected later.
		        INSERT INTO techbd_udi_ingress.sat_interaction_http_request (
		            sat_interaction_http_request_id, hub_interaction_id, nature, content_type, payload, payload_text, from_state, 
		            to_state, state_transition_reason, elaboration, created_at, created_by, provenance, nature_denorm, tenant_id_denorm, request_source, techbd_version_number, tenant_id
		        )
		        VALUES (
		            gen_random_uuid()::text, p_interaction_id, p_nature, p_content_type, p_payload, p_payload_text, p_from_state, p_to_state, 
		            p_state_transition_reason, p_elaboration, v_created_at, v_created_by, v_provenance, v_nature_denorm, v_tenant_id_denorm, p_request_source, p_techbd_version_number, v_tenant_id_denorm
		        );
		        
				v_elaboration_steps_text := array_append(v_elaboration_steps_text, '3: Inserted into sat_interaction_http_request');

		       	--1) Call the function with the defined parameters to upsert user info
		       	IF(v_nature_denorm = 'Original HL7 Payload')	THEN     		       	
			    	PERFORM techbd_udi_ingress.sat_interaction_user_upserted(
				        hub_interaction_id 			=> p_interaction_id,
				        hub_interaction_key			=> p_interaction_key, 
			            nature						=> v_nature_denorm,
				        payload						=> p_payload, 
				        user_id						=> p_user_id, 
				        user_name					=> p_user_name, 
				        user_session				=> p_user_session, 
				        user_role					=> p_user_role,
				        tenant_id					=> v_tenant_id_denorm,
				        elaboration					=> p_elaboration,
				        created_at 					=> v_created_at,
				        created_by 					=> v_created_by,
				        provenance 					=> v_provenance,
						p_additional_details		=> v_additional_details
			       	); 

			      	v_elaboration_steps_text := array_append(v_elaboration_steps_text, '4: Inserted into user info');
			    END IF;
			   
			    --2.4) Call the function with the defined parameters to upsert interaction HL7 request info.
			    IF v_nature_denorm IN ('Original HL7 Payload', 'Converted to FHIR', 'HL7 Validation Result') THEN
					PERFORM techbd_udi_ingress.sat_interaction_hl7_request_upserted(
				        p_hub_interaction_id 		=> p_interaction_id,
				        p_tenant_id					=> v_tenant_id_denorm,
				        p_uri						=> p_interaction_key,
				        p_nature					=> v_nature_denorm,
				        p_from_state				=> p_from_state,
				        p_to_state					=> p_to_state,
				        p_payload					=> p_payload,
						p_hl7_payload_text			=> p_payload_text,
				        p_state_transition_reason	=> p_state_transition_reason,
				        p_created_at				=> v_created_at,
				        p_created_by				=> v_created_by,
				        p_provenance				=> v_provenance,
				        p_elaboration				=> p_elaboration,
						p_client_ip_address			=> p_client_ip_address,
						p_user_agent				=> p_user_agent,
						p_origin					=> p_origin,
						p_techbd_version_number		=> p_techbd_version_number,
						p_file_name					=> p_file_name
			       	);
					v_return := jsonb_build_object('payload', p_payload,
											 'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
											 'interaction_id', p_interaction_id);
		
					v_elaboration_steps_text := array_append(v_elaboration_steps_text, '5: Inserted into sat_interaction_hl7_request'); 
		       	ELSE
					v_return := jsonb_build_object('error','Not a valid HL7 nature',
											 'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
											 'interaction_id', p_interaction_id);
				END IF;	
				
		    EXCEPTION
		        WHEN OTHERS THEN
		            -- Capture exception details
		            GET STACKED DIAGNOSTICS
		                v_error_msg = MESSAGE_TEXT,
		                v_sqlstate = RETURNED_SQLSTATE,
		                v_pg_detail = PG_EXCEPTION_DETAIL,
		                v_pg_hint = PG_EXCEPTION_HINT,
		                v_pg_context = PG_EXCEPTION_CONTEXT;
		                v_error_type = 'SQL';
		
		         	v_is_exception := true;		         	
					v_return := jsonb_build_object('error', v_error_msg, 
										  	 'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
											 'interaction_id', p_interaction_id);
		    END;
		END IF;
	END IF;

   	-- Save the log details into table
	v_elaboration := jsonb_build_object(
							'hub_nexus_interaction_id', p_hub_nexus_interaction_id,
							'interaction_id', p_interaction_id,
							'interaction_key', p_interaction_key,
							'nature', p_nature,
							'from_state', p_from_state,
							'to_state', p_to_state,
							'techbd_version_number', p_techbd_version_number,
							'file_name', p_file_name
							);
	-- Convert steps array to JSONB and attach
    IF array_length(v_elaboration_steps_text, 1) IS NOT NULL THEN
        v_elaboration_steps_jsonb := to_jsonb(v_elaboration_steps_text);
        v_elaboration := v_elaboration || jsonb_build_object('steps', v_elaboration_steps_jsonb);
    END IF;

    -- Attach error message if present
    IF v_error_msg IS NOT NULL THEN
        v_elaboration := v_elaboration || jsonb_build_object('error_message', v_error_msg);
    END IF;

    -- Register diagnostic log
    CALL techbd_udi_ingress.register_diagnostic_log(v_exception_id, p_interaction_key, v_created_by, v_provenance, 'info'::text, 
       																'register_interaction_hl7_request Logs'::text, v_user_id, 'success'::text, NULL::text, 0, v_elaboration, v_tenant_id_denorm, p_interaction_id);

	IF v_is_exception = true THEN    
        -- Log the exception, reusing the previous exception ID if it exists
        v_elaboration := v_elaboration || jsonb_build_array(jsonb_build_object('error_message', v_error_msg));
        v_exception_id := techbd_udi_ingress.register_issue(
            v_exception_id, p_interaction_key, v_error_msg, v_error_type, v_sqlstate, v_pg_detail, v_pg_hint, v_pg_context, v_created_by, v_provenance, v_elaboration, v_tenant_id_denorm, p_interaction_id
        );
	END IF;
   	RETURN v_return;
END;
$function$
;

/*******************************************************************************************
This view provides a simplified representation of CSV validation results by selecting 
the hub interaction ID, the corresponding ZIP file request ID, and the associated 
validation result payload from the sat_interaction_zip_file_request table.
*******************************************************************************************/

DROP VIEW IF EXISTS techbd_udi_ingress.interaction_csv_http_validation_result CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_csv_http_validation_result
AS SELECT hub_interaction_id,
    sat_interaction_zip_file_request_id,
    validation_result_payload
   FROM techbd_udi_ingress.sat_interaction_zip_file_request sizfr;

/*******************************************************************************************
 * View for CSV Zip File Download
 *
 * This view provides access to CSV zip file content stored in the database for download
 * purposes. It exposes the hub_interaction_id and the corresponding zip file content
 * (stored as bytea) that can be retrieved and downloaded by the frontend application.
 *
 * Usage: Used by the downloadZipFile JavaScript function to fetch and download
 * CSV zip files associated with specific interaction IDs.
 ******************************************************************************************/
DROP VIEW IF EXISTS techbd_udi_ingress.interaction_csv_zip_download CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_csv_zip_download AS
SELECT
    hub_interaction_id,
    csv_zip_file_content
FROM techbd_udi_ingress.sat_interaction_zip_file_request;

/*******************************************************************************************
 * Stored Procedure for CSV Data Integrity Errors (Replacement for View)
 *
 * This stored procedure replaces the csv_data_integrity_errors view by accepting
 * zip_file_hub_interaction_id as a parameter instead of having it hardcoded.
 *
 * Parameters:
 * - p_zip_file_hub_interaction_id: The zip file interaction ID to filter errors for
 *
 * Returns: CSV data integrity error records for the specified zip file interaction
 ******************************************************************************************/
DROP FUNCTION IF EXISTS techbd_udi_ingress.get_csv_data_integrity_errors(TEXT);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.get_csv_data_integrity_errors(
    p_zip_file_hub_interaction_id TEXT
)
RETURNS TABLE (
    created_at TIMESTAMPTZ,
    tenant_id TEXT,
    uri TEXT,
    zip_file_hub_interaction_id TEXT,
    hub_interaction_id TEXT,
    group_id TEXT,
    section TEXT,
    fieldname TEXT,
    value TEXT,
    error_type TEXT,
    error TEXT,
    description TEXT,
    rownumber INTEGER,
    fieldnumber INTEGER,
    file_name TEXT
) AS $$
BEGIN
    RETURN QUERY
	SELECT
			scfpe.created_at,
			scfpe.tenant_id,
			scfpe.uri,
			scfpe.zip_file_hub_interaction_id,
			scfpe.flat_file_hub_interaction_id,
			scfpe.group_id,
			scfpe.section,
			scfpe.field_name as fieldname,
			scfpe.value,
			scfpe.error_type, 
			scfpe.error,
			scfpe.description,
			scfpe."row_number"::integer  as rownumber,
			scfpe.field_number::integer  as fieldnumber,
			scfpe.file_name 
	FROM
		techbd_udi_ingress.sat_csv_fhir_processing_errors scfpe 
	where  
		scfpe.zip_file_hub_interaction_id = p_zip_file_hub_interaction_id 
		AND scfpe.category = 'data_integrity'
	ORDER BY
	scfpe.created_at DESC;
END;
$$ LANGUAGE plpgsql;

/*******************************************************************************************
 * Stored Procedure for HTTP Interaction Requests (Replacement for View)
 *
 * This stored procedure replaces the interaction_http_request view by accepting
 * start_date and end_date as parameters instead of having no date filtering.
 *
 * Parameters:
 * - start_date: The start date for filtering (format: MM-DD-YYYY)
 * - end_date: The end date for filtering (format: MM-DD-YYYY)
 *
 * Returns: HTTP interaction request records for the specified date range
 ******************************************************************************************/
DROP FUNCTION IF EXISTS techbd_udi_ingress.get_interaction_http_request(TEXT, TEXT);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.get_interaction_http_request(
    start_date TEXT,
    end_date TEXT
)
RETURNS TABLE (
    interaction_id TEXT,
    uri TEXT,
    interaction_created_at TIMESTAMP WITH TIME ZONE,
    interaction_created_by TEXT,
    interaction_provenance TEXT,
    sat_interaction_http_request_id TEXT,
    nature TEXT,
    tenant_id TEXT,
    user_agent TEXT,
    client_ip_address TEXT,
    content_type TEXT,
    elaboration JSONB, 
    request_created_at TIMESTAMP WITH TIME ZONE,
    request_created_by TEXT,
    request_provenance TEXT 
) AS $$
BEGIN
    RETURN QUERY
        SELECT
            user_req.hub_interaction_id AS interaction_id,
            user_req.uri,
            user_req.created_at AS interaction_created_at,
            user_req.created_by AS interaction_created_by,
            user_req.provenance AS interaction_provenance,
            user_req.sat_interaction_user_id AS sat_interaction_http_request_id,
            user_req.nature, 
            user_req.tenant_id,
            user_req.user_agent,
            user_req.client_ip_address,
            'application/json'::text AS content_type,
            user_req.elaboration, 
            user_req.created_at AS request_created_at,
            user_req.created_by AS request_created_by,
            user_req.provenance AS request_provenance
        FROM techbd_udi_ingress.sat_interaction_user user_req
        WHERE user_req.uri NOT ILIKE '%/Bundle%' 
          AND user_req.created_at >= TO_DATE(start_date, 'MM-DD-YYYY')
          AND user_req.created_at <  TO_DATE(end_date, 'MM-DD-YYYY') + INTERVAL '1 day';
END;
$$ LANGUAGE plpgsql;


DROP FUNCTION IF EXISTS techbd_udi_ingress.denormalize_validation_errors(text, text);
DROP FUNCTION IF EXISTS techbd_udi_ingress.denormalize_validation_errors(text, text, text);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.denormalize_validation_errors(p_category text, p_hub_interaction_id text, p_uri text DEFAULT NULL::text)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    -- Declare variables to hold error and extracted data
	v_error_msg 		TEXT;
    v_error_type 		TEXT;
    v_sqlstate 			TEXT;
    v_pg_detail 		TEXT;
    v_pg_hint 			TEXT;
    v_pg_context 		TEXT;   
    v_created_at 		TIMESTAMPTZ := CURRENT_TIMESTAMP;  -- Ensure created_at is not null
    v_created_by 		TEXT 		:= CURRENT_USER;  -- Default created_by to the current user if not provided
    v_provenance 		TEXT 		:= 'CSV';  -- Set default provenance
    v_exception_id 		TEXT 	:= gen_random_uuid()::text;
	v_is_exception		BOOLEAN := false;
    v_elaboration 		JSONB 	:= NULL;
	v_elaboration_steps JSONB 	:= '[]'::jsonb;
	v_elaboration_steps_text 	TEXT[] 	:= ARRAY[]::text[]; 
    v_elaboration_steps_jsonb 	JSONB 	:= '[]'::jsonb;
    v_diagnostic_id 	TEXT  	:= gen_random_uuid()::text;
	v_inserted_count 	integer	:= 0;
	v_return			TEXT;
	v_return_status		BOOLEAN := false;
	v_uri				TEXT	:= COALESCE(p_uri, '/flatfile/csv/Bundle');
BEGIN
	BEGIN
		v_elaboration_steps_text := array_append(v_elaboration_steps_text, '0: In denormalize_validation_errors function');
		
		IF p_category IS NULL OR p_hub_interaction_id IS NULL THEN
	        v_return_status := true;
			v_return := NULL;
			v_error_msg := 'p_category AND p_hub_interaction_id should not be NULL.';
	    END IF;
	
		IF NOT v_return_status THEN			   
			-- data_integrity errors
			IF p_category = 'data_integrity' AND p_hub_interaction_id IS NOT NULL THEN
				INSERT INTO techbd_udi_ingress.sat_csv_fhir_processing_errors (
							sat_validation_and_fhir_conversion_errors_id, category, flat_file_hub_interaction_id, created_at, tenant_id, uri, 
							zip_file_hub_interaction_id, group_id, section, field_name, value, error_type, error_subtype, error, description, 
							row_number, field_number, zip_file_name, file_name, origin, user_agent, created_by, provenance, techbd_version_number)
					SELECT 
							gen_random_uuid()::text,
							'data_integrity',
							p_hub_interaction_id, -- flat_file_hub_interaction_id
							flat.created_at,
							flat.tenant_id,
							flat.uri,
							flat.zip_file_hub_interaction_id,
							flat.group_id,
							tasks.value ->> 'name' AS section,
							COALESCE(error_payload.value ->> 'fieldName',
									errors_summary.value ->> 'fieldName') AS field_name,
							error_payload.value ->> 'cell' AS value,
							COALESCE(error_payload.value ->> 'title',
									errors_summary.value ->> 'type') AS error_type,
							NULL,
							COALESCE(error_payload.value ->> 'message',
									errors_summary.value ->> 'message') AS error,
							error_payload.value ->> 'description' AS description,
							(error_payload.value ->> 'rowNumber')::integer AS row_number,
							(error_payload.value ->> 'fieldNumber')::integer AS field_number,
							zip.csv_zip_file_name,
							CASE
				                WHEN tasks.value ->> 'name' = 'qe_admin_data' THEN orig.qe_admin_data_file_name
				                WHEN tasks.value ->> 'name' = 'demographic_data' THEN orig.demographic_data_file_name
				                WHEN tasks.value ->> 'name' = 'screening_observation_data' THEN orig.screening_observation_data_file_name
				                WHEN tasks.value ->> 'name' = 'screening_profile_data' THEN orig.screening_profile_data_file_name
				                ELSE NULL
				            END AS file_name,
							zip.origin,
							COALESCE(zip.user_agent, orig.user_agent), 
							v_created_by, 
							v_provenance, 
							flat.techbd_version_number 
					FROM techbd_udi_ingress.sat_interaction_flat_file_csv_request AS flat
					LEFT JOIN techbd_udi_ingress.sat_interaction_flat_file_csv_request orig ON orig.hub_interaction_id = flat.hub_interaction_id AND orig.nature = 'Original Flat File CSV'
					CROSS JOIN LATERAL jsonb_array_elements(((flat.validation_result_payload -> 'validationResults') -> 'report') -> 'tasks') AS tasks(value)
					LEFT JOIN LATERAL jsonb_array_elements(tasks.value -> 'errors') AS error_payload(value) ON true
					LEFT JOIN LATERAL jsonb_array_elements((flat.validation_result_payload -> 'validationResults') -> 'errorsSummary') AS errors_summary(value) ON true
					INNER JOIN techbd_udi_ingress.sat_interaction_zip_file_request AS zip 
						ON zip.hub_interaction_id = flat.zip_file_hub_interaction_id
					WHERE flat.nature = 'CSV Validation Result'
					AND (
							((((flat.validation_result_payload -> 'validationResults') -> 'report') -> 'stats') ->> 'errors')::integer > 0
							OR jsonb_array_length((flat.validation_result_payload -> 'validationResults') -> 'errorsSummary') > 0
						)
					AND COALESCE(error_payload.value ->> 'title', errors_summary.value ->> 'type') IS NOT NULL
					AND flat.hub_interaction_id = p_hub_interaction_id;
		
				GET DIAGNOSTICS v_inserted_count = ROW_COUNT;
		
				v_elaboration_steps_text := array_append(v_elaboration_steps_text, '3: Inserted into sat_csv_fhir_processing_errors - data_integrity');
		
			END IF;

			-- Processing, file_not_processed and incomplete_groups Errors from techbd_udi_ingress.sat_interaction_zip_file_request.full_operation_outcome
			IF p_category IN ('processing_errors', 'file_not_processed', 'incomplete_groups') AND p_hub_interaction_id IS NOT NULL THEN
				
				--For validate, the structure of full_operation_outcome is different, so writing separate queries
				IF v_uri IN ('/flatfile/csv/Bundle/$validate', '/flatfile/csv/Bundle/$validate/') THEN 	
					
					-- File Not Processed Errors
					INSERT INTO techbd_udi_ingress.sat_csv_fhir_processing_errors (
								sat_validation_and_fhir_conversion_errors_id, category, flat_file_hub_interaction_id, created_at, tenant_id, uri, 
								zip_file_hub_interaction_id, group_id, section, field_name, value, error_type, error_subtype, error, description, 
								row_number, field_number, zip_file_name, file_name, origin, user_agent, created_by, provenance, techbd_version_number)
					SELECT gen_random_uuid()::text,
						        --p_category,
								CASE issue.value ->> 'type' 
									WHEN 'processing-error'  	THEN 'processing_errors'
									WHEN 'files-not-processed' 	THEN 'file_not_processed'
									WHEN 'incomplete_groups' 	THEN 'incomplete_groups'
									WHEN 'missing-file-error' 	THEN 'incomplete_groups'
									WHEN 'data-integrity' 		THEN 'data_integrity'
									ELSE p_category
								END AS category,
						        null,--flat_file_hub_interaction_id
						        v_created_at,
						        tenant_id,
						        uri,
						        p_hub_interaction_id,--zip_file_hub_interaction_id
						        group_id,
						        NULL,
						        NULL,
						        NULL,
						        issue.value ->> 'type'        AS error_type,
						        issue.value ->> 'subType'     AS error_subtype,
						        issue.value ->> 'reason'      AS error,
						        issue.value ->> 'description' AS description,
						        NULL,
						        NULL,
						        csv_zip_file_name,
						        --jsonb_array_elements_text(issue.value -> 'files') AS file_name,
								file_name.value AS file_name,
						        origin,
						        user_agent,
								v_created_by,
								v_provenance,
								techbd_version_number
						FROM 	techbd_udi_ingress.sat_interaction_zip_file_request,
						LATERAL jsonb_array_elements(COALESCE(general_errors, '[]'::jsonb)) AS general_error(value)
						CROSS JOIN LATERAL jsonb_array_elements(general_error.value -> 'validationResults' -> 'errors') AS issue(value)
						CROSS JOIN LATERAL jsonb_array_elements_text(issue.value -> 'files') AS file_name(value)
						WHERE 	hub_interaction_id = p_hub_interaction_id;
			
					GET DIAGNOSTICS v_inserted_count = ROW_COUNT;
			
					v_elaboration_steps_text := array_append(v_elaboration_steps_text, '1: Inserted into sat_csv_fhir_processing_errors - file_not_processed');
				
					-- incomplete_groups 
					INSERT INTO techbd_udi_ingress.sat_csv_fhir_processing_errors (
								sat_validation_and_fhir_conversion_errors_id, category, flat_file_hub_interaction_id, created_at, tenant_id, uri, 
								zip_file_hub_interaction_id, group_id, section, field_name, value, error_type, error_subtype, error, description, 
								row_number, field_number, zip_file_name, file_name, origin, user_agent, created_by, provenance, techbd_version_number)
						SELECT 	gen_random_uuid()::text,
						       	--p_category,
								CASE error_payload.value ->> 'type'
									WHEN 'processing-error'  	THEN 'processing_errors'
									WHEN 'files-not-processed' 	THEN 'file_not_processed'
									WHEN 'incomplete_groups' 	THEN 'incomplete_groups'
									WHEN 'missing-file-error' 	THEN 'incomplete_groups'
									WHEN 'data-integrity' 		THEN 'data_integrity'
									ELSE p_category
								END AS category,
						       	flat.hub_interaction_id, --flat_file_hub_interaction_id
						       	v_created_at,
						       	flat.tenant_id,
								flat.uri,
								zip_file_hub_interaction_id,
								flat.group_id,
								NULL,
								NULL,
								NULL,
								error_payload.value ->> 'type'        AS error_type,
								NULL,
								error_payload.value ->> 'message'     AS error,
								error_payload.value ->> 'description' AS description,
								NULL,
								NULL,
								zip.csv_zip_file_name,
								NULL,
								zip.origin, 
			    				COALESCE(zip.user_agent, flat.user_agent), 
								v_created_by, 
								v_provenance, 
								flat.techbd_version_number
						FROM techbd_udi_ingress.sat_interaction_flat_file_csv_request AS flat
						CROSS JOIN LATERAL jsonb_array_elements((flat.validation_result_payload -> 'validationResults') -> 'errors') AS error_payload(value)
						INNER JOIN techbd_udi_ingress.sat_interaction_zip_file_request AS zip ON zip.hub_interaction_id = flat.zip_file_hub_interaction_id
						WHERE  
							(flat.nature = 'CSV Validation Result' OR flat.nature = 'Converted to FHIR')
							AND jsonb_array_length(
									COALESCE((flat.validation_result_payload -> 'validationResults') -> 'errors', '[]'::jsonb)
								) > 0
							AND flat.zip_file_hub_interaction_id = p_hub_interaction_id;
			
					GET DIAGNOSTICS v_inserted_count = ROW_COUNT;
			
					v_elaboration_steps_text := array_append(v_elaboration_steps_text, '2: Inserted into sat_csv_fhir_processing_errors - incomplete_groups');
	
				ELSE --/Bundle
					INSERT INTO techbd_udi_ingress.sat_csv_fhir_processing_errors (
								sat_validation_and_fhir_conversion_errors_id, category, flat_file_hub_interaction_id, created_at, tenant_id, uri, 
								zip_file_hub_interaction_id, group_id, section, field_name, value, error_type, error_subtype, error, description, 
								row_number, field_number, zip_file_name, file_name, origin, user_agent, created_by, provenance, techbd_version_number)
					SELECT gen_random_uuid()::text,
						        --p_category,
								CASE err ->> 'type'
									WHEN 'processing-error'  	THEN 'processing_errors'
									WHEN 'files-not-processed' 	THEN 'file_not_processed'
									WHEN 'incomplete_groups' 	THEN 'incomplete_groups'
									WHEN 'missing-file-error' 	THEN 'incomplete_groups'
									WHEN 'data-integrity' 		THEN 'data_integrity'
									ELSE p_category
								END AS category,
						        o -> 'provenance' ->> 'groupInteractionId' as flat_file_hub_interaction_id,
						        v_created_at,
						        tenant_id,
						        uri,
						        p_hub_interaction_id,--zip_file_hub_interaction_id
						        o -> 'provenance' ->> 'groupInteractionId' as group_id,
						        NULL,
						        NULL,
						        NULL,
						        err ->> 'type'        AS error_type,
						        NULL,
						        COALESCE(err ->> 'message', err ->> 'reason') AS error,
						        err ->> 'description' AS description,
						        NULL,
						        NULL,
						        csv_zip_file_name,
						        STRING_AGG(DISTINCT file_elem, ', ' ORDER BY file_elem) AS file_name,
						        origin,
						        user_agent,
								v_created_by,
								v_provenance,
								techbd_version_number
						FROM 	techbd_udi_ingress.sat_interaction_zip_file_request
						CROSS JOIN LATERAL jsonb_array_elements(COALESCE(full_operation_outcome, '[]'::jsonb)) AS o
						CROSS JOIN LATERAL jsonb_array_elements(COALESCE(o -> 'validationResults' -> 'errors', '[]'::jsonb)) AS err
						LEFT  JOIN LATERAL jsonb_array_elements_text(COALESCE(err -> 'files', '[]'::jsonb)) AS file_elem ON TRUE
						WHERE hub_interaction_id = p_hub_interaction_id
						GROUP BY category, flat_file_hub_interaction_id, tenant_id, uri, group_id, error_type, error, description, csv_zip_file_name, origin, user_agent, techbd_version_number; 
						
					GET DIAGNOSTICS v_inserted_count = ROW_COUNT;
			
					v_elaboration_steps_text := array_append(v_elaboration_steps_text, '1: Inserted into sat_csv_fhir_processing_errors - processing_errors, file_not_processed and incomplete_groups from full_operation_outcome');
				END IF;
			END IF;
			
			v_return := p_category || ': ' || v_inserted_count::text || ' records inserted.';
		END IF;
	EXCEPTION
	   WHEN OTHERS THEN
		    -- Capture exception details
			GET STACKED DIAGNOSTICS
		        v_error_msg = MESSAGE_TEXT,
		        v_sqlstate = RETURNED_SQLSTATE,
		        v_pg_detail = PG_EXCEPTION_DETAIL,
		        v_pg_hint = PG_EXCEPTION_HINT,
		        v_pg_context = PG_EXCEPTION_CONTEXT;
		        v_error_type = 'SQL';
			v_is_exception := true;				
			v_return_status := true;
			v_return := null;
	END;

	-- Save the log details into table
	v_elaboration := jsonb_build_object(
			 						'category', p_category,
			 						'flat_file_csv_request_hub_interaction_id', p_hub_interaction_id,
									'rows inserted', v_inserted_count
			 						);
	-- Convert steps array to JSONB and attach
    IF array_length(v_elaboration_steps_text, 1) IS NOT NULL THEN
        v_elaboration_steps_jsonb := to_jsonb(v_elaboration_steps_text);
        v_elaboration := v_elaboration || jsonb_build_object('steps', v_elaboration_steps_jsonb);
    END IF;

    -- Attach error message if present
    IF v_error_msg IS NOT NULL THEN
        v_elaboration := v_elaboration || jsonb_build_object('error_message', v_error_msg);
    END IF;

    -- Register diagnostic log
    CALL techbd_udi_ingress.register_diagnostic_log(v_exception_id, null, v_created_by, v_provenance, 'info'::text, 
       																'denormalize_validation_errors function Logs'::text, v_created_by, 'success'::text, NULL::text, 0, v_elaboration, null, p_hub_interaction_id);

	IF v_is_exception = true THEN    
        -- Log the exception, reusing the previous exception ID if it exists
        v_elaboration := v_elaboration || jsonb_build_array(jsonb_build_object('error_message', v_error_msg));
        v_exception_id := techbd_udi_ingress.register_issue(
            v_exception_id, null, v_error_msg, v_error_type, v_sqlstate, v_pg_detail, v_pg_hint, v_pg_context, v_created_by, v_provenance, v_elaboration, null, p_hub_interaction_id
        );
	END IF;
   	RETURN v_return;
END;
$function$;

DROP FUNCTION IF EXISTS techbd_udi_ingress.upsert_dashboard_widget_metadata(text, text, timestamptz, text);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.upsert_dashboard_widget_metadata(
    p_widget_name TEXT,
    p_tenant_id TEXT,
    p_last_updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    p_last_updated_by TEXT DEFAULT CURRENT_USER
)
RETURNS TEXT AS $$
DECLARE
    allowed_widgets TEXT[] := ARRAY['FHIR', 'CSV', 'CCDA', 'HL7V2'];
	v_exception_id	TEXT;
    v_error_msg		TEXT;
    v_error_type	TEXT;
    v_sqlstate		TEXT;
    v_pg_detail		TEXT;
    v_pg_hint		TEXT;
    v_pg_context	TEXT;
BEGIN
	/*
	 * Inserts or updates dashboard widget metadata for a given widget_name and tenant_id. 
	 * If either widget_name or tenant_id is NULL, the function returns without changes. 
	 * Updates last_updated_at and last_updated_by if the record exists.
	 */
	-- Check for NULL required parameters
	IF p_widget_name IS NULL OR p_tenant_id IS NULL THEN
	    RETURN 'Error: Missing required field(s): ' ||
	           CASE WHEN p_widget_name IS NULL AND p_tenant_id IS NULL THEN 'widget_name, tenant_id'
	                WHEN p_widget_name IS NULL THEN 'widget_name'
	                WHEN p_tenant_id IS NULL THEN 'tenant_id'
	           END || '.';
	END IF;

	-- Validate widget_name
    IF NOT UPPER(p_widget_name) = ANY (allowed_widgets) THEN
        RETURN 'Error: Invalid widget_name. Allowed values are FHIR, CSV, CCDA, HL7V2.';
    END IF;


    INSERT INTO techbd_udi_ingress.dashboard_widget_metadata (
		id,
        widget_name,
        tenant_id,
        last_updated_at,
        last_updated_by
    )
    VALUES (
		gen_random_uuid()::text,
        UPPER(p_widget_name),
        LOWER(p_tenant_id),
        COALESCE(p_last_updated_at, CURRENT_TIMESTAMP),
        COALESCE(p_last_updated_by, CURRENT_USER)
    )
    ON CONFLICT (widget_name, tenant_id)
    DO UPDATE SET
        last_updated_at = COALESCE(EXCLUDED.last_updated_at, CURRENT_TIMESTAMP),
        last_updated_by = COALESCE(EXCLUDED.last_updated_by, CURRENT_USER);
	
	RETURN 'Success: Dashboard widget metadata inserted/updated.';
EXCEPTION
   WHEN OTHERS THEN
	    -- Capture exception details
	    GET STACKED DIAGNOSTICS
	        v_error_msg = MESSAGE_TEXT,
	        v_sqlstate = RETURNED_SQLSTATE,
	        v_pg_detail = PG_EXCEPTION_DETAIL,
	        v_pg_hint = PG_EXCEPTION_HINT,
	        v_pg_context = PG_EXCEPTION_CONTEXT;
	        v_error_type = 'SQL';
		      
	    -- Log the exception, reusing the previous exception ID if it exists
		v_exception_id := techbd_udi_ingress.register_issue(
        COALESCE(v_exception_id, NULL),
		null,
		v_error_msg,
		v_error_type,
		v_sqlstate,
		v_pg_detail,
		v_pg_hint,
		v_pg_context,
		p_last_updated_by,
		'DashBoard-Widget', null::jsonb, p_tenant_id);
	RETURN 'Error';
END;
$$ LANGUAGE plpgsql;

DROP FUNCTION IF EXISTS techbd_udi_ingress.upsert_user(text, text, text, text);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.upsert_user(
    p_name TEXT,
    p_github_id TEXT,
	p_tenant_id TEXT DEFAULT NULL,
	p_role TEXT DEFAULT NULL
)
RETURNS TEXT AS $$
DECLARE
    v_exception_id	TEXT;
    v_error_msg		TEXT;
    v_error_type	TEXT;
    v_sqlstate		TEXT;
    v_pg_detail		TEXT;
    v_pg_hint		TEXT;
    v_pg_context	TEXT;
BEGIN
	-- Check for NULL required parameters
	IF p_name IS NULL OR p_github_id IS NULL THEN
	    RETURN 'Error: Missing required field(s): ' ||
	           CASE WHEN p_name IS NULL AND p_github_id IS NULL THEN 'name, github_id'
	                WHEN p_name IS NULL THEN 'name'
	                WHEN p_github_id IS NULL THEN 'github_id'
	           END || '.';
	END IF;

    INSERT INTO techbd_udi_ingress.users (
		id,
        github_id,
		name,
        tenant_id,
		role,
        created_at,
        created_by,
		provenance
    )
    VALUES (
		gen_random_uuid()::text,
        p_github_id,
        p_name,
		p_tenant_id,
		p_role,
        CURRENT_TIMESTAMP,
        CURRENT_USER,
		'user'
    )
    ON CONFLICT (github_id)
    DO UPDATE SET
		name = EXCLUDED.name,
        tenant_id = EXCLUDED.tenant_id,
        role = EXCLUDED.role,
        created_at = EXCLUDED.created_at,
        created_by = EXCLUDED.created_by;
	
	RETURN 'Success: user inserted/updated.';
EXCEPTION
   WHEN OTHERS THEN
	    -- Capture exception details
	    GET STACKED DIAGNOSTICS
	        v_error_msg = MESSAGE_TEXT,
	        v_sqlstate = RETURNED_SQLSTATE,
	        v_pg_detail = PG_EXCEPTION_DETAIL,
	        v_pg_hint = PG_EXCEPTION_HINT,
	        v_pg_context = PG_EXCEPTION_CONTEXT;
	        v_error_type = 'SQL';
		      
	    -- Log the exception, reusing the previous exception ID if it exists
		v_exception_id := techbd_udi_ingress.register_issue(
        COALESCE(v_exception_id, NULL),
		null,
		v_error_msg,
		v_error_type,
		v_sqlstate,
		v_pg_detail,
		v_pg_hint,
		v_pg_context,
		CURRENT_USER,
		'User', null::jsonb, p_tenant_id);
	RETURN format('Error: %s (SQLSTATE %s)', v_error_msg, v_sqlstate);
END;
$$ LANGUAGE plpgsql;


/**
-- View: interaction_ccda_https_request
-- This view lists all "Original CCDA Payload" interactions from the sat_interaction_ccda_request table.
-- It includes additional metadata such as file name, user agent, and client IP address.
-- The 'fhir_generated' column indicates whether the payload was converted to FHIR, and 
-- the 'ccda_validation_status' column shows whether CCDA validation succeeded or failed.
**/

DROP VIEW IF EXISTS techbd_udi_ingress.interaction_ccda_https_request CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_ccda_https_request AS
SELECT
    sicr.hub_interaction_id AS interaction_id,
	sicr.sat_interaction_ccda_request_id,
    sicr.tenant_id,
    sicr.uri,
    sicr.origin,
    sicr.created_at,
    sicr.techbd_version_number,
    sicr.ig_version,
    sicr.file_name, 
	sicr.user_agent,
    sicr.client_ip_address, 
    sicr.ccda_authoring_device,	
    htt_req.bundle_id,
    -- NEW FIELD: sat_interaction_ccda_request_id where to_state = 'CCDA Validation Result'
    (
        SELECT s4.sat_interaction_ccda_request_id
        FROM techbd_udi_ingress.sat_interaction_ccda_request s4
        WHERE s4.hub_interaction_id = sicr.hub_interaction_id
          AND s4.nature = 'CCDA Validation Result'
        LIMIT 1
    ) AS ccda_validation_result_request_id,
    CASE 
        WHEN EXISTS (
            SELECT 1
            FROM techbd_udi_ingress.sat_interaction_ccda_request s3
            WHERE s3.hub_interaction_id = sicr.hub_interaction_id
              AND s3.to_state = 'VALIDATION_SUCCESS'
        ) THEN 'Success'
        ELSE 'Failed'
    END AS ccda_validation_status  
FROM
    techbd_udi_ingress.sat_interaction_ccda_request sicr
left join   techbd_udi_ingress.sat_interaction_fhir_request htt_req
on  sicr.hub_interaction_id = htt_req.hub_interaction_id  and  htt_req.nature = 'Original FHIR Payload'
WHERE 
    sicr.nature = 'Original CCDA Payload';

/*******************************************************************************************
This view takes data from sat_interaction_fhir_request table to 
provide a consolidated view of HTTP FHIR requests via CCDA, including resource types, 
interaction details, request attributes, and validation issues.
******************************************************************************************/

DROP VIEW IF EXISTS techbd_udi_ingress.interaction_ccda_http_fhir_request CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_ccda_http_fhir_request
AS SELECT hub_interaction_id AS interaction_id,
    uri,
    bundle_id,
    created_at AS interaction_created_at,
    created_by AS interaction_created_by,
    provenance AS interaction_provenance,
    sat_interaction_fhir_request_id AS sat_interaction_http_request_id,
    nature,
    tenant_id,
    user_agent,
    client_ip_address,
    'application/json'::text AS content_type, 
    elaboration,
    from_state,
    to_state,
    state_transition_reason,
    created_at AS request_created_at,
    created_by AS request_created_by,
    provenance AS request_provenance,
    issues_count,
    resource_type_set AS resource_types,
    interaction_start_time::text AS start_time,
    interaction_end_time::text AS finish_time,
    EXTRACT(epoch FROM interaction_end_time::timestamp without time zone - interaction_start_time::timestamp without time zone) * 1000::numeric AS duration_millisecs,
    patient_id,
    patient_mrn,
    patient_mrn_source_system,
    organization_id,
    organization_name,
    concat_ws('-'::text, tenant_id, organization_id, patient_mrn) AS source_mrn,  
    bundle_type AS bundle_resource_type,
    source_hub_interaction_id,
	CASE
		WHEN is_bundle_valid = true THEN 'Valid'::text
		WHEN is_bundle_valid = false THEN 'Invalid'::text
		ELSE NULL::text
	END AS is_bundle_valid  
   FROM techbd_udi_ingress.sat_interaction_fhir_request htt_req
   WHERE source_type='CCDA';	


/**
-- View: interaction_hl7v2_https_request
-- This view lists all "Original HL7 Payload" interactions from the sat_interaction_ccda_request table.
-- It includes additional metadata such as file name, user agent, and client IP address.
-- The 'fhir_generated' column indicates whether the payload was converted to FHIR, and 
-- the 'interaction_hl7v2_https_request' column shows whether HL7 validation succeeded or failed.
**/

DROP VIEW IF EXISTS techbd_udi_ingress.interaction_hl7v2_https_request CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_hl7v2_https_request AS
SELECT
    sicr.hub_interaction_id AS interaction_id,
	sicr.sat_interaction_hl7_request_id,
    sicr.tenant_id,
    sicr.uri,
    sicr.origin,
    sicr.created_at,
    sicr.techbd_version_number,
    sicr.ig_version,
    sicr.file_name, 
	sicr.user_agent,
    sicr.client_ip_address, 
    htt_req.bundle_id,
    -- NEW FIELD: sat_interaction_hl7_request_id where to_state = 'HL7 Validation Result'
    (
        SELECT s4.sat_interaction_hl7_request_id
        FROM techbd_udi_ingress.sat_interaction_hl7_request s4
        WHERE s4.hub_interaction_id = sicr.hub_interaction_id
          AND s4.nature = 'HL7 Validation Result'
        LIMIT 1
    ) AS hl7_validation_result_request_id,
    CASE 
        WHEN EXISTS (
            SELECT 1
            FROM techbd_udi_ingress.sat_interaction_hl7_request s3
            WHERE s3.hub_interaction_id = sicr.hub_interaction_id
              AND s3.to_state = 'VALIDATION_SUCCESS'
        ) THEN 'Success'
        ELSE 'Failed'
    END AS hl7_validation_status  
FROM
    techbd_udi_ingress.sat_interaction_hl7_request sicr
left join   techbd_udi_ingress.sat_interaction_fhir_request htt_req
on  sicr.hub_interaction_id = htt_req.hub_interaction_id  and  htt_req.nature = 'Original FHIR Payload'    
WHERE 
    sicr.nature = 'Original HL7 Payload';

/*******************************************************************************************
This view takes data from sat_interaction_fhir_request table to 
provide a consolidated view of HTTP FHIR requests via HL7V2, including resource types, 
interaction details, request attributes, and validation issues.
******************************************************************************************/

DROP VIEW IF EXISTS techbd_udi_ingress.interaction_hl7v2_http_fhir_request CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_hl7v2_http_fhir_request
AS SELECT hub_interaction_id AS interaction_id,
    uri,
    bundle_id,
    created_at AS interaction_created_at,
    created_by AS interaction_created_by,
    provenance AS interaction_provenance,
    sat_interaction_fhir_request_id AS sat_interaction_http_request_id,
    nature,
    tenant_id,
    user_agent,
    client_ip_address,
    'application/json'::text AS content_type, 
    elaboration,
    from_state,
    to_state,
    state_transition_reason,
    created_at AS request_created_at,
    created_by AS request_created_by,
    provenance AS request_provenance,
    issues_count,
    resource_type_set AS resource_types,
    interaction_start_time::text AS start_time,
    interaction_end_time::text AS finish_time,
    EXTRACT(epoch FROM interaction_end_time::timestamp without time zone - interaction_start_time::timestamp without time zone) * 1000::numeric AS duration_millisecs,
    patient_id,
    patient_mrn,
    patient_mrn_source_system,
    organization_id,
    organization_name,
    concat_ws('-'::text, tenant_id, organization_id, patient_mrn) AS source_mrn,  
    bundle_type AS bundle_resource_type,
    source_hub_interaction_id,
	CASE
		WHEN is_bundle_valid = true THEN 'Valid'::text
		WHEN is_bundle_valid = false THEN 'Invalid'::text
		ELSE NULL::text
	END AS is_bundle_valid  
   FROM techbd_udi_ingress.sat_interaction_fhir_request htt_req
   WHERE source_type='HL7V2';	

/**
-- View: interaction_ccda_payload
-- This view retrieves the payloads of CCDA interactions that failed validation.
-- It selects the hub_interaction_id and corresponding payload from the sat_interaction_ccda_request table.
-- Used to analyze or reprocess CCDA payloads that did not pass the validation stage.
**/

DROP VIEW IF EXISTS techbd_udi_ingress.interaction_ccda_payload CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_ccda_payload AS
select 
sicr.hub_interaction_id,
sicr.payload
FROM
    techbd_udi_ingress.sat_interaction_ccda_request sicr
WHERE 
    sicr.to_state = 'VALIDATION_FAILED';


/* -------------------------------------------------------------
-- View: interaction_ccda_validation_errors
-- This view provides detailed records of CCDA validation errors for each interaction.
-- It includes tenant, file, and environment metadata along with who created the record and its provenance.
-- Useful for diagnosing CCDA validation failures and tracking error trends across tenants.
-- -------------------------------------------------------------
*/

DROP VIEW IF EXISTS techbd_udi_ingress.interaction_ccda_validation_errors CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_ccda_validation_errors AS
SELECT
    s.sat_ccda_validation_errors_id,
    s.hub_interaction_id,
    s.tenant_id,
    s.uri, 
    s.error,
    s.file_name, 
    s.created_at 
FROM
    techbd_udi_ingress.sat_interaction_ccda_validation_errors s
where
    s.error != '';

/* -------------------------------------------------------------
-- View: interaction_hl7v2_validation_errors
-- This view provides detailed records of HL7v2 validation errors for each interaction.
-- It includes tenant, file, and environment metadata along with who created the record and its provenance.
-- Useful for diagnosing HL7v2 validation failures and tracking error trends across tenants.
-- -------------------------------------------------------------
*/

DROP VIEW IF EXISTS techbd_udi_ingress.interaction_hl7v2_validation_errors CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.interaction_hl7v2_validation_errors AS
SELECT
    s.sat_hl7_validation_errors_id,
    s.hub_interaction_id,
    s.tenant_id,
    s.uri, 
    s.error,
    s.file_name, 
    s.created_at 
FROM
    techbd_udi_ingress.sat_interaction_hl7_validation_errors s
where
    s.error != '';

/* -------------------------------------------------------------
-- View: csv_processing_errors_summary
-- This view provides a summarized overview of CSV processing errors 
-- encountered during data ingestion. 
-- It aggregates key details such as interaction ID, tenant, group, 
-- file name, origin, and error type for each failed ZIP file.
-- Useful for monitoring ingestion failures and identifying 
-- recurring CSV processing issues across tenants.
-- -------------------------------------------------------------
*/

DROP VIEW IF EXISTS techbd_udi_ingress.csv_processing_errors_summary cascade;
CREATE OR REPLACE view techbd_udi_ingress.csv_processing_errors_summary
AS 
SELECT
    scfpe.zip_file_hub_interaction_id,
    MIN(scfpe.created_at) AS created_at,
    MIN(scfpe.tenant_id) AS tenant_id,
    MIN(scfpe.group_id) AS group_id,
    MIN(scfpe.uri) AS uri,
    MIN(scfpe.error_type) AS error_types,
    MIN(scfpe.zip_file_name) AS zip_file_name,
    MIN(scfpe.origin) AS origin,
    MIN(scfpe.user_agent) AS user_agent
FROM
    techbd_udi_ingress.sat_csv_fhir_processing_errors scfpe 
where 
	scfpe.category = 'processing_errors'    
GROUP BY
    scfpe.zip_file_hub_interaction_id ;


/* -------------------------------------------------------------
-- View: csv_processing_error
-- This view provides detailed records of CSV processing errors 
-- captured during data ingestion. 
-- It includes timestamps, tenant information, file and group identifiers, 
-- error types, descriptions, and file origins. 
-- Useful for diagnosing individual CSV ingestion failures, 
-- analyzing error patterns, and supporting data quality troubleshooting.
-- -------------------------------------------------------------
*/

DROP VIEW IF EXISTS techbd_udi_ingress.csv_processing_error cascade;
CREATE OR REPLACE view techbd_udi_ingress.csv_processing_error
AS 
SELECT
    scfpe.created_at,
    scfpe.tenant_id,
    scfpe.zip_file_hub_interaction_id,
    scfpe.group_id,
    scfpe.uri,
    scfpe.error_type,
    scfpe.error_subtype,
    scfpe.description,
    scfpe.error,
    scfpe.file_name,
    scfpe.zip_file_name,
    scfpe.origin,
    scfpe.user_agent
FROM
	techbd_udi_ingress.sat_csv_fhir_processing_errors scfpe 
where 
	scfpe.category = 'processing_errors'       
ORDER BY
    scfpe.created_at DESC;      


/* -------------------------------------------------------------
-- View: diagnostics_log_details
-- This view provides detailed diagnostic and exception log records 
-- captured during file interaction processing. 
-- It includes information such as the interaction ID, tenant ID, 
-- diagnostic messages, elaboration details, timestamps, 
-- and unique diagnostic log identifiers. 
-- Useful for analyzing processing exceptions, understanding 
-- system behavior, and assisting in troubleshooting and data quality improvement.
-- -------------------------------------------------------------
*/

DROP VIEW IF EXISTS techbd_udi_ingress.diagnostics_logs cascade;
CREATE OR REPLACE view techbd_udi_ingress.diagnostics_logs
AS 
select
 hub_interaction_id,
 tenant_id,
 diagnostic_log_message, 
 created_at,
 sat_diagnostic_log_id
FROM techbd_udi_ingress.sat_diagnostic_log
where tenant_id is not null;


DROP VIEW IF EXISTS techbd_udi_ingress.diagnostics_log_details cascade;
CREATE OR REPLACE view techbd_udi_ingress.diagnostics_log_details
AS 
select
 hub_interaction_id,
 tenant_id,
 diagnostic_log_message,
 elaboration,
 created_at,
 sat_diagnostic_log_id
FROM techbd_udi_ingress.sat_diagnostic_log
where tenant_id is not null;  

/*********************** CCDA Replay ******************************/
DROP FUNCTION IF EXISTS techbd_udi_ingress.get_xml_content_from_mirth_fdw(text);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.get_xml_content_from_mirth_fdw(p_bundle_id text)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    v_xml                          TEXT := NULL;
    v_channel_id                   INT;
    v_table                        TEXT;
    v_sql                          TEXT;
 
    v_base_fhir_url                TEXT := NULL;
    v_patient_cin                  TEXT := NULL;
    v_facility_id                  TEXT := NULL;
    v_org_npi                      TEXT := NULL;
    v_org_tax                      TEXT := NULL;
    v_encounter_type               TEXT := NULL;
    v_tenant_id                    TEXT := NULL;
    v_original_hub_interaction_id  TEXT := NULL;
    v_grouper_screening_code       TEXT := NULL;
	v_original_bundle              JSONB := NULL;
	v_alreadyResubmitted		   boolean := false;
BEGIN
    IF p_bundle_id IS NULL OR p_bundle_id = '' THEN
        RETURN jsonb_build_object('error', 'bundle_id is empty.');
    END IF;
 
    -- Prevent duplicate replay
    IF EXISTS (
        SELECT 1
        FROM techbd_udi_ingress.ccda_replay_details
        WHERE bundle_id = p_bundle_id
    ) THEN
		v_alreadyResubmitted := true;
    END IF;    
 
	SELECT mc.content
	  INTO v_xml
      FROM public.d_mc mc
      JOIN public.d_mc mc2
        ON mc.message_id = mc2.message_id
       AND mc2.metadata_id = 2
       AND mc2.content_type = 11
       AND mc2.content LIKE '%%' || p_bundle_id || '%%'
     WHERE mc.metadata_id = 2
       AND mc.content_type = 3
     LIMIT 1;
 
    WITH fhir_req AS (
        SELECT r.payload, r.tenant_id, r.hub_interaction_id
          FROM techbd_udi_ingress.sat_interaction_fhir_request r
         WHERE r.nature = 'Original FHIR Payload'
           AND r.bundle_id = p_bundle_id
           AND r.payload IS NOT NULL
           AND r.payload->>'id' = r.bundle_id
         LIMIT 1
    ),
 
    -- Patient identifiers
    patient AS (
        SELECT
            MAX(CASE WHEN coding->>'code' = 'MA' THEN ident->>'value' END) AS patient_cin,
            MAX(CASE WHEN coding->>'code' = 'MR'
                     THEN split_part(
                              ident->>'system',
                              '/',
                              array_length(string_to_array(ident->>'system', '/'), 1)
                          )
                END) AS facility_id
          FROM fhir_req r
          JOIN LATERAL jsonb_array_elements(r.payload->'entry') e ON TRUE
          JOIN LATERAL jsonb_array_elements(e->'resource'->'identifier') ident ON TRUE
          JOIN LATERAL jsonb_array_elements(ident->'type'->'coding') coding ON TRUE
         WHERE e->'resource'->>'resourceType' = 'Patient'
    ),
 
    -- Organization identifiers
    org AS (
        SELECT
            MAX(CASE WHEN coding->>'code' = 'NPI' THEN ident->>'value' END) AS org_npi,
            MAX(CASE WHEN coding->>'code' = 'TAX' THEN ident->>'value' END) AS org_tax
          FROM fhir_req r
          JOIN LATERAL jsonb_array_elements(r.payload->'entry') e ON TRUE
          JOIN LATERAL jsonb_array_elements(e->'resource'->'identifier') ident ON TRUE
          JOIN LATERAL jsonb_array_elements(ident->'type'->'coding') coding ON TRUE
         WHERE e->'resource'->>'resourceType' = 'Organization'
    ),
 
    -- Encounter type (aggregate to guarantee one row)
    encounter AS (
        SELECT MAX(coding->>'code') AS encounter_type
          FROM fhir_req r
          JOIN LATERAL jsonb_array_elements(r.payload->'entry') e ON TRUE
          JOIN LATERAL jsonb_array_elements(e->'resource'->'type') typ ON TRUE
          JOIN LATERAL jsonb_array_elements(typ->'coding') coding ON TRUE
         WHERE e->'resource'->>'resourceType' = 'Encounter'
    ),
 
    -- Grouper Observation code (hasMember present) — aggregate to guarantee one row
    grouper AS (
	    SELECT MAX(coding->>'code') AS grouper_screening_code
	      FROM fhir_req r
	      JOIN LATERAL jsonb_array_elements(r.payload->'entry') e ON TRUE
	      JOIN LATERAL jsonb_array_elements((e->'resource'->'code'->'coding')) coding ON TRUE
	     WHERE e->'resource'->>'resourceType' = 'Observation'
	       AND (e->'resource') ? 'hasMember'
	),
 
    -- Base FHIR URL + tenant + interaction (aggregate to guarantee one row)
    base AS (
        SELECT
            MIN(split_part(pf, '/StructureDefinition', 1)) AS base_fhir_url,
            r.tenant_id,
            r.hub_interaction_id,
			r.payload
          FROM fhir_req r
          LEFT JOIN LATERAL (
              SELECT jsonb_array_elements_text(r.payload->'meta'->'profile') AS pf
          ) p ON TRUE
         GROUP BY r.tenant_id, r.hub_interaction_id, r.payload
    )
 
    SELECT
        p.patient_cin,
        p.facility_id,
        o.org_npi,
        o.org_tax,
        e.encounter_type,
        b.base_fhir_url,
        b.tenant_id,
        b.hub_interaction_id,
        g.grouper_screening_code,
		b.payload
      INTO
        v_patient_cin,
        v_facility_id,
        v_org_npi,
        v_org_tax,
        v_encounter_type,
        v_base_fhir_url,
        v_tenant_id,
        v_original_hub_interaction_id,
        v_grouper_screening_code,
		v_original_bundle
      FROM patient         p
      CROSS JOIN org       o
      CROSS JOIN encounter e
      CROSS JOIN grouper   g
      CROSS JOIN base      b;
 
    RETURN jsonb_build_object(
        'alreadyResubmitted', v_alreadyResubmitted,
        'originalCCDAPayload', v_xml,
		'originalBundle', v_original_bundle,
        'original_hub_interaction_id', v_original_hub_interaction_id,
        'X-TechBD-Tenant-ID', v_tenant_id,
        'X-TechBD-Bundle-ID', p_bundle_id,
        'X-TechBD-Base-FHIR-URL', v_base_fhir_url,
        'X-TechBD-CIN', v_patient_cin,
        'X-TechBD-OrgNPI', v_org_npi,
        'X-TechBD-Facility-ID', v_facility_id,
        'X-TechBD-OrgTIN', v_org_tax,
        'X-TechBD-Encounter-Type', v_encounter_type,
        'X-TechBD-Screening-Code', v_grouper_screening_code
    );
END;
$$;

DROP FUNCTION IF EXISTS techbd_udi_ingress.ccda_replay_details_upserted(text, text, text, timestamptz, text, text, bool, jsonb, jsonb, text, jsonb, jsonb, text);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.ccda_replay_details_upserted(
	p_bundle_id text,
	p_hub_interaction_id text,
	p_retry_interaction_id text,
	p_created_at timestamp with time zone DEFAULT NULL::timestamp with time zone,
	p_created_by text DEFAULT NULL::text,
	p_provenance text DEFAULT NULL::text,
	p_is_valid boolean DEFAULT false,
	p_error_message jsonb DEFAULT NULL::jsonb,
	p_elaboration jsonb DEFAULT NULL::jsonb,
	p_retry_master_interaction_id text DEFAULT NULL::text,
	p_new_bundle_generated jsonb DEFAULT NULL::jsonb,
	p_corrected_bundle jsonb DEFAULT NULL::jsonb,
	p_original_ccda_file text DEFAULT NULL::text
	)
RETURNS text
LANGUAGE plpgsql
AS $function$
DECLARE
    v_error_msg 			TEXT;
    v_error_type 			TEXT;
    v_sqlstate 				TEXT;
    v_pg_detail 			TEXT;
    v_pg_hint 				TEXT;
    v_pg_context 			TEXT;
    v_created_at 			TIMESTAMPTZ := COALESCE(p_created_at, CURRENT_TIMESTAMP);
    v_created_by 			TEXT 		:= COALESCE(p_created_by, current_user);
    v_provenance 			TEXT 		:= COALESCE(p_provenance, 'replay ccda');
    v_exception_id 			TEXT 		:= gen_random_uuid()::text;
   	v_hub_interaction_key 	TEXT;
   	v_return				TEXT;
	v_elaboration 			JSONB;   	
BEGIN
	/*---register diagnostic log---*/
	v_elaboration := jsonb_build_object(
							'old interaction_id', p_hub_interaction_id,
							'bundle_id', p_bundle_id,
							'retry_interaction_id', p_retry_interaction_id,
							'retry_master_interaction_id', p_retry_master_interaction_id
							);
	CALL techbd_udi_ingress.register_diagnostic_log(
			v_exception_id, v_hub_interaction_key, v_created_by, v_provenance, 'info'::text,
			'Input parameters into ccda_replay_details_upserted function'::text, v_created_by, 'success'::text, NULL::text, 0, v_elaboration);
	/*------------------------------*/
 
  	IF p_bundle_id IS NULL OR p_hub_interaction_id IS NULL THEN
        RETURN NULL;
    END IF;
 
	INSERT INTO techbd_udi_ingress.ccda_replay_details
				(bundle_id, hub_interaction_id, retry_interaction_id, provenance, created_by, created_at,
				is_valid, error_message, elaboration, retry_master_interaction_id,
				new_bundle_generated, corrected_bundle, original_ccda_file)
	VALUES(p_bundle_id, p_hub_interaction_id, p_retry_interaction_id, v_provenance, v_created_by, v_created_at,
				p_is_valid, p_error_message, p_elaboration, p_retry_master_interaction_id,
				p_new_bundle_generated, p_corrected_bundle, p_original_ccda_file);
	v_return = p_bundle_id;
	
	RETURN v_return;
EXCEPTION
	WHEN OTHERS THEN
	    -- Capture exception details
	    GET STACKED DIAGNOSTICS
	        v_error_msg = MESSAGE_TEXT,
	        v_sqlstate = RETURNED_SQLSTATE,
	        v_pg_detail = PG_EXCEPTION_DETAIL,
	        v_pg_hint = PG_EXCEPTION_HINT,
	        v_pg_context = PG_EXCEPTION_CONTEXT;
	        v_error_type = 'SQL';
		    
	    -- Log the exception, reusing the previous exception ID if it exists
		v_exception_id := techbd_udi_ingress.register_issue(
        COALESCE(v_exception_id,NULL),
		v_hub_interaction_key,
		v_error_msg,
		v_error_type,
		v_sqlstate,
		v_pg_detail,
		v_pg_hint,
		v_pg_context,
		v_created_by,
		v_provenance);
	RETURN NULL;
END;
$function$
;
/*********************** End of CCDA Replay ******************************/



/* -------------------------------------------------------------
-- View: csv_all_processing_error_summary
-- This view provides a summarized list of CSV processing issues 
-- recorded during data ingestion. 
-- It consolidates records across categories such as 
-- file_not_processed, incomplete_groups, processing_errors, 
-- and data_integrity.
--
-- For each Zip File Interaction ID, the view captures the earliest 
-- creation time, tenant details, group information, submission URI, 
-- error type, ZIP file name, origin, and user agent.
--
-- Useful for understanding overall CSV processing outcomes, 
-- identifying problematic files, grouping errors by interaction,
-- and supporting high-level data quality monitoring.
-- -------------------------------------------------------------
*/

DROP VIEW IF EXISTS techbd_udi_ingress.csv_all_processing_error_summary CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.csv_all_processing_error_summary AS
SELECT
    scfpe.category,
    CASE scfpe.category
        WHEN 'file_not_processed' THEN 'File Not Processed'
        WHEN 'incomplete_groups' THEN 'Incomplete Groups'
        WHEN 'processing_errors' THEN 'Processing Errors'
        WHEN 'data_integrity' THEN 'Data Integrity'
        ELSE scfpe.category
    END AS category_label,    
    scfpe.zip_file_hub_interaction_id,
    MIN(scfpe.created_at) AS created_at,
    MIN(scfpe.tenant_id) AS tenant_id,
    MIN(scfpe.group_id) AS group_id,
    MIN(scfpe.uri) AS uri,
    MIN(scfpe.error_type) AS error_types,
    MIN(scfpe.zip_file_name) AS zip_file_name,
    MIN(scfpe.origin) AS origin,
    MIN(scfpe.user_agent) AS user_agent
FROM
    techbd_udi_ingress.sat_csv_fhir_processing_errors scfpe
WHERE
    scfpe.category IN (
        'file_not_processed',
        'incomplete_groups',
        'processing_errors',
        'data_integrity'
    )
GROUP BY
    scfpe.category,
    scfpe.zip_file_hub_interaction_id;



DROP VIEW IF EXISTS techbd_udi_ingress.csv_error_summary CASCADE;
CREATE OR REPLACE VIEW techbd_udi_ingress.csv_error_summary AS
SELECT 
    scfpe.zip_file_hub_interaction_id,
    MIN(scfpe.created_at) AS created_at,
    MIN(scfpe.tenant_id) AS tenant_id,
    MIN(scfpe.group_id) AS group_id,
    MIN(scfpe.uri) AS uri,
    MIN(scfpe.error_type) AS error_types,
    MIN(scfpe.zip_file_name) AS zip_file_name,
    MIN(scfpe.origin) AS origin,
    MIN(scfpe.user_agent) AS user_agent
FROM
    techbd_udi_ingress.sat_csv_fhir_processing_errors scfpe
WHERE
    scfpe.category IN (
        'file_not_processed',
        'incomplete_groups',
        'processing_errors',
        'data_integrity'
    )
GROUP BY 
    scfpe.zip_file_hub_interaction_id;    


/****************
-- Retrieves API/FHIR interaction performance metrics within a date range, returning the longest duration per interaction_id with additional API-specific metadata
******************/
DROP FUNCTION IF EXISTS techbd_udi_ingress.get_api_interaction_observe(DATE, DATE);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.get_api_interaction_observe(
    start_date DATE,
    end_date DATE
)
RETURNS TABLE (
    interaction_id TEXT,
    uri TEXT,
    start_time TIMESTAMP WITH TIME ZONE,
    finish_time TIMESTAMP WITH TIME ZONE,
    duration_millisecs NUMERIC,
    interaction_http_request_id TEXT
) 
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    WITH date_range AS (
        SELECT 
            start_date::TIMESTAMP AS range_start,
            end_date::TIMESTAMP + INTERVAL '1 day' AS range_end
    ),
    fhir_interactions AS (
        SELECT 
            req.hub_interaction_id AS interaction_id,
            req.uri,
            req.interaction_start_time AS start_time,
            req.interaction_end_time AS finish_time,
            EXTRACT(EPOCH FROM (req.interaction_end_time - req.interaction_start_time)) * 1000 AS duration_millisecs,
            req.sat_interaction_fhir_request_id AS interaction_http_request_id
        FROM 
            techbd_udi_ingress.sat_interaction_fhir_request req
            CROSS JOIN date_range dr
        WHERE 
            req.nature = 'Original FHIR Payload'
            AND req.interaction_start_time IS NOT NULL
            AND req.interaction_end_time IS NOT NULL
            AND req.interaction_start_time >= dr.range_start
            AND req.interaction_start_time < dr.range_end 
    )
    SELECT *
    FROM fhir_interactions ;
END;
$$;



/****************
-- Retrieves user interaction performance metrics within a date range (excluding Bundle URIs), returning the longest duration per interaction_id with user-specific metadata.
******************/

DROP FUNCTION IF EXISTS techbd_udi_ingress.get_user_interaction_observe(DATE, DATE);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.get_user_interaction_observe(
    start_date DATE,
    end_date DATE
)
RETURNS TABLE (
    interaction_id TEXT,
    uri TEXT,
    start_time TIMESTAMP WITH TIME ZONE,
    finish_time TIMESTAMP WITH TIME ZONE,
    duration_millisecs NUMERIC,
    interaction_http_request_id TEXT
) 
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    WITH date_range AS (
        SELECT 
            start_date::TIMESTAMP AS range_start,
            end_date::TIMESTAMP + INTERVAL '1 day' AS range_end
    ),
    user_interactions AS (
        SELECT 
            req.hub_interaction_id AS interaction_id,
            req.uri,
            req.interaction_start_time AS start_time,
            req.interaction_end_time AS finish_time,
            EXTRACT(EPOCH FROM (req.interaction_end_time - req.interaction_start_time)) * 1000 AS duration_millisecs,
            req.sat_interaction_user_id AS interaction_http_request_id
        FROM 
            techbd_udi_ingress.sat_interaction_user req
            CROSS JOIN date_range dr
        WHERE 
            req.uri NOT ILIKE '%/Bundle%'
            AND req.interaction_start_time IS NOT NULL
            AND req.interaction_end_time IS NOT NULL
            AND req.interaction_start_time >= dr.range_start
            AND req.interaction_start_time < dr.range_end
    )
    SELECT 
        ui.interaction_id,
        ui.uri,
        ui.start_time,
        ui.finish_time,
        ui.duration_millisecs,
        ui.interaction_http_request_id
    FROM user_interactions ui;
END;
$$;

/*****************************************************************************************************************
-- Get the bundle type from the given FHIR bundle as REFERRAL, ASSESSMENT, SCREENING, CONSENT_ONLY or OUTREACH_ONLY
******************************************************************************************************************/
DROP FUNCTION IF EXISTS techbd_udi_ingress.get_fhir_bundle_type(jsonb);
CREATE OR REPLACE FUNCTION techbd_udi_ingress.get_fhir_bundle_type(p_payload jsonb)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
  DECLARE
    v_category text := null;
  BEGIN
	WITH bundle_type_flags AS (
		----------------------------------------------------------------------
	    -- REFERRAL
	    ----------------------------------------------------------------------
	    SELECT
	        CASE
	            WHEN jsonb_path_exists(
	                    p_payload,
	                    '$.entry[*].resource?(
	                        (
	                            @.resourceType == "ServiceRequest"
	                            && @.meta.profile[*] like_regex "SHINNYSDOHServiceRequest$"
	                        )
	                        ||
	                        (
	                            @.resourceType == "Task"
	                            && @.meta.profile[*] like_regex "SHINNYSDOHTaskForReferralManagement$"
	                        )
	                    )'
	                )
	            THEN 'Referral'
	            ELSE NULL
	        END AS category

		UNION ALL
	
	    ----------------------------------------------------------------------
	    -- ASSESSMENT
	    ----------------------------------------------------------------------
		SELECT
		    CASE
		        WHEN jsonb_path_exists(
		            p_payload,
		            '$.entry[*].resource ? (
		                (
		                    @.resourceType == "Condition"
		                    && @.meta.profile[*] like_regex "SHINNY-SDOHCC-Condition$"
		                )
		                ||
		                (
		                    @.resourceType == "QuestionnaireResponse"
		                    && (
		                        @.questionnaire like_regex "SHINNYApprovalQuestionnaire$"
		
		                        || (
		                            @.questionnaire like_regex "SHINNYAdministrativeQuestionnaire$"
		                            && exists(@.item[*].item[*].answer[*].valueCoding.display)
		                        )
		
		                        || (
		                            @.questionnaire like_regex "SHINNYServiceDuplicationQuestionnaire$"
		                            && exists(@.item[*].item[*].item[*].answer[*].valueCoding.display)
		                        )
		                    )
		                )
		            )'
		        )
		        THEN 'Assessment'
		        ELSE NULL
		    END AS category

		UNION ALL

	    ----------------------------------------------------------------------
	    -- SCREENING
	    ----------------------------------------------------------------------

	    SELECT
	        CASE
	            WHEN jsonb_path_exists(
	                    p_payload,
	                    '$.entry[*].resource?(
	                        @.resourceType == "Observation"
	                        && @.hasMember.size() > 0
	                        && @.meta.profile[*] like_regex "shinny-observation-screening-response$"
	                    )'
	                )
	            THEN 'Screening'
	            ELSE NULL
	        END AS category
	
	    UNION ALL

	    ----------------------------------------------------------------------
	    -- CONSENT_ONLY
	    ----------------------------------------------------------------------

	    SELECT
		    CASE
		        WHEN
		            -- 1. Consent with shinny-Consent profile exists
		            jsonb_path_exists(
		                p_payload,
		                '$.entry[*].resource ? (
		                    @.resourceType == "Consent"
		                    && @.meta.profile[*] like_regex "shinny-Consent$"
		                )'
		            )
		
		            -- 2. NO Observation, Condition, ServiceRequest, or Task exists
		            AND NOT jsonb_path_exists(
		                p_payload,
		                '$.entry[*].resource ? (
		                    (@.resourceType == "Observation" && @.meta.profile[*] like_regex "shinny-observation-screening-response$")
		                    || @.resourceType == "Condition"
		                    || @.resourceType == "ServiceRequest"
		                    || @.resourceType == "Task"
		                )'
		            )
		
		            -- 3. NO QuestionnaireResponse with restricted profiles
		            AND NOT jsonb_path_exists(
		                p_payload,
		                '$.entry[*].resource ? (
					        @.resourceType == "QuestionnaireResponse"
					        && (
					            @.questionnaire like_regex "SHINNYApprovalQuestionnaire$"
					            || @.questionnaire like_regex "SHINNYAdministrativeQuestionnaire$"
					            || @.questionnaire like_regex "SHINNYServiceDuplicationQuestionnaire$"
					        )
					    )'
		            )
		
		        THEN 'Consent_Only'
		        ELSE NULL
		    END AS category

		UNION ALL

	    ----------------------------------------------------------------------
	    -- OUTREACH_ONLY
	    ----------------------------------------------------------------------

	    SELECT
		    CASE
		        WHEN
		            -- 1. NO Observation, Condition, ServiceRequest, consent or Task exists
		            NOT jsonb_path_exists(
		                p_payload,
		                '$.entry[*].resource ? (
		                    (@.resourceType == "Observation" && @.meta.profile[*] like_regex "shinny-observation-screening-response$")
		                    || @.resourceType == "Condition"
		                    || @.resourceType == "ServiceRequest"
		                    || @.resourceType == "Task"
							|| @.resourceType == "Consent"
		                )'
		            )
		
		            -- 2. NO QuestionnaireResponse with restricted profiles
		            AND NOT jsonb_path_exists(
		                p_payload,
		                '$.entry[*].resource ? (
					        @.resourceType == "QuestionnaireResponse"
					        && (
					            @.questionnaire like_regex "SHINNYApprovalQuestionnaire$"
					            || @.questionnaire like_regex "SHINNYAdministrativeQuestionnaire$"
					            || @.questionnaire like_regex "SHINNYServiceDuplicationQuestionnaire$"
					        )
					    )'
		            )
		
		        THEN 'Outreach_Only'
		        ELSE NULL
		    END AS category
	    
	)
	
	SELECT STRING_AGG(category, ', ') into v_category
	FROM bundle_type_flags
	WHERE category IS NOT NULL;

	RETURN v_category;
END;
$function$
;


/****************
-- Retrieves FHIR session diagnostics within a date range, grouped by hub_interaction_id, returning the latest interaction metadata for sessions with validation issues
******************/


DROP FUNCTION IF EXISTS techbd_udi_ingress.get_fhir_session_diagnostics(DATE, DATE);

CREATE OR REPLACE FUNCTION techbd_udi_ingress.get_fhir_session_diagnostics(
    p_start_date DATE,
    p_end_date   DATE
)
RETURNS TABLE (
    hub_interaction_id     TEXT,
    created_at             TIMESTAMPTZ,
    tenant_id              TEXT,
    bundle_id              TEXT,
    uri                    TEXT,
    ig_version             TEXT,
    techbd_version_number  TEXT,
    validation_engine      TEXT
) 
LANGUAGE sql
STABLE
AS $function$
SELECT DISTINCT ON (hub_interaction_id)
    hub_interaction_id,
    created_at,
    tenant_id,
    bundle_id,
    uri,
    ig_version,
    techbd_version_number,
    validation_engine
FROM techbd_udi_ingress.sat_interaction_fhir_session_diagnostic
WHERE
    severity IN ('warning','error','fatal')
    AND message IS DISTINCT FROM ''
    AND created_at >= p_start_date::timestamp
    AND created_at <  (p_end_date::timestamp + interval '1 day')
ORDER BY
    hub_interaction_id,
    created_at DESC;
$function$;

/****************
-- Retrieves detailed FHIR validation issues within a date range, optionally filtered by hub_interaction_id, including severity, message, line, column, and diagnostics
******************/


DROP FUNCTION IF EXISTS techbd_udi_ingress.get_fhir_session_diagnostics_details(
    TEXT, TEXT, TEXT, TEXT, TEXT, DATE
);

DROP FUNCTION IF EXISTS techbd_udi_ingress.get_fhir_session_diagnostics_details(
    DATE, DATE, TEXT
);

CREATE OR REPLACE FUNCTION techbd_udi_ingress.get_fhir_session_diagnostics_details(
    p_start_date DATE,
    p_end_date   DATE,
    p_hub_interaction_id TEXT DEFAULT NULL
)
RETURNS TABLE (
    hub_interaction_id TEXT,
    session_id TEXT,
    severity TEXT,
    message TEXT,
    line TEXT,
    col_no TEXT,             -- renamed (was "column")
    diagnostics TEXT
)
LANGUAGE sql
 STABLE
AS $function$
SELECT 
        sifsd.hub_interaction_id,    
        sifsd.session_id,
        sifsd.severity AS severity,
        sifsd.message,
        sifsd.line,
        sifsd."column" AS col_no,  -- map the quoted table column into the renamed output
        sifsd.diagnostics              
    FROM techbd_udi_ingress.sat_interaction_fhir_session_diagnostic sifsd
    WHERE
        (p_hub_interaction_id IS NULL OR sifsd.hub_interaction_id = p_hub_interaction_id)  
        AND sifsd.severity IN ('warning', 'error', 'fatal') 
        AND sifsd.message IS DISTINCT FROM '' 
        AND sifsd.created_at >= p_start_date::timestamp
        AND sifsd.created_at <  (p_end_date::timestamp + interval '1 day')
    ORDER BY sifsd.hub_interaction_id, sifsd.created_at DESC;
$function$;