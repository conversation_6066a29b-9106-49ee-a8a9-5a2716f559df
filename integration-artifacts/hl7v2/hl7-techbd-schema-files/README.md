# HL7v2 TechBD Schema Files

This directory contains HL7v2 schema and transformation files used for validating and processing HL7v2 messages within the Tech by Design integration platform.

## 📄 Overview

HL7v2 schemas and XSLT files are essential for:
- Validating HL7v2 messages
- Transforming HL7v2 messages to FHIR bundles or other formats
- Supporting interoperability and automated integration workflows

## 📂 File List

| File Name                | Description (if known)                |
|--------------------------|---------------------------------------|
| hl7v2-fhir-bundle.xslt   | XSLT for transforming HL7v2 to FHIR   |
| hl7v2-validation-schema  | XSLT for validating the HL7 file      |
| TechBD HL7 Workflow.xml  | TechBD HL7 workflow definition        |

## 🔗 Related Documentation

- [HL7v2 Channel Files](../hl7-techbd-channel-files/mirth-connect/README.md)
- [HL7v2 Nexus Channels](../hl7-techbd-channel-files/nexus/README.md)
- [Integration Artifacts Index](../../README.md)

---