{"group": {"id": 3, "name": "Config", "description": "Environment variables needed for CCDA Workflow", "version": "1", "cacheSize": 1000, "cachePolicy": "LRU", "createdDate": "2026-01-14T10:28:06.079+00:00", "updatedDate": "2026-01-23T11:38:54.051+00:00"}, "values": {"BASE_FHIR_URL": "http://test.shinny.org/us/ny/hrsn", "BL_FHIR_BUNDLE_VALIDATION_API_URL": "https://nexus.fhir.sandbox.techbd.org", "DATA_LEDGER_API_URL": "https://gbp2obo8d0.execute-api.us-east-1.amazonaws.com/development/DataLedger", "DATA_LEDGER_TRACKING": "false", "HL7_XSLT_PATH": "/opt/bridgelink/hl7-techbd-schema-files", "HUB_UI_URL": "https://hub.sandbox.dev.techbd.org", "MC_CCDA_SCHEMA_FOLDER": "/opt/bridgelink/ccda-techbd-schema-files/", "MC_FHIR_BUNDLE_SUBMISSION_API_URL": "https://bl.sbx.nexus.techbd.org:6000/Bundle", "MC_VALID_FHIR_URLS": "http://shinny.org/us/ny/hrsn,http://test.shinny.org/us/ny/hrsn", "PROFILE_URL_BUNDLE": "/StructureDefinition/SHINNYBundleProfile", "PROFILE_URL_CONSENT": "/StructureDefinition/shinny-<PERSON>sent", "PROFILE_URL_ENCOUNTER": "/StructureDefinition/shinny-encounter", "PROFILE_URL_LOCATION": "http://hl7.org/fhir/us/sdoh-clinicalcare/StructureDefinition/SDOHCC-Location", "PROFILE_URL_OBSERVATION": "/StructureDefinition/shinny-observation-screening-response", "PROFILE_URL_ORGANIZATION": "/StructureDefinition/shin-ny-organization", "PROFILE_URL_PATIENT": "/StructureDefinition/shinny-patient", "PROFILE_URL_PRACTITIONER": "/StructureDefinition/shin-ny-practitioner", "PROFILE_URL_PROCEDURE": "/StructureDefinition/shinny-sdoh-procedure", "PROFILE_URL_QUESTIONNAIRE": "/StructureDefinition/shinny-questionnaire", "PROFILE_URL_QUESTIONNAIRE_RESPONSE": "/StructureDefinition/shinny-questionnaire-response", "PROFILE_URL_SEXUAL_ORIENTATION": "/StructureDefinition/shinny-observation-sexual-orientation", "SM_KEY_RDS_SECRETS": "techbd-rds-secrets", "TECHBD_CSV_SERVICE_API_URL": "https://nexus.csv.sandbox.techbd.org", "TECHBD_DEFAULT_DATALAKE_API_URL": "https://uzrlhp39e0.execute-api.us-east-1.amazonaws.com/dev/HRSNBundle"}, "exportDate": "2026-02-04T10:36:31.003+00:00"}