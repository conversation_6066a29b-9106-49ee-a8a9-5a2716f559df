<channel version="4.6.0">
  <id>3f3f6dcd-2923-4f09-89c3-1e248c1556b3</id>
  <nextMetaDataId>5</nextMetaDataId>
  <name>FhirBundlleValidation</name>
  <description>Version: 0.8.4
Added Request body validation and set Response Status Code for FhirBundleValidation Channel.</description>
  <revision>4</revision>
  <sourceConnector version="4.6.0">
    <metaDataId>0</metaDataId>
    <name>sourceConnector</name>
    <properties class="com.mirth.connect.connectors.http.HttpReceiverProperties" version="4.6.0">
      <pluginProperties>
        <com.mirth.connect.plugins.ssl.SSLSettingsProperties version="4.6.0">
  <sslEnabled>false</sslEnabled>
          <mutualTlsEnabled>false</mutualTlsEnabled>
          <verifyHostname>false</verifyHostname>
          <keystorePath/>
          <keystorePassword/>
          <certAlias/>
          <certPassword/>
          <truststorePath/>
          <truststorePassword/>
          <tls13>true</tls13>
          <tls12>true</tls12>
          <tls11>true</tls11>
          <keystoreType/>
          <truststoreType/>
          <keystoreSettingFromSystem>false</keystoreSettingFromSystem>
          <keystoreUid/>
          <myCertificateAlias/>
          <truststoreSettingFromSystem>false</truststoreSettingFromSystem>
          <truststoreUid/>
        </com.mirth.connect.plugins.ssl.SSLSettingsProperties>
        <com.mirth.connect.plugins.httpauth.NoneHttpAuthProperties version="4.6.0">
  <authType>NONE</authType>
        </com.mirth.connect.plugins.httpauth.NoneHttpAuthProperties>
      </pluginProperties>
      <listenerConnectorProperties version="4.6.0">
        <host>0.0.0.0</host>
        <port>9001</port>
      </listenerConnectorProperties>
      <sourceConnectorProperties version="4.6.0">
        <responseVariable>finalResponse</responseVariable>
        <respondAfterProcessing>true</respondAfterProcessing>
        <processBatch>false</processBatch>
        <firstResponse>false</firstResponse>
        <processingThreads>1</processingThreads>
        <resourceIds class="linked-hash-map">
          <entry>
            <string>Default Resource</string>
            <string>[Default Resource]</string>
          </entry>
        </resourceIds>
        <queueBufferSize>1000</queueBufferSize>
      </sourceConnectorProperties>
      <xmlBody>false</xmlBody>
      <parseMultipart>false</parseMultipart>
      <includeMetadata>false</includeMetadata>
      <binaryMimeTypes></binaryMimeTypes>
      <binaryMimeTypesRegex>false</binaryMimeTypesRegex>
      <responseContentType>application/fhir+json</responseContentType>
      <responseDataTypeBinary>false</responseDataTypeBinary>
      <responseStatusCode>${status}</responseStatusCode>
      <responseHeaders class="linked-hash-map">
        <entry>
          <string>Access-Control-Allow-Origin</string>
          <list>
            <string>${HUB_UI_URL}</string>
          </list>
        </entry>
        <entry>
          <string>Access-Control-Allow-Methods</string>
          <list>
            <string>GET, POST, OPTIONS</string>
          </list>
        </entry>
        <entry>
          <string>Access-Control-Allow-Headers</string>
          <list>
            <string>Content-Type, Authorization, X-TechBD-Base-FHIR-URL, X-TechBD-Tenant-ID, User-Agent, X-TechBD-REMOTE-IP, X-TechBD-Override-Request-URI, X-Correlation-ID, accept, X-TechBD-DataLake-API-URL, DataLake-API-Content-Type, X-TechBD-HealthCheck, X-TechBD-Validation-Severity-Level, X-SHIN-NY-IG-Version</string>
          </list>
        </entry>
        <entry>
          <string>Access-Control-Expose-Headers</string>
          <list>
            <string>Location, X-TechBD-Tenant-ID, User-Agent, X-TechBD-REMOTE-IP, X-TechBD-Override-Request-URI,X-Correlation-ID,X-TechBD-HealthCheck</string>
          </list>
        </entry>
      </responseHeaders>
      <responseHeadersVariable></responseHeadersVariable>
      <useResponseHeadersVariable>false</useResponseHeadersVariable>
      <charset>DEFAULT_ENCODING</charset>
      <contextPath>/</contextPath>
      <timeout>10000</timeout>
      <staticResources/>
    </properties>
    <transformer version="4.6.0">
      <elements>
        <com.mirth.connect.plugins.javascriptstep.JavaScriptStep version="4.6.0">
          <name>lookup_manager</name>
          <sequenceNumber>0</sequenceNumber>
          <enabled>true</enabled>
          <script>logger.info(&quot;HTTP request validation started.&quot;);
///////////////////////////////////////////////////////////////////////////////////////////
/*******************************
    AWS Secrets Manager Loader
********************************/
// Lookup cache TTL: 20 days
var ttlHours = 24 * 20;
//
//function fetchSecret(secretName) {
//    var region = &quot;us-east-1&quot;;
//
//    var Region = Packages.software.amazon.awssdk.regions.Region;
//    var SecretsManagerClient = Packages.software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;
//    var GetSecretValueRequest = Packages.software.amazon.awssdk.services.secretsmanager.model.GetSecretValueRequest;
//
//    try {
//        var client = SecretsManagerClient.builder()
//            .region(Region.of(region))
//            .build();
//
//        var request = GetSecretValueRequest.builder()
//            .secretId(secretName)
//            .build();
//
//        var response = client.getSecretValue(request);
//        client.close();
//
//        var value = response.secretString();
//        logger.info(&quot;★ Successfully fetched secret: &quot; + secretName);
//
//        return value;
//
//    } catch (e) {
//        logger.error(&quot;❌ Failed fetching secret: &quot; + secretName + &quot; | Error: &quot; + e.message);
//        throw e;
//    }
//}

/********************************************
    1️⃣ FETCH JDBC SECRET (JSON secret)
********************************************/

try {
  
//var jdbcSecretName = LookupHelper.get(&quot;Config&quot;, &quot;SM_KEY_RDS_SECRETS&quot;, ttlHours);
//    var jdbcSecretString = fetchSecret(jdbcSecretName);
//    var jdbcSecret = JSON.parse(jdbcSecretString);

	var username = LookupHelper.get(&quot;Config-sensitive&quot;, &quot;MC_JDBC_USERNAME&quot;, ttlHours);
	var password = LookupHelper.get(&quot;Config-sensitive&quot;, &quot;MC_JDBC_PASSWORD&quot;, ttlHours);

	channelMap.put(&quot;JDBC_USERNAME&quot;, username);
	channelMap.put(&quot;JDBC_PASSWORD&quot;, password);
    
	 logger.info(&quot;JDBC_USERNAME loaded: &quot; + username);

     globalMap.put(&quot;JDBC_USERNAME&quot;, username);
     globalMap.put(&quot;JDBC_PASSWORD&quot;, password);

    logger.info(&quot; JDBC credentials loaded into channelMap&quot;);

} catch (e) {
    logger.error(&quot; Error processing JDBC secret: &quot; + e.message);
}

/********************************************
    2️⃣ FETCH DATA LEDGER API KEY (Plain text)
********************************************/

try {
    var dataLedgerApiKey = LookupHelper.get(&quot;Config-sensitive&quot;, &quot;TECHBD_NYEC_DATALEDGER_API_KEY&quot;, ttlHours);
   
    channelMap.put(&quot;TECHBD_NYEC_DATALEDGER_API_KEY&quot;, dataLedgerApiKey);
    logger.info(&quot; DataLedger API Key loaded into channelMap&quot;);

} catch (e) {
    logger.error(&quot; Error processing DataLedger API Key secret: &quot; + e.message);
}
/////////////////////////////////////////////////////////////////////////
/********************************************
    3️⃣ FETCH FHIR BUNDLE SUBMISSION API URL (Plain text)
********************************************/
try {
    var fhirChannelUrl = LookupHelper.get(&quot;Config&quot;, &quot;MC_FHIR_BUNDLE_SUBMISSION_API_URL&quot;, ttlHours);

    if (!fhirChannelUrl || fhirChannelUrl.trim() === &quot;&quot;) {
        throw &quot;FHIR Channel URL retrieved from Lookup Manager is empty.&quot;;
    }
    channelMap.put(&quot;FHIR_CHANNEL_URL&quot;, fhirChannelUrl);
    globalMap.put(&quot;fhirChannelUrl&quot;, fhirChannelUrl);
    logger.info(&quot; FHIR API URL loaded successfully from Lookup Manager: &quot; + fhirChannelUrl);

} catch (e) {
    logger.error(&quot; Error fetching FHIR CHANNEL URL: &quot; + e);
    setErrorResponse(500, &quot;Failed to load FHIR CHANNEL URL&quot;);
    throw e;
}

/********************************************
    6️⃣ FETCH JDBC URL (Plain text)
********************************************/
try {
    var jdbcUrl = LookupHelper.get(&quot;Config-sensitive&quot;, &quot;MC_JDBC_URL&quot;, ttlHours);

    if (!jdbcUrl || jdbcUrl.trim() === &quot;&quot;) {
        throw &quot; MC_JDBC_URL retrieved from Lookup Manager is empty.&quot;;
    }

    jdbcUrl = jdbcUrl.trim(); // IMPORTANT: remove trailing spaces
    channelMap.put(&quot;jdbcUrl&quot;, jdbcUrl);
    logger.info(&quot;JDBC URL loaded successfully from Lookup Manager: &quot; + jdbcUrl);

} catch (e) {
    logger.error(&quot; Error fetching JDBC URL: &quot; + e);
    setErrorResponse(500, &quot;Failed to load JDBC URL&quot;);
    throw e;
}


/********************************************
    4️⃣ FETCH CSV SERVICE API URL (Plain text)
********************************************/
try {
    var csvServiceUrl = LookupHelper.get(&quot;Config&quot;, &quot;TECHBD_CSV_SERVICE_API_URL&quot;, ttlHours);

    if (!csvServiceUrl || csvServiceUrl.trim() === &quot;&quot;) {
        throw &quot;CSV Service URL retrieved from Lookup Manager is empty.&quot;;
    }

    channelMap.put(&quot;CSV_SERVICE_API_URL&quot;, csvServiceUrl);
    globalMap.put(&quot;csvServiceApiUrl&quot;, csvServiceUrl);
    logger.info(&quot;CSV Service API URL loaded successfully from Lookup Manager: &quot; + csvServiceUrl);

} catch (e) {
    logger.error(&quot;❌ Error fetching CSV Service API URL: &quot; + e);
    setErrorResponse(500, &quot;Failed to load CSV Service API URL&quot;);
    throw e;
}


/********************************************
    5️⃣ FETCH DEFAULT DATALAKE API URL (Plain text)
********************************************/
try {
    var datalakeApiUrl = &quot;&quot;;
    var incomingHeader = $(&apos;headers&apos;).getHeader(&apos;X-TechBD-DataLake-API-URL&apos;);

    if (incomingHeader != null &amp;&amp; incomingHeader.trim() !== &quot;&quot;) {
        datalakeApiUrl = incomingHeader.trim();
        logger.info(&quot; Datalake API URL loaded from request header: &quot; + datalakeApiUrl);
    } else {
        var datalakeApiUrl = LookupHelper.get(
            &quot;Config&quot;,
            &quot;TECHBD_DEFAULT_DATALAKE_API_URL&quot;,
            ttlHours
        );

        if (!datalakeApiUrl || datalakeApiUrl.trim() === &quot;&quot;) {
            throw &quot;DataLake API URL retrieved from Lookup Manager is empty.&quot;;
        }

        datalakeApiUrl = datalakeApiUrl.trim();
        logger.info(&quot;Datalake API URL loaded successfully from Lookup Manager: &quot; + datalakeApiUrl);
    }

    channelMap.put(&quot;DATALAKE_API_URL&quot;, datalakeApiUrl);
    globalMap.put(&quot;datalakeApiUrl&quot;, datalakeApiUrl);

} catch (e) {
    logger.error(&quot; Error fetching Datalake API URL: &quot; + e);
    setErrorResponse(500, &quot;Failed to load Datalake API URL&quot;);
    throw e;
}
&#xd;
&#xd;
/********************************************&#xd;
    6️⃣ FETCH DATA LEDGER API KEY (Plain text)&#xd;
********************************************/&#xd;
&#xd;
try {&#xd;
    var dataLakeApiKey = LookupHelper.get(&quot;Config-sensitive&quot;, &quot;TECHBD_NYEC_DATALAKE_API_KEY&quot;, ttlHours);&#xd;
 &#xd;
    channelMap.put(&quot;DATA_LAKE_API_KEY&quot;, dataLakeApiKey);&#xd;
    logger.info(&quot; DataLake API Key loaded into channelMap&quot;);&#xd;
&#xd;
} catch (e) {&#xd;
    logger.error(&quot; Error processing DataLake API Key secret: &quot; + e.message);&#xd;
}&#xd;


/********************************************
    7️⃣ FETCH FHIR BUNDLE SUBMISSION API URL (Plain text)
********************************************/
try {
    var fhirApiUrl = LookupHelper.get(&quot;Config&quot;, &quot;BL_FHIR_BUNDLE_VALIDATION_API_URL&quot;, ttlHours);

    if (!fhirApiUrl || fhirApiUrl.trim() === &quot;&quot;) {
        throw &quot;BL FHIR Bundle Validation API URL retrieved from Lookup Manager is empty.&quot;;
    }
	fhirApiUrl = fhirApiUrl + &quot;/Bundle/$validate&quot;;
    channelMap.put(&quot;FHIR_API_URL&quot;, fhirApiUrl);
    globalMap.put(&quot;fhirBundleSubmissionApiUrl&quot;, fhirApiUrl);
    logger.info(&quot;BL FHIR Bundle Validation API URL loaded successfully from Lookup Manager:&quot; + fhirApiUrl);

} catch (e) {
    logger.error(&quot; Error fetching FHIR API URL: &quot; + e);
    setErrorResponse(500, &quot;Failed to load FHIR API URL&quot;);
    throw e;
}


/********************************************
    8️⃣ FETCH HUB UI URL (Plain text)
********************************************/
try {
    var hubUiUrl = LookupHelper.get(&quot;Config&quot;, &quot;HUB_UI_URL&quot;, ttlHours);

    if (!hubUiUrl || hubUiUrl.trim() === &quot;&quot;) {
        throw &quot;HUB UI URL retrieved from Lookup Manager is empty.&quot;;
    }
    hubUiUrl = hubUiUrl.trim();
    channelMap.put(&quot;HUB_UI_URL&quot;, hubUiUrl);
    globalMap.put(&quot;HUB_UI_URL&quot;, hubUiUrl);
    logger.info(&quot;HUB UI URL loaded successfully from Lookup Manager: &quot; + hubUiUrl);
} catch (e) {
    logger.error(&quot; Error fetching HUB UI URL: &quot; + e);
    setErrorResponse(500, &quot;Failed to load HUB UI URL&quot;);
    throw e;
}


/********************************************
    DONE
********************************************/


/**
* Util function to generate json string wit hstatus and message
*/

function createJsonResponse(status, message) {
    return JSON.stringify({ status: status, message: message });
}

/**
* Util function to set error response.
*/
function setErrorResponse(statusCode, errorMessage) {
    responseMap.put(&apos;status&apos;, String(statusCode)); // Set HTTP status
    responseMap.put(&apos;message&apos;, errorMessage);
    responseMap.put(&apos;finalResponse&apos;, createJsonResponse(statusCode, errorMessage));
}</script>
        </com.mirth.connect.plugins.javascriptstep.JavaScriptStep>
        <com.mirth.connect.plugins.javascriptstep.JavaScriptStep version="4.6.0">
          <name>FHIR Validation</name>
          <sequenceNumber>1</sequenceNumber>
          <enabled>true</enabled>
          <script>// Use logger.info with a single string (no channelMap arg)

var requestedPath = channelMap.get(&apos;requestedPath&apos;);
logger.info(&quot;[CHANNEL: 1115 TECHBD &quot; + channelName + &quot; ][INTERACTION ID: &quot; +interactionId +&quot; ]: Request URL: &quot; + requestedPath);
channelMap.put(&quot;requestUri&quot;, requestedPath);
if (requestedPath == &quot;/&quot;) {&#xd;
	return;&#xd;
}
if (requestedPath == &quot;/Bundle/$validate/&quot; || requestedPath == &quot;/Bundle/$validate&quot;) {
var fhirJson  = connectorMessage.getRawData();
channelMap.put(&apos;fhirJson&apos;, fhirJson);

var interactionId = channelMap.get(&apos;interactionId&apos;);
var validationSeverityLevel = channelMap.get(&apos;validationSeverityLevel&apos;);
var source = channelMap.get(&apos;source&apos;);
var igVersion = channelMap.get(&apos;igVersion&apos;);
var elaboration = channelMap.get(&apos;elaboration&apos;);
&#xd;


logger.info(&quot;Channel Name: - &quot; + channelName);
logger.info(&quot;[CHANNEL: 1115 TECHBD &quot; + channelName + &quot; ][INTERACTION ID: &quot; +interactionId +&quot; ]:Received message: &quot; + connectorMessage.getRawData());



channelMap.put(&apos;interactionId&apos;, interactionId);&#xd;
channelMap.put(&quot;validationSeverityLevel&quot;,validationSeverityLevel);
//channelMap.put(&quot;fhirSubmissionApiUrl&quot;,fhirSubmissionApiUrl);
channelMap.put(&apos;source&apos;,source);
channelMap.put(&apos;igVersion&apos;, igVersion);
channelMap.put(&apos;elaboration&apos;, elaboration);

//logger.info(&quot;[CHANNEL: 1115 TECHBD &quot; + channelName + &quot;][INTERACTION ID: &quot; +interactionId +&quot; ]: fhirSubmissionApiUrl: &quot; + fhirSubmissionApiUrl);

//logger.info(&quot;[CHANNEL: 1115 TECHBD &quot; + channelName + &quot;][INTERACTION ID: &quot; +interactionId +&quot; ]: igVersion: &quot; + igVersion);


var tenantId = $(&apos;headers&apos;).getHeader(&apos;X-TechBD-Tenant-ID&apos;);
logger.info(&quot;[CHANNEL: 1115 TECHBD &quot; + channelName + &quot; ][INTERACTION ID: &quot; +interactionId +&quot; ]: tenantid: &quot; + tenantId); 
channelMap.put(&apos;tenantId&apos;, tenantId);

&#xd;
var userAgent = $(&apos;headers&apos;).getHeader(&apos;User-Agent&apos;);&#xd;
channelMap.put(&apos;userAgent&apos;, userAgent);



channelMap.put(&quot;endpoint&quot;, &quot;validate&quot;);
responseMap.put(&apos;status&apos;, &apos;200&apos;); // Set HTTP status 200

}</script>
        </com.mirth.connect.plugins.javascriptstep.JavaScriptStep>
      </elements>
      <inboundTemplate encoding="base64"></inboundTemplate>
      <outboundTemplate encoding="base64"></outboundTemplate>
      <inboundDataType>RAW</inboundDataType>
      <outboundDataType>RAW</outboundDataType>
      <inboundProperties class="com.mirth.connect.plugins.datatypes.raw.RawDataTypeProperties" version="4.6.0">
        <batchProperties class="com.mirth.connect.plugins.datatypes.raw.RawBatchProperties" version="4.6.0">
          <splitType>JavaScript</splitType>
          <batchScript></batchScript>
        </batchProperties>
      </inboundProperties>
      <outboundProperties class="com.mirth.connect.plugins.datatypes.raw.RawDataTypeProperties" version="4.6.0">
        <batchProperties class="com.mirth.connect.plugins.datatypes.raw.RawBatchProperties" version="4.6.0">
          <splitType>JavaScript</splitType>
          <batchScript></batchScript>
        </batchProperties>
      </outboundProperties>
    </transformer>
    <filter version="4.6.0">
      <elements>
        <com.mirth.connect.plugins.rulebuilder.RuleBuilderRule version="4.6.0">
          <name>Accept message if &quot;sourceMap.get(&apos;contextPath&apos;)&quot; equals &apos;/Bundle/$validate/&apos; or &apos;/Bundle/$validate&apos;</name>
          <sequenceNumber>0</sequenceNumber>
          <enabled>true</enabled>
          <field>sourceMap.get(&apos;contextPath&apos;)</field>
          <condition>EQUALS</condition>
          <values>
            <string>&apos;/Bundle/$validate/&apos;</string>
            <string>&apos;/Bundle/$validate&apos;</string>
          </values>
        </com.mirth.connect.plugins.rulebuilder.RuleBuilderRule>
        <com.mirth.connect.plugins.rulebuilder.RuleBuilderRule version="4.6.0">
          <name>Accept message if &quot;sourceMap.get(&apos;method&apos;)&quot; equals &apos;POST&apos;</name>
          <sequenceNumber>1</sequenceNumber>
          <enabled>true</enabled>
          <operator>AND</operator>
          <field>sourceMap.get(&apos;method&apos;)</field>
          <condition>EQUALS</condition>
          <values>
            <string>&apos;POST&apos;</string>
          </values>
        </com.mirth.connect.plugins.rulebuilder.RuleBuilderRule>
      </elements>
    </filter>
    <transportName>HTTP Listener</transportName>
    <mode>SOURCE</mode>
    <enabled>true</enabled>
    <waitForPrevious>true</waitForPrevious>
  </sourceConnector>
  <destinationConnectors>
    <connector version="4.6.0">
      <metaDataId>1</metaDataId>
      <name>dest_bundle</name>
      <properties class="com.mirth.connect.connectors.http.HttpDispatcherProperties" version="4.6.0">
        <pluginProperties>
          <com.mirth.connect.plugins.ssl.SSLSettingsProperties version="4.6.0">
  <sslEnabled>false</sslEnabled>
            <mutualTlsEnabled>false</mutualTlsEnabled>
            <verifyHostname>false</verifyHostname>
            <keystorePath/>
            <keystorePassword/>
            <certAlias/>
            <certPassword/>
            <truststorePath/>
            <truststorePassword/>
            <tls13>true</tls13>
            <tls12>true</tls12>
            <tls11>true</tls11>
            <keystoreType/>
            <truststoreType/>
            <keystoreSettingFromSystem>false</keystoreSettingFromSystem>
            <keystoreUid/>
            <myCertificateAlias/>
            <truststoreSettingFromSystem>false</truststoreSettingFromSystem>
            <truststoreUid/>
          </com.mirth.connect.plugins.ssl.SSLSettingsProperties>
        </pluginProperties>
        <destinationConnectorProperties version="4.6.0">
          <queueEnabled>false</queueEnabled>
          <sendFirst>false</sendFirst>
          <retryIntervalMillis>10000</retryIntervalMillis>
          <regenerateTemplate>false</regenerateTemplate>
          <retryCount>0</retryCount>
          <rotate>false</rotate>
          <includeFilterTransformer>false</includeFilterTransformer>
          <threadCount>1</threadCount>
          <threadAssignmentVariable></threadAssignmentVariable>
          <validateResponse>false</validateResponse>
          <resourceIds class="linked-hash-map">
            <entry>
              <string>Default Resource</string>
              <string>[Default Resource]</string>
            </entry>
          </resourceIds>
          <queueBufferSize>1000</queueBufferSize>
          <reattachAttachments>true</reattachAttachments>
        </destinationConnectorProperties>
        <host>${FHIR_API_URL}</host>
        <useProxyServer>false</useProxyServer>
        <proxyAddress></proxyAddress>
        <proxyPort></proxyPort>
        <method>post</method>
        <headers class="linked-hash-map">
          <entry>
            <string>Access-Control-Allow-Origin</string>
            <list>
              <string>${HUB_UI_URL}</string>
            </list>
          </entry>
          <entry>
            <string>Access-Control-Allow-Methods</string>
            <list>
              <string>GET, POST, OPTIONS</string>
            </list>
          </entry>
          <entry>
            <string>Access-Control-Allow-Headers</string>
            <list>
              <string>Content-Type, Authorization, X-TechBD-Base-FHIR-URL, X-TechBD-Tenant-ID, User-Agent, X-TechBD-REMOTE-IP, X-TechBD-Override-Request-URI, X-Correlation-ID, accept, X-TechBD-DataLake-API-URL, DataLake-API-Content-Type, X-TechBD-HealthCheck, X-TechBD-Validation-Severity-Level, X-SHIN-NY-IG-Version</string>
            </list>
          </entry>
          <entry>
            <string>Access-Control-Expose-Headers</string>
            <list>
              <string>Location, X-TechBD-Tenant-ID, User-Agent, X-TechBD-REMOTE-IP, X-TechBD-Override-Request-URI,X-Correlation-ID,X-TechBD-HealthCheck</string>
            </list>
          </entry>
          <entry>
            <string>X-TechBD-Tenant-ID</string>
            <list>
              <string>${tenantId}</string>
            </list>
          </entry>
          <entry>
            <string>Content-Type</string>
            <list>
              <string>application/fhir+json</string>
            </list>
          </entry>
          <entry>
            <string>User-Agent</string>
            <list>
              <string>${userAgent}</string>
            </list>
          </entry>
          <entry>
            <string>Access-Control-Allow-Credentials</string>
            <list>
              <string>true</string>
            </list>
          </entry>
          <entry>
            <string>X-TechBD-Interaction-ID</string>
            <list>
              <string>${interactionId}</string>
            </list>
          </entry>
          <entry>
            <string>X-TechBD-Request-URI</string>
            <list>
              <string>${requestUri}</string>
            </list>
          </entry>
          <entry>
            <string>X-TechBD-Validation-Severity-Level</string>
            <list>
              <string>${validationSeverityLevel}</string>
            </list>
          </entry>
          <entry>
            <string>X-SHIN-NY-IG-Version</string>
            <list>
              <string>${igVersion}</string>
            </list>
          </entry>
          <entry>
            <string>X-TechBD-Override-Request-URI</string>
            <list>
              <string>${requestUri}</string>
            </list>
          </entry>
          <entry>
            <string>X-TechBD-Source-Type</string>
            <list>
              <string>${source}</string>
            </list>
          </entry>
          <entry>
            <string>X-TechBD-Elaboration</string>
            <list>
              <string>${elaboration}</string>
            </list>
          </entry>
        </headers>
        <parameters class="linked-hash-map">
          <entry>
            <string>source</string>
            <list>
              <string>${source}</string>
            </list>
          </entry>
        </parameters>
        <useHeadersVariable>false</useHeadersVariable>
        <headersVariable></headersVariable>
        <useParametersVariable>false</useParametersVariable>
        <parametersVariable></parametersVariable>
        <responseXmlBody>false</responseXmlBody>
        <responseParseMultipart>true</responseParseMultipart>
        <responseIncludeMetadata>false</responseIncludeMetadata>
        <responseBinaryMimeTypes>application/fhir+json</responseBinaryMimeTypes>
        <responseBinaryMimeTypesRegex>true</responseBinaryMimeTypesRegex>
        <multipart>false</multipart>
        <useAuthentication>false</useAuthentication>
        <authenticationType>Basic</authenticationType>
        <usePreemptiveAuthentication>false</usePreemptiveAuthentication>
        <username></username>
        <password></password>
        <content>${fhirJson}</content>
        <contentType>application/fhir+json</contentType>
        <dataTypeBinary>false</dataTypeBinary>
        <charset>UTF-8</charset>
        <socketTimeout>30000</socketTimeout>
      </properties>
      <transformer version="4.6.0">
        <elements/>
        <inboundTemplate encoding="base64"></inboundTemplate>
        <outboundTemplate encoding="base64"></outboundTemplate>
        <inboundDataType>RAW</inboundDataType>
        <outboundDataType>RAW</outboundDataType>
        <inboundProperties class="com.mirth.connect.plugins.datatypes.raw.RawDataTypeProperties" version="4.6.0">
          <batchProperties class="com.mirth.connect.plugins.datatypes.raw.RawBatchProperties" version="4.6.0">
            <splitType>JavaScript</splitType>
            <batchScript></batchScript>
          </batchProperties>
        </inboundProperties>
        <outboundProperties class="com.mirth.connect.plugins.datatypes.raw.RawDataTypeProperties" version="4.6.0">
          <batchProperties class="com.mirth.connect.plugins.datatypes.raw.RawBatchProperties" version="4.6.0">
            <splitType>JavaScript</splitType>
            <batchScript></batchScript>
          </batchProperties>
        </outboundProperties>
      </transformer>
      <responseTransformer version="4.6.0">
        <elements/>
        <inboundDataType>RAW</inboundDataType>
        <outboundDataType>RAW</outboundDataType>
        <inboundProperties class="com.mirth.connect.plugins.datatypes.raw.RawDataTypeProperties" version="4.6.0">
          <batchProperties class="com.mirth.connect.plugins.datatypes.raw.RawBatchProperties" version="4.6.0">
            <splitType>JavaScript</splitType>
            <batchScript></batchScript>
          </batchProperties>
        </inboundProperties>
        <outboundProperties class="com.mirth.connect.plugins.datatypes.raw.RawDataTypeProperties" version="4.6.0">
          <batchProperties class="com.mirth.connect.plugins.datatypes.raw.RawBatchProperties" version="4.6.0">
            <splitType>JavaScript</splitType>
            <batchScript></batchScript>
          </batchProperties>
        </outboundProperties>
      </responseTransformer>
      <filter version="4.6.0">
        <elements/>
      </filter>
      <transportName>HTTP Sender</transportName>
      <mode>DESTINATION</mode>
      <enabled>true</enabled>
      <waitForPrevious>true</waitForPrevious>
    </connector>
  </destinationConnectors>
  <preprocessingScript>// Modify the message variable below to pre process data
//logInfo(&quot;Validate Header and Request Parameters BEGIN in channel preprocessor : &quot;,channelMap);
//var tenantId = $(&apos;headers&apos;).getHeader(&apos;X-TechBD-Tenant-ID&apos;);
//if (validate(&quot;X-TechBD-Tenant-ID&quot;, tenantId, &quot;isRequired&quot;, responseMap, 400)) {
//    return;
//}
//TODO - check and add other parameters /header validation for /Bundle
//logInfo(&quot;Validate Header and Request Parameters END  in channel preprocessor : &quot;,channelMap);

///////////////////////////////////////////////////////////////////////////
// Access the required header values using getHeader method

var requestedPath = sourceMap.get(&apos;contextPath&apos;);

// Read both headers
var overridePath = $(&apos;headers&apos;).getHeader(&apos;X-TechBD-Override-Request-URI&apos;);
var requestUri = $(&apos;headers&apos;).getHeader(&apos;X-TechBD-Request-URI&apos;);

// Use whichever is present and non-empty (first check override, otherwise requestUri)
var effectivePath = (overridePath &amp;&amp; overridePath.trim() !== &apos;&apos;) 
    ? overridePath.trim() 
    : (requestUri &amp;&amp; requestUri.trim() !== &apos;&apos;) 
        ? requestUri.trim() 
        : null;

// If we found a usable override path, apply it
if (effectivePath !== null) {
    requestedPath = effectivePath;
}

channelMap.put(&quot;requestedPath&quot;, requestedPath);


if (requestedPath == &quot;/&quot;) {&#xd;
	return;&#xd;
}


// ---- REQUEST BODY VALIDATION ----

if (message == null || String(message).trim() === &quot;&quot;) {
    var errorMessage = &quot;Validation Error: Required request body is missing&quot;;
    logger.error(errorMessage);
    setErrorResponsePre(400, errorMessage);
    throw errorMessage;
}

channelMap.put(&apos;channelName&apos;,channelName);

var incomingInteractionId = $(&apos;headers&apos;).getHeader(&apos;X-TechBD-Interaction-ID&apos;);
var incomingCorrelationId = $(&apos;headers&apos;).getHeader(&apos;X-Correlation-ID&apos;);

var interactionId;

if (incomingInteractionId != null &amp;&amp; incomingInteractionId.trim() !== &apos;&apos;) {
    interactionId = incomingInteractionId;
} else if (incomingCorrelationId != null &amp;&amp; incomingCorrelationId.trim() !== &apos;&apos;) {
    interactionId = incomingCorrelationId;
} else {
    interactionId = java.util.UUID.randomUUID().toString();
}

channelMap.put(&apos;interactionId&apos;, interactionId);

var source = &quot;FHIR&quot;;
var incomingSource = sourceMap.get(&apos;source&apos;);

if (incomingSource != null &amp;&amp; incomingSource.trim() !== &apos;&apos;) {
    source = incomingSource;
}

channelMap.put(&apos;source&apos;, source);
logger.info(&quot;Source: &quot; + source);





var tenantId = $(&apos;headers&apos;).getHeader(&apos;X-TechBD-Tenant-ID&apos;);
logger.info(&quot;[CHANNEL: 1115 TECHBD &quot; + channelName + &quot; ][INTERACTION ID: &quot; +interactionId +&quot; ]: tenantid: &quot; + tenantId); // Mirth helper that accepts channelMap
channelMap.put(&apos;tenantId&apos;, tenantId);

&#xd;
&#xd;
var userAgent = $(&apos;headers&apos;).getHeader(&apos;User-Agent&apos;);&#xd;
channelMap.put(&apos;userAgent&apos;, userAgent);



var elaboration = &quot;&quot;;
var incomingElaboration = $(&apos;headers&apos;).getHeader(&apos;X-TechBD-Elaboration&apos;);

// If header has valid value, use it; otherwise keep default (empty string or any fallback)
if (incomingElaboration != null &amp;&amp; incomingElaboration.trim() !== &apos;&apos;) {
    elaboration = incomingElaboration.trim();
}

channelMap.put(&quot;elaboration&quot;, elaboration);



var igVersion = &quot;&quot;;
var incomingIgVersion = $(&apos;headers&apos;).getHeader(&apos;X-SHIN-NY-IG-Version&apos;);

if (incomingIgVersion != null &amp;&amp; incomingIgVersion.trim() !== &apos;&apos;) {
    igVersion = incomingIgVersion;
}

channelMap.put(&apos;igVersion&apos;, igVersion);






var validationSeverityLevel = &quot;error&quot;;&#xd;
var incomingSeverity = $(&apos;headers&apos;).getHeader(&apos;X-TechBD-Validation-Severity-Level&apos;);&#xd;
&#xd;
// If header has valid value, use it; otherwise keep default&#xd;
if (incomingSeverity != null &amp;&amp; incomingSeverity.trim() !== &apos;&apos;) {&#xd;
    validationSeverityLevel = incomingSeverity;&#xd;
}&#xd;
&#xd;
channelMap.put(&quot;validationSeverityLevel&quot;,validationSeverityLevel);

// Check if the header is null or empty
if (tenantId == null || String(tenantId).trim() === &quot;&quot;) {
    // Log the error for debugging
    var errorMessage = &apos;Bad Request: Missing required header X-TechBD-Tenant-ID&apos;;
    logger.error(errorMessage);
    setErrorResponsePre(400, errorMessage); // Set the HTTP response status to 400 (Bad Request)
    throw errorMessage; // Stop further processing by throwing an exception
}

function setErrorResponsePre(statusCode, errorMessage) {
    responseMap.put(&apos;status&apos;, String(statusCode)); // Set HTTP status
    responseMap.put(&apos;message&apos;, errorMessage);
    responseMap.put(&apos;finalResponse&apos;, createJsonResponsePre(statusCode, errorMessage));
}
function createJsonResponsePre(status, message) {
    return JSON.stringify({ status: status, message: message });
}

return message;</preprocessingScript>
  <postprocessingScript>var chlName = channelMap.get(&apos;channelName&apos;);
var interactionId = channelMap.get(&apos;interactionId&apos;);

var requestedPath = channelMap.get(&apos;requestedPath&apos;);
if (requestedPath == &quot;/&quot;) {&#xd;
	return;&#xd;
}


logger.info(&quot;[CHANNEL: 1115 TECHBD &quot; + chlName + &quot;][INTERACTION ID: &quot; +interactionId +&quot; ]: Postprocessor STARTS.&quot;);
var endpoint = channelMap.get(&quot;endpoint&quot;);
if(endpoint == &apos;validate&apos;) {&#xd;
	// Get the response from the HTTP Writer destination&#xd;
	var destinationName = &quot;dest_bundle&quot;;&#xd;
	var destinationResponse = responseMap.get(destinationName);&#xd;
&#xd;
	// Check if the response exists&#xd;
	if (destinationResponse) {&#xd;
	    var responseStatus = destinationResponse.getStatus();  // HTTP status code&#xd;
	    var responseData = destinationResponse.getMessage();   // Response message body&#xd;
		responseMap.put(&apos;finalResponse&apos;, responseData);&#xd;
		&#xd;
	    // Log the response details&#xd;
	    logger.info(&quot;[CHANNEL: 1115 TECHBD &quot; + chlName + &quot;][INTERACTION ID: &quot; +interactionId +&quot; ]:Response from &quot; + destinationName + &quot;:&quot;);&#xd;
	    logger.info(&quot;[CHANNEL: 1115 TECHBD &quot; + chlName + &quot;][INTERACTION ID: &quot; +interactionId +&quot; ]:Status Code: &quot; + responseStatus);&#xd;
	    logger.info(&quot;[CHANNEL: 1115 TECHBD &quot; + chlName + &quot;][INTERACTION ID: &quot; +interactionId +&quot; ]:Response Data: &quot; + responseData);&#xd;
	} else {&#xd;
	    logger.info(&quot;[CHANNEL: 1115 TECHBD &quot; + chlName + &quot;][INTERACTION ID: &quot; +interactionId +&quot; ]:No response found for destination: &quot; + destinationName);&#xd;
	}&#xd;
}&#xd;
logger.info(&quot;[CHANNEL: 1115 TECHBD &quot; + chlName + &quot;][INTERACTION ID: &quot; +interactionId +&quot; ]: Postprocessor ENDS.&quot;);





return;
</postprocessingScript>
  <deployScript>// This script executes once when the channel is deployed
// You only have access to the globalMap and globalChannelMap here to persist data

/**
* Util function to generate json string wit hstatus and message
*/

function createJsonResponse(status, message) {
    return JSON.stringify({ status: status, message: message });
}

/**
* Util function to set error response.
*/
function setErrorResponse(statusCode, errorMessage) {
    responseMap.put(&apos;status&apos;, String(statusCode)); // Set HTTP status
    responseMap.put(&apos;message&apos;, errorMessage);
    responseMap.put(&apos;finalResponse&apos;, createJsonResponse(statusCode, errorMessage));
}


&#xd;
&#xd;
function toJsonb(value) {&#xd;
    var jsonbObj = new PGobject();&#xd;
    jsonbObj.setType(&quot;jsonb&quot;);&#xd;
&#xd;
	if (typeof value === &quot;object&quot;) {&#xd;
        value = JSON.stringify(value);&#xd;
    }&#xd;
    &#xd;
    jsonbObj.setValue(value);&#xd;
    logger.info(&quot;jsonbObj: &quot; + jsonbObj);&#xd;
    return jsonbObj;&#xd;
}

// Function to extract a query param from the URI&#xd;
function getQueryParam(uriString, paramName) {&#xd;
    if (!uriString) return null;&#xd;
    var regex = new RegExp(&apos;[?&amp;]&apos; + paramName + &apos;=([^&amp;]*)&apos;, &apos;i&apos;);&#xd;
    var match = uriString.match(regex);&#xd;
    return (match &amp;&amp; match[1]) ? decodeURIComponent(match[1]) : null;&#xd;
}
globalMap.put(&apos;getQueryParam&apos;, getQueryParam);


return;</deployScript>
  <undeployScript>// This script executes once when the channel is undeployed
// You only have access to the globalMap and globalChannelMap here to persist data
return;</undeployScript>
  <properties version="4.6.0">
    <clearGlobalChannelMap>true</clearGlobalChannelMap>
    <messageStorageMode>DEVELOPMENT</messageStorageMode>
    <encryptData>false</encryptData>
    <encryptAttachments>false</encryptAttachments>
    <encryptCustomMetaData>false</encryptCustomMetaData>
    <removeContentOnCompletion>false</removeContentOnCompletion>
    <removeOnlyFilteredOnCompletion>false</removeOnlyFilteredOnCompletion>
    <removeAttachmentsOnCompletion>false</removeAttachmentsOnCompletion>
    <initialState>STARTED</initialState>
    <storeAttachments>true</storeAttachments>
    <metaDataColumns>
      <metaDataColumn>
        <name>SOURCE</name>
        <type>STRING</type>
        <mappingName>message_source</mappingName>
      </metaDataColumn>
      <metaDataColumn>
        <name>TYPE</name>
        <type>STRING</type>
        <mappingName>message_type</mappingName>
      </metaDataColumn>
    </metaDataColumns>
    <attachmentProperties version="4.6.0">
      <type>None</type>
      <properties/>
    </attachmentProperties>
    <resourceIds class="linked-hash-map">
      <entry>
        <string>Default Resource</string>
        <string>[Default Resource]</string>
      </entry>
    </resourceIds>
  </properties>
  <exportData>
    <metadata>
      <enabled>true</enabled>
      <lastModified>
        <time>1769594070423</time>
        <timezone>Asia/Calcutta</timezone>
      </lastModified>
      <pruningSettings>
        <archiveEnabled>true</archiveEnabled>
        <pruneErroredMessages>false</pruneErroredMessages>
      </pruningSettings>
      <userId>1</userId>
    </metadata>
    <channelTags>
      <channelTag>
        <id>bd25c131-0dc1-47a4-b01c-27ee5d23b8f2</id>
        <name>0_8_4</name>
        <channelIds>
          <string>d97bd741-575a-4933-b6cb-4a4075689895</string>
          <string>15ac7d8c-f134-4593-8fcd-8c5929c8ac88</string>
          <string>3f3f6dcd-2923-4f09-89c3-1e248c1556b3</string>
          <string>eb7b9426-2620-4037-aeec-eb45106175b2</string>
        </channelIds>
        <backgroundColor>
          <red>255</red>
          <green>0</green>
          <blue>0</blue>
          <alpha>255</alpha>
        </backgroundColor>
      </channelTag>
      <channelTag>
        <id>9ec6ef81-2762-421a-93b5-8f5b57c7ea6a</id>
        <name>Active</name>
        <channelIds>
          <string>1ca854d7-cebf-4897-a9ac-2a01e10ba533</string>
          <string>ec9c3233-54f3-4995-b808-7592e512037b</string>
          <string>6387f0ca-5310-4994-b9f6-e2e1dc233245</string>
          <string>228791e0-c8ac-4d16-ba51-263252f4e6b2</string>
          <string>07de8921-59c3-4539-8d69-b3c241b33d3d</string>
          <string>ca5d292a-45ee-476b-b0ee-a46df2320417</string>
          <string>ca53db04-3ca7-4d35-8418-a9f2c92ae6bf</string>
          <string>3f3f6dcd-2923-4f09-89c3-1e248c1556b3</string>
          <string>3393ac1b-6c87-4c48-a765-240c13a09dc6</string>
          <string>ca7a435f-dc52-4170-a100-73cdede86f0b</string>
          <string>467f826d-1a1b-4e15-84b6-6c4c71968059</string>
          <string>957444f7-b1ca-40a4-9b3d-34b56aec2d36</string>
          <string>8ac69fa1-9441-43b7-9ad9-cef544c9384e</string>
          <string>95a4b010-c209-4904-8500-433538921ae5</string>
          <string>0ab3d3e9-f19d-46d5-9614-46106b435cb3</string>
          <string>af1a4679-daa3-4f5b-bc4a-dc17e633188b</string>
          <string>b5ce54b4-54a4-4173-9258-fcce5d81a231</string>
          <string>a2fdd3b6-8a2e-43cb-834b-f770f4b57d06</string>
          <string>b3aed8a2-8417-4575-93fb-3d6bfca955b5</string>
          <string>2b544207-bac7-4e49-a67d-bfe55e676c61</string>
          <string>49b0d71c-1ca9-4ac3-ac4e-dc945370f184</string>
          <string>5b642c6d-9ce3-4cc6-8f09-fc4914437ec9</string>
          <string>fe70827b-3f88-4a81-bd75-fe95792e5de6</string>
          <string>8f7b1a24-eb1e-436b-b147-4885b9c8a8ad</string>
          <string>cb060b55-d21f-428f-9708-bb2962687ae2</string>
          <string>a9adff1e-d18b-408d-a6b8-dab8fad509ff</string>
          <string>595f2397-f04b-483b-84e3-2a7bc8a52384</string>
          <string>873588e2-8903-4e9c-b8f3-c44889f22392</string>
          <string>56c509d8-fc3a-42e5-a1b5-d04ea4d4ef61</string>
          <string>9e22ec82-73ee-4565-8c5e-60a51e941cca</string>
          <string>1bc85cd3-7c82-4ae1-b493-d8cf401072bf</string>
          <string>785baac8-14b8-4964-8992-572a90d357dc</string>
          <string>6f63073f-c59d-4a40-87cd-7b4fc317d3f5</string>
          <string>5a3de04c-8bb9-4aa6-9342-d39231de25e2</string>
          <string>9408e60f-8bb1-440e-9d69-1bbba9d59212</string>
          <string>2d04773b-df86-4429-a80d-9fbd07d179a0</string>
        </channelIds>
        <backgroundColor>
          <red>0</red>
          <green>255</green>
          <blue>0</blue>
          <alpha>255</alpha>
        </backgroundColor>
      </channelTag>
    </channelTags>
  </exportData>
</channel>