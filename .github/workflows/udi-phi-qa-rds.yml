name:  PHI-QA UDI schema generator

on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'Polyglot Prime Tag Version'
        required: true
jobs:
  schema-generation:
    runs-on: qa-techbd-sql
    env:
      SQL_AIDE_TAG: v0.14.9
    steps:
      - name: Running schema generation
        run: echo "Running schema generation"
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Create .pgpass file
        run: |
          # Write the metadata comment to the .pgpass file
          echo "# { id: \"QA_TECHBD_UDI_DS\", description: \"UDI QA non-PHI database \", boundary: \"non-PHI QA\" }" > ~/.pgpass

          # Append the database connection details to the .pgpass file
          echo "${{ secrets.PHI_QA_TECHBD_UDI_DS_HOST }}:${{ secrets.PHI_QA_TECHBD_UDI_DS_PORT }}:${{ secrets.PHI_QA_TECHBD_UDI_DS_NAME }}:${{ secrets.PHI_QA_TECHBD_UDI_DS_USER }}:${{ secrets.PHI_QA_TECHBD_UDI_DS_PASSWORD }}" >> ~/.pgpass

          # Set the .pgpass file permissions to 600 (read and write for owner only)
          chmod 600 ~/.pgpass

      - name: Clone SQL Aide repository
        run: |
          git clone --branch ${{ env.SQL_AIDE_TAG }} https://github.com/netspective-labs/sql-aide.git

      - name: Navigate to SQL Aide and Generate ISLM SQL
        run: |
          deno add npm:json5
          cd sql-aide/lib/postgres/islm
          chmod +x islmctl.ts
          ./islmctl.ts evolve up --conn-id QA_TECHBD_UDI_DS

      - name: Run ISLM test
        run: |
          cd sql-aide/lib/postgres/islm
          ./islmctl.ts evolve test --conn-id QA_TECHBD_UDI_DS

      - name: Display ISLM Test log
        run: |
          cd sql-aide/lib/postgres/islm
          log_file=$(ls -t ./islmctl-test-*.log | head -n 1)
          cat "$log_file"

      - name: Clone Polyglot Prime repository
        run: git clone -b ${{ inputs.tag }} https://github.com/tech-by-design/polyglot-prime.git

      - name: Navigate to UDI Prime and Generate SQL
        run: |
          cd polyglot-prime/udi-prime
          ./udictl.ts ic generate sql

      - name: Load SQL into database
        run: |
          cd polyglot-prime/udi-prime
          ./udictl.ts ic load-sql --conn-id QA_TECHBD_UDI_DS

      - name: Display Load SQL log
        run: |
          cd polyglot-prime/udi-prime
          log_file=$(ls -t ./udictl-load-sql-*.log | head -n 1)
          cat "$log_file"

      - name: Run UDI test
        run: |
          cd polyglot-prime/udi-prime
          ./udictl.ts ic test --conn-id QA_TECHBD_UDI_DS

      - name: Run UDI Migrations
        run: |
          cd polyglot-prime/udi-prime
          ./udictl.ts ic migrate --conn-id QA_TECHBD_UDI_DS --is-linted false

      - name: Grant readonly permissions on schemas
        run: |
          psql \
            -h ${{ secrets.PHI_QA_TECHBD_UDI_DS_HOST }} \
            -p ${{ secrets.PHI_QA_TECHBD_UDI_DS_PORT }} \
            -d ${{ secrets.PHI_QA_TECHBD_UDI_DS_NAME  }} \
            -U ${{ secrets.PHI_QA_TECHBD_UDI_DS_USER  }} \
            <<'SQL'
          GRANT USAGE ON SCHEMA techbd_udi_ingress TO ${{secrets.PHI_QA_TECHBD_UDI_DS_READER_JDBC_USERNAME}};
          GRANT SELECT ON ALL TABLES IN SCHEMA techbd_udi_ingress TO ${{secrets.PHI_QA_TECHBD_UDI_DS_READER_JDBC_USERNAME}};

          GRANT USAGE ON SCHEMA info_schema_lifecycle TO ${{secrets.PHI_QA_TECHBD_UDI_DS_READER_JDBC_USERNAME}};
          GRANT SELECT ON ALL TABLES IN SCHEMA info_schema_lifecycle TO ${{secrets.PHI_QA_TECHBD_UDI_DS_READER_JDBC_USERNAME}};

          ALTER DEFAULT PRIVILEGES IN SCHEMA techbd_udi_ingress
          GRANT SELECT ON TABLES TO ${{secrets.PHI_QA_TECHBD_UDI_DS_READER_JDBC_USERNAME}};

          ALTER DEFAULT PRIVILEGES IN SCHEMA info_schema_lifecycle
          GRANT SELECT ON TABLES TO ${{secrets.PHI_QA_TECHBD_UDI_DS_READER_JDBC_USERNAME}};
          SQL

      - name: Display UDI Test log
        run: |
          cd polyglot-prime/udi-prime
          log_file=$(ls -t ./udictl-test-*.log | head -n 1)
          cat "$log_file"
